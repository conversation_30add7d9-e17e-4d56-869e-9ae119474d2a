import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Modal, Form, Input, Select, Space, Tag, Popconfirm, message, Tabs, Row, Col } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, LinkOutlined } from '@ant-design/icons';
import { useEntityManagement } from '../../../hooks/developer/useEntityManagement';
import { Entity, EntityField, EntityRelationship } from '../../../types/developer/entity';

const { Option } = Select;
const { TabPane } = Tabs;

interface EntityModelingStepProps {
  onNext: () => void;
  onPrev: () => void;
  scenarioId?: string;
}

const EntityModelingStep: React.FC<EntityModelingStepProps> = ({
  onNext,
  onPrev,
  scenarioId
}) => {
  const {
    entities,
    loading,
    createEntity,
    updateEntity,
    deleteEntity,
    getEntityDetail,
    createRelationship,
    getRelationships,
    refreshEntities
  } = useEntityManagement();

  const [activeTab, setActiveTab] = useState('entities');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null);
  const [form] = Form.useForm();

  // 实体表格列定义
  const entityColumns = [
    {
      title: '实体名称',
      dataIndex: 'displayName',
      key: 'displayName',
      render: (text: string, record: Entity) => (
        <Space>
          <span style={{ fontWeight: 600 }}>{text}</span>
          <Tag color="blue">{record.name}</Tag>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '字段数量',
      dataIndex: 'fields',
      key: 'fieldCount',
      render: (fields: EntityField[]) => (
        <Tag color="green">{fields?.length || 0} 个字段</Tag>
      ),
    },
    {
      title: '关系数量',
      dataIndex: 'relationships',
      key: 'relationshipCount',
      render: (relationships: EntityRelationship[]) => (
        <Tag color="orange">{relationships?.length || 0} 个关系</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: Entity) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewEntity(record)}
            title="查看"
          />
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditEntity(record)}
            title="编辑"
          />
          <Button
            type="link"
            size="small"
            icon={<LinkOutlined />}
            onClick={() => handleManageRelationships(record)}
            title="关系"
          />
          <Popconfirm
            title="确定要删除这个实体吗？"
            onConfirm={() => handleDeleteEntity(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              title="删除"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 加载实体列表
  useEffect(() => {
    refreshEntities(scenarioId);
  }, [scenarioId, refreshEntities]);

  // 处理创建实体
  const handleCreateEntity = () => {
    setModalType('create');
    setSelectedEntity(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑实体
  const handleEditEntity = (entity: Entity) => {
    setModalType('edit');
    setSelectedEntity(entity);
    form.setFieldsValue({
      displayName: entity.displayName,
      description: entity.description,
      icon: entity.icon,
    });
    setIsModalVisible(true);
  };

  // 处理查看实体
  const handleViewEntity = async (entity: Entity) => {
    try {
      const detail = await getEntityDetail(entity.id);
      setModalType('view');
      setSelectedEntity(detail);
      form.setFieldsValue({
        displayName: detail.displayName,
        description: detail.description,
        icon: detail.icon,
      });
      setIsModalVisible(true);
    } catch (error) {
      message.error('获取实体详情失败');
    }
  };

  // 处理删除实体
  const handleDeleteEntity = async (entityId: string) => {
    try {
      await deleteEntity(entityId);
      message.success('实体删除成功');
      refreshEntities(scenarioId);
    } catch (error) {
      message.error('删除实体失败');
    }
  };

  // 处理管理关系
  const handleManageRelationships = (entity: Entity) => {
    setSelectedEntity(entity);
    setActiveTab('relationships');
  };

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (modalType === 'create') {
        await createEntity({
          name: values.displayName.toLowerCase().replace(/\s+/g, '_'),
          displayName: values.displayName,
          description: values.description,
          icon: values.icon,
          scenarioId: scenarioId,
          fields: [], // 初始创建时没有字段，后续可以添加
        });
        message.success('实体创建成功');
      } else if (modalType === 'edit' && selectedEntity) {
        await updateEntity(selectedEntity.id, {
          displayName: values.displayName,
          description: values.description,
          icon: values.icon,
        });
        message.success('实体更新成功');
      }
      
      setIsModalVisible(false);
      refreshEntities(scenarioId);
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <div className="entity-modeling-step">
      <div className="step-header">
        <h2 className="page-title">实体建模</h2>
        <p className="page-subtitle">
          定义业务实体的结构和关系，构建完整的数据模型
        </p>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        tabBarExtraContent={
          activeTab === 'entities' ? (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateEntity}
            >
              创建实体
            </Button>
          ) : null
        }
      >
        <TabPane tab="实体管理" key="entities">
          <Card>
            <Table
              columns={entityColumns}
              dataSource={entities}
              loading={loading}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个实体`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="关系管理" key="relationships">
          <Card>
            <p>关系管理功能开发中...</p>
          </Card>
        </TabPane>
      </Tabs>

      {/* 实体创建/编辑模态框 */}
      <Modal
        title={
          modalType === 'create' ? '创建实体' :
          modalType === 'edit' ? '编辑实体' : '查看实体'
        }
        open={isModalVisible}
        onOk={modalType === 'view' ? handleModalCancel : handleModalOk}
        onCancel={handleModalCancel}
        okText={modalType === 'view' ? '关闭' : '确定'}
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          disabled={modalType === 'view'}
        >
          <Form.Item
            label="实体显示名称"
            name="displayName"
            rules={[{ required: true, message: '请输入实体显示名称' }]}
          >
            <Input placeholder="请输入实体显示名称" />
          </Form.Item>

          <Form.Item
            label="实体描述"
            name="description"
            rules={[{ required: true, message: '请输入实体描述' }]}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="请输入实体描述" 
            />
          </Form.Item>

          <Form.Item
            label="图标"
            name="icon"
          >
            <Select placeholder="选择图标">
              <Option value="user">用户</Option>
              <Option value="shopping-cart">购物车</Option>
              <Option value="product">商品</Option>
              <Option value="order">订单</Option>
              <Option value="category">分类</Option>
              <Option value="tag">标签</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 底部操作按钮 */}
      <div className="step-actions">
        <Button onClick={onPrev}>上一步</Button>
        <Button 
          type="primary" 
          onClick={onNext}
          disabled={entities.length === 0}
        >
          下一步
        </Button>
      </div>
    </div>
  );
};

export default EntityModelingStep;
