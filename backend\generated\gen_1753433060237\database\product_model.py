```python
from sqlalchemy import Column, Integer, String, Numeric, DateTime, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from typing import Optional
from decimal import Decimal

Base = declarative_base()

class Product(Base):
    """
    商品实体模型
    
    用于存储商品信息，包括名称、价格和分类等基本信息。
    包含创建和更新时间戳，便于追踪数据变更历史。
    """
    
    __tablename__ = 'products'
    
    # 主键ID
    id = Column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="商品唯一标识符"
    )
    
    # 商品名称
    name = Column(
        String(255),
        nullable=False,
        comment="商品名称"
    )
    
    # 价格
    price = Column(
        Numeric(precision=10, scale=2),
        nullable=False,
        comment="商品价格"
    )
    
    # 分类
    category = Column(
        String(100),
        nullable=False,
        comment="商品分类"
    )
    
    # 创建时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="记录创建时间"
    )
    
    # 更新时间戳
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="记录最后更新时间"
    )
    
    # 索引定义
    __table_args__ = (
        Index('idx_product_name', 'name'),
        Index('idx_product_category', 'category'),
        Index('idx_product_price', 'price'),
        Index('idx_product_created_at', 'created_at'),
    )
    
    def __repr__(self) -> str:
        """
        返回对象的字符串表示形式
        
        Returns:
            str: Product对象的字符串表示
        """
        return f"<Product(id={self.id}, name='{self.name}', price={self.price}, category='{self.category}')>"
    
    def to_dict(self) -> dict:
        """
        将Product对象转换为字典格式
        
        Returns:
            dict: 包含商品所有信息的字典
        """
        return {
            "id": self.id,
            "name": self.name,
            "price": float(self.price) if self.price else None,
            "category": self.category,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Product':
        """
        从字典创建Product对象
        
        Args:
            data (dict): 包含商品信息的字典
            
        Returns:
            Product: 创建的Product对象
        """
        return cls(
            id=data.get('id'),
            name=data.get('name'),
            price=Decimal(str(data.get('price'))) if data.get('price') is not None else None,
            category=data.get('category')
        )
```