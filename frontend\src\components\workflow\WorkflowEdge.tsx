/**
 * 工作流连线组件 - Apple风格设计
 */

import { Edge } from 'butterfly-dag';

class WorkflowEdge extends Edge {
  public options: any;

  constructor(opts: any) {
    super(opts);
    this.options = opts;
  }

  draw(obj: any) {
    const path = super.draw(obj);

    if (path) {
      // Apple风格的连线样式
      path.setAttribute('stroke', '#007aff');
      path.setAttribute('stroke-width', '2');
      path.setAttribute('fill', 'none');
      path.setAttribute('stroke-linecap', 'round');
      path.setAttribute('stroke-linejoin', 'round');

      // 添加动画效果
      path.style.transition = 'all 0.3s ease';

      // 悬停效果
      path.addEventListener('mouseenter', () => {
        path.setAttribute('stroke', '#0051d5');
        path.setAttribute('stroke-width', '3');
      });

      path.addEventListener('mouseleave', () => {
        path.setAttribute('stroke', '#007aff');
        path.setAttribute('stroke-width', '2');
      });
    }

    return path;
  }

  drawArrow() {
    const arrow = super.drawArrow();

    if (arrow) {
      // Apple风格的箭头样式
      arrow.setAttribute('fill', '#007aff');
      arrow.setAttribute('stroke', '#007aff');
    }

    return arrow;
  }

  drawLabel() {
    if (!this.options.label) return null;

    const labelGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    labelGroup.setAttribute('class', 'edge-label-group');

    // 标签背景
    const labelBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    labelBg.setAttribute('rx', '8');
    labelBg.setAttribute('ry', '8');
    labelBg.setAttribute('fill', 'rgba(255, 255, 255, 0.95)');
    labelBg.setAttribute('stroke', '#007aff');
    labelBg.setAttribute('stroke-width', '1');
    labelBg.style.filter = 'drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1))';

    // 标签文本
    const labelText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    labelText.setAttribute('text-anchor', 'middle');
    labelText.setAttribute('dominant-baseline', 'middle');
    labelText.setAttribute('fill', '#007aff');
    labelText.setAttribute('font-size', '12');
    labelText.setAttribute('font-weight', '500');
    labelText.setAttribute('font-family', '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif');
    labelText.textContent = this.options.label;

    // 计算文本尺寸并设置背景尺寸
    const textBBox = labelText.getBBox();
    const padding = 8;
    labelBg.setAttribute('x', String(-textBBox.width / 2 - padding));
    labelBg.setAttribute('y', String(-textBBox.height / 2 - padding));
    labelBg.setAttribute('width', String(textBBox.width + padding * 2));
    labelBg.setAttribute('height', String(textBBox.height + padding * 2));

    labelGroup.appendChild(labelBg);
    labelGroup.appendChild(labelText);

    return labelGroup;
  }
}

export default WorkflowEdge;
