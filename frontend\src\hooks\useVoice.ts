import { useState, useRef, useEffect } from 'react';

// 语音识别类型定义
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }

  interface SpeechRecognition extends EventTarget {
    continuous: boolean;
    interimResults: boolean;
    lang: string;
    onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
    onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
    onend: ((this: SpeechRecognition, ev: Event) => any) | null;
    onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
    start(): void;
    stop(): void;
    abort(): void;
  }
}

interface UseVoiceReturn {
  isListening: boolean;
  transcript: string;
  isSupported: boolean;
  isSpeaking: boolean;
  volume: number; // 音量级别 (0-1)
  startListening: () => Promise<void>;
  stopListening: () => void;
  speak: (text: string) => void;
  stopSpeaking: () => void;
  checkMicrophonePermission: () => Promise<boolean>;
}

export const useVoice = (): UseVoiceReturn => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [volume, setVolume] = useState(0);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    // 检查浏览器支持
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const speechSynthesis = window.speechSynthesis;
    
    if (SpeechRecognition && speechSynthesis) {
      setIsSupported(true);
      
      // 初始化语音识别
      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = true;
      recognition.lang = 'zh-CN';
      
      recognition.onstart = () => {
        setIsListening(true);
        console.log('[语音] 开始语音识别');
      };
      
      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        setTranscript(finalTranscript || interimTranscript);
      };

      recognition.onend = () => {
        setIsListening(false);
        console.log('[语音] 语音识别结束');
        // 停止音量分析
        stopVolumeAnalysis();
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('[语音] 语音识别错误:', event.error);
        setIsListening(false);
        // 停止音量分析
        stopVolumeAnalysis();

        // 根据错误类型给出不同的提示
        switch (event.error) {
          case 'network':
            console.warn('[语音] 网络错误，语音识别需要网络连接');
            break;
          case 'not-allowed':
            console.warn('[语音] 麦克风权限被拒绝');
            break;
          case 'no-speech':
            console.warn('[语音] 未检测到语音');
            break;
          case 'audio-capture':
            console.warn('[语音] 音频捕获失败');
            break;
          default:
            console.warn('[语音] 语音识别错误:', event.error);
        }
      };
      
      recognitionRef.current = recognition;
      synthRef.current = speechSynthesis;
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
    };
  }, []);

  // 音量分析函数
  const analyzeVolume = () => {
    if (!analyserRef.current) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    analyserRef.current.getByteFrequencyData(dataArray);

    // 计算平均音量
    let sum = 0;
    for (let i = 0; i < bufferLength; i++) {
      sum += dataArray[i];
    }
    const average = sum / bufferLength;

    // 将音量标准化到0-1范围，并应用平滑处理
    const normalizedVolume = Math.min(average / 128, 1);
    setVolume(prev => prev * 0.8 + normalizedVolume * 0.2); // 平滑处理

    if (isListening) {
      animationFrameRef.current = requestAnimationFrame(analyzeVolume);
    }
  };

  // 启动音频分析
  const startVolumeAnalysis = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);

      analyserRef.current.fftSize = 256;
      microphoneRef.current.connect(analyserRef.current);

      analyzeVolume();
    } catch (error) {
      console.error('[语音] 音频分析启动失败:', error);
    }
  };

  // 停止音频分析
  const stopVolumeAnalysis = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (microphoneRef.current) {
      microphoneRef.current.disconnect();
      microphoneRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    analyserRef.current = null;
    setVolume(0);
  };

  const checkMicrophonePermission = async (): Promise<boolean> => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('[语音] 麦克风权限被拒绝:', error);
      return false;
    }
  };

  const startListening = async (): Promise<void> => {
    if (!recognitionRef.current || !isSupported) return;

    const hasPermission = await checkMicrophonePermission();
    if (!hasPermission) return;

    setTranscript('');

    try {
      // 启动音量分析
      await startVolumeAnalysis();
      // 启动语音识别
      recognitionRef.current.start();
    } catch (error) {
      console.error('[语音] 启动语音识别失败:', error);
    }
  };

  const stopListening = (): void => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    // 停止音量分析
    stopVolumeAnalysis();
  };

  const speak = (text: string): void => {
    if (!synthRef.current || !text.trim()) return;
    
    // 停止当前播放
    synthRef.current.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'zh-CN';
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;
    
    utterance.onstart = () => {
      setIsSpeaking(true);
      console.log('[语音] 开始语音合成');
    };
    
    utterance.onend = () => {
      setIsSpeaking(false);
      console.log('[语音] 语音合成结束');
    };
    
    utterance.onerror = (event) => {
      console.error('[语音] 语音合成错误:', event.error);
      setIsSpeaking(false);
    };
    
    synthRef.current.speak(utterance);
  };

  const stopSpeaking = (): void => {
    if (synthRef.current) {
      synthRef.current.cancel();
      setIsSpeaking(false);
    }
  };

  return {
    isListening,
    transcript,
    isSupported,
    isSpeaking,
    volume,
    startListening,
    stopListening,
    speak,
    stopSpeaking,
    checkMicrophonePermission
  };
};
