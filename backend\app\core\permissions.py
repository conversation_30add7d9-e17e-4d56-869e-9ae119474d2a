"""
权限验证装饰器和中间件
在系统运行时进行真正的角色权限验证
"""
from fastapi import HTTPException, Depends, Request, status
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from typing import List, Optional, Callable, Any
from functools import wraps
import jwt
import logging

from app.core.database import get_db_session
from app.services.permission_service import PermissionService

logger = logging.getLogger(__name__)
security = HTTPBearer()
permission_service = PermissionService()


class PermissionDenied(HTTPException):
    """权限拒绝异常"""
    def __init__(self, detail: str = "权限不足"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)


def extract_user_from_token(token: str) -> dict:
    """从token中提取用户信息"""
    try:
        # 这里简化处理，实际应该验证JWT token
        # 目前返回模拟的用户信息
        if token == "AILF_DEV_2024_SECURE":
            return {
                "user_id": "developer_001",
                "user_type": "developer",
                "name": "开发者"
            }
        
        # 模拟从token解析用户信息
        # 实际项目中应该使用JWT解码
        user_mapping = {
            "front_desk_token": {
                "user_id": "front_desk_user_001",
                "user_type": "business_user",
                "name": "前台小王"
            },
            "coach_token": {
                "user_id": "coach_user_001", 
                "user_type": "business_user",
                "name": "教练小李"
            },
            "manager_token": {
                "user_id": "manager_user_001",
                "user_type": "business_user", 
                "name": "经理小张"
            }
        }
        
        return user_mapping.get(token, {
            "user_id": "unknown_user",
            "user_type": "guest",
            "name": "未知用户"
        })
        
    except Exception as e:
        logger.error(f"Token解析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌"
        )


def require_permission(resource: str, action: str, context: Optional[dict] = None):
    """
    权限验证装饰器
    
    Args:
        resource: 资源名称 (如: products, orders, customers)
        action: 操作类型 (如: read, create, update, delete)
        context: 上下文信息 (可选)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从请求中获取token
            request = None
            credentials = None
            
            # 查找Request和HTTPAuthorizationCredentials参数
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                elif isinstance(arg, HTTPAuthorizationCredentials):
                    credentials = arg
            
            for key, value in kwargs.items():
                if isinstance(value, Request):
                    request = value
                elif isinstance(value, HTTPAuthorizationCredentials):
                    credentials = value
            
            # 如果没有找到credentials，尝试从request的headers中获取
            if not credentials and request:
                auth_header = request.headers.get("Authorization")
                if auth_header and auth_header.startswith("Bearer "):
                    token = auth_header[7:]  # 移除 "Bearer " 前缀
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="缺少认证令牌"
                    )
            else:
                token = credentials.credentials if credentials else None
            
            if not token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="缺少认证令牌"
                )
            
            # 提取用户信息
            user_info = extract_user_from_token(token)
            
            # 如果是开发者，跳过权限检查
            if user_info.get("user_type") == "developer":
                logger.info(f"开发者访问: {resource}:{action}")
                return await func(*args, **kwargs)
            
            # 进行权限检查
            try:
                with get_db_session() as db:
                    permission_check = permission_service.check_permission(
                        user_id=user_info["user_id"],
                        resource=resource,
                        action=action,
                        context=context or {},
                        db=db
                    )
                    
                    if not permission_check["allowed"]:
                        logger.warning(f"权限拒绝: 用户{user_info['user_id']}尝试访问{resource}:{action}")
                        raise PermissionDenied(
                            detail=f"用户没有'{resource}:{action}'权限"
                        )
                    
                    logger.info(f"权限验证通过: 用户{user_info['user_id']}访问{resource}:{action}")
                    
                    # 将用户信息添加到kwargs中，供API使用
                    kwargs['current_user'] = user_info
                    kwargs['permission_context'] = permission_check
                    
                    return await func(*args, **kwargs)
                    
            except PermissionDenied:
                raise
            except Exception as e:
                logger.error(f"权限检查异常: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="权限检查失败"
                )
        
        return wrapper
    return decorator


def require_any_permission(permissions: List[tuple], context: Optional[dict] = None):
    """
    要求任一权限的装饰器
    
    Args:
        permissions: 权限列表，每个元素为(resource, action)元组
        context: 上下文信息
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取用户信息的逻辑与require_permission相同
            request = None
            credentials = None
            
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                elif isinstance(arg, HTTPAuthorizationCredentials):
                    credentials = arg
            
            for key, value in kwargs.items():
                if isinstance(value, Request):
                    request = value
                elif isinstance(value, HTTPAuthorizationCredentials):
                    credentials = value
            
            if not credentials and request:
                auth_header = request.headers.get("Authorization")
                if auth_header and auth_header.startswith("Bearer "):
                    token = auth_header[7:]
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="缺少认证令牌"
                    )
            else:
                token = credentials.credentials if credentials else None
            
            if not token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="缺少认证令牌"
                )
            
            user_info = extract_user_from_token(token)
            
            # 开发者跳过权限检查
            if user_info.get("user_type") == "developer":
                return await func(*args, **kwargs)
            
            # 检查是否有任一权限
            has_permission = False
            permission_results = []
            
            try:
                with get_db_session() as db:
                    for resource, action in permissions:
                        permission_check = permission_service.check_permission(
                            user_id=user_info["user_id"],
                            resource=resource,
                            action=action,
                            context=context or {},
                            db=db
                        )
                        permission_results.append(permission_check)
                        
                        if permission_check["allowed"]:
                            has_permission = True
                            break
                    
                    if not has_permission:
                        required_perms = [f"{r}:{a}" for r, a in permissions]
                        raise PermissionDenied(
                            detail=f"用户需要以下任一权限: {', '.join(required_perms)}"
                        )
                    
                    kwargs['current_user'] = user_info
                    kwargs['permission_results'] = permission_results
                    
                    return await func(*args, **kwargs)
                    
            except PermissionDenied:
                raise
            except Exception as e:
                logger.error(f"权限检查异常: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="权限检查失败"
                )
        
        return wrapper
    return decorator


def require_role_level(min_level: int):
    """
    要求最低角色级别的装饰器
    
    Args:
        min_level: 最低角色级别 (1-10)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取用户信息
            request = None
            credentials = None
            
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                elif isinstance(arg, HTTPAuthorizationCredentials):
                    credentials = arg
            
            for key, value in kwargs.items():
                if isinstance(value, Request):
                    request = value
                elif isinstance(value, HTTPAuthorizationCredentials):
                    credentials = value
            
            if not credentials and request:
                auth_header = request.headers.get("Authorization")
                if auth_header and auth_header.startswith("Bearer "):
                    token = auth_header[7:]
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="缺少认证令牌"
                    )
            else:
                token = credentials.credentials if credentials else None
            
            if not token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="缺少认证令牌"
                )
            
            user_info = extract_user_from_token(token)
            
            # 开发者跳过级别检查
            if user_info.get("user_type") == "developer":
                return await func(*args, **kwargs)
            
            # 检查用户角色级别
            try:
                with get_db_session() as db:
                    user_roles = permission_service._get_user_roles(user_info["user_id"], db)
                    
                    if not user_roles:
                        raise PermissionDenied("用户没有分配角色")
                    
                    max_user_level = max(role["level"] for role in user_roles)
                    
                    if max_user_level < min_level:
                        raise PermissionDenied(
                            f"需要角色级别 {min_level} 或以上，当前最高级别: {max_user_level}"
                        )
                    
                    kwargs['current_user'] = user_info
                    kwargs['user_roles'] = user_roles
                    kwargs['max_role_level'] = max_user_level
                    
                    return await func(*args, **kwargs)
                    
            except PermissionDenied:
                raise
            except Exception as e:
                logger.error(f"角色级别检查异常: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="角色级别检查失败"
                )
        
        return wrapper
    return decorator
