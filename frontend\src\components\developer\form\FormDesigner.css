/* FormDesigner.css */

.form-designer {
  height: 100%;
  background: #f8f9fa;
}

.designer-content {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e9ea;
}

.basic-config-form {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sections-container {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sections-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.empty-sections {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-count {
  color: #6b7280;
  font-size: 12px;
}

.fields-container {
  padding: 16px 0;
}

.empty-fields {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-style: italic;
}

.field-item {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.field-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-label {
  font-weight: 500;
  color: #1f2937;
}

.field-actions {
  display: flex;
  gap: 4px;
}

.field-details {
  display: flex;
  gap: 16px;
  color: #6b7280;
  font-size: 12px;
}
