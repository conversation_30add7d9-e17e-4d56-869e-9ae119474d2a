"""
测试工作流API的脚本
"""
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

BASE_URL = "http://localhost:5000"

def get_developer_token():
    """获取开发者token"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/developer",
            json={"password": "AILF_DEV_2024_SECURE"},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data["data"]["token"]
        else:
            print(f"❌ 获取token失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取token异常: {e}")
        return None

def test_workflow_configuration_api(token):
    """测试工作流配置API"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print("🔍 测试工作流配置API...")
        response = requests.get(
            f"{BASE_URL}/api/configuration/for-amis",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 工作流配置API测试成功")
            print(f"📊 响应数据:")
            print(f"   - API数量: {data['data']['apis']['total']}")
            print(f"   - 工作流数量: {data['data']['workflows']['total']}")
            
            # 显示工作流详情
            workflows = data['data']['workflows']['items']
            if workflows:
                print(f"\n📋 工作流列表:")
                for workflow in workflows:
                    print(f"   • {workflow['name']}")
                    print(f"     业务场景: {workflow['business_scenario']}")
                    print(f"     节点数量: {workflow['node_count']}")
                    user_intents = workflow.get('user_intents') or []
                    trigger_keywords = workflow.get('trigger_keywords') or []
                    print(f"     用户意图: {', '.join(user_intents)}")
                    print(f"     触发关键词: {', '.join(trigger_keywords)}")
                    print()
            
            return True
        else:
            print(f"❌ 工作流配置API测试失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 工作流配置API测试异常: {e}")
        return False

def test_ai_amis_with_workflow(token):
    """测试AI amis生成器与工作流的集成"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print("🤖 测试AI amis生成器与工作流集成...")
        
        # 测试火车票预订相关的命令
        test_commands = [
            "我要买火车票",
            "帮我订火车票",
            "查询火车票",
            "预订车票"
        ]
        
        for command in test_commands:
            print(f"\n🎯 测试命令: '{command}'")
            
            response = requests.post(
                f"{BASE_URL}/api/command",
                json={"command": command},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 命令处理成功")
                print(f"   完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")

                # 检查响应结构
                if data.get('success') and data.get('data'):
                    response_data = data['data']

                    if 'response_text' in response_data:
                        print(f"   响应文本: {response_data['response_text']}")

                    # 检查是否匹配到工作流
                    if 'matched_workflow' in response_data:
                        workflow = response_data['matched_workflow']
                        print(f"   🎯 匹配到工作流: {workflow['name']}")
                        print(f"   📋 业务场景: {workflow['business_scenario']}")
                    else:
                        print(f"   ⚠️ 未匹配到特定工作流")

                    # 检查生成的amis schema
                    if 'amis_schema' in response_data:
                        schema = response_data['amis_schema']
                        print(f"   📄 生成的schema类型: {schema.get('type', 'unknown')}")
                        if 'title' in schema:
                            print(f"   📝 页面标题: {schema['title']}")
                else:
                    print(f"   ⚠️ 响应格式异常或处理失败")
                    if 'error' in data:
                        print(f"   错误信息: {data['error']}")

            else:
                print(f"❌ 命令处理失败: {response.status_code} - {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI amis测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 AILF 工作流API测试工具")
    print("=" * 60)
    
    # 1. 获取开发者token
    print("1️⃣ 获取开发者token...")
    token = get_developer_token()
    if not token:
        print("❌ 无法获取开发者token，测试终止")
        return 1
    
    print(f"✅ 获取token成功: {token[:50]}...")
    
    # 2. 测试工作流配置API
    print("\n2️⃣ 测试工作流配置API...")
    if not test_workflow_configuration_api(token):
        print("❌ 工作流配置API测试失败")
        return 1
    
    # 3. 测试AI amis生成器与工作流集成
    print("\n3️⃣ 测试AI amis生成器与工作流集成...")
    if not test_ai_amis_with_workflow(token):
        print("❌ AI amis集成测试失败")
        return 1
    
    print("\n🎉 所有测试完成！")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
