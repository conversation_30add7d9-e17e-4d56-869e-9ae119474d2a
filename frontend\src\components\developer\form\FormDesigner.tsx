/**
 * 表单设计器组件
 */

import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Layout,
  Card,
  Button,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Space,
  message,
  Row,
  Col,
  Typography,
  Collapse,
  Modal,
  Tooltip,
  Tag,
  Divider,
  List
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SettingOutlined,
  EyeOutlined,
  SaveOutlined,
  UndoOutlined
} from '@ant-design/icons';
import {
  FormConfig,
  FormSection,
  FormField,
  FormLayout,
  FormPermission,
  formAPI,
  FORM_DISPLAY_TYPES,
  FORM_LAYOUT_TYPES,
  VALIDATION_RULES,
  PERMISSION_ACTIONS
} from '../../../services/developer/formAPI';
import './FormDesigner.css';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;

interface FormDesignerProps {
  mode: 'create' | 'edit';
  formConfig?: FormConfig | null;
  entities: Array<{
    id: string;
    name: string;
    displayName?: string;
    fields?: Array<{
      name: string;
      type: string;
      displayName?: string;
    }>;
  }>;
  onSave?: () => void;
  onCancel?: () => void;
}

interface FormDesignerRef {
  handleSave: () => void;
}

// 字段项组件
const FieldItem: React.FC<{
  field: FormField;
  onEdit: (field: FormField) => void;
  onDelete: (fieldId: string) => void;
}> = ({ field, onEdit, onDelete }) => {
  const displayTypeConfig = FORM_DISPLAY_TYPES[field.displayType as keyof typeof FORM_DISPLAY_TYPES];
  
  return (
    <div className="field-item">
      <div className="field-header">
        <div className="field-info">
          <span className="field-label">{field.label}</span>
          <Tag color={field.required ? 'red' : 'default'}>
            {field.required ? '必填' : '可选'}
          </Tag>
          <Tag color="blue">{displayTypeConfig?.label || field.displayType}</Tag>
        </div>
        <div className="field-actions">
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit(field)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              danger
              onClick={() => onDelete(field.id)}
            />
          </Tooltip>
        </div>
      </div>
      <div className="field-details">
        <Text type="secondary">字段: {field.entityField}</Text>
        {field.placeholder && (
          <Text type="secondary" style={{ marginLeft: 8 }}>
            占位符: {field.placeholder}
          </Text>
        )}
      </div>
    </div>
  );
};

const FormDesigner = forwardRef<FormDesignerRef, FormDesignerProps>(
  ({ mode, formConfig, entities, onSave, onCancel }, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [currentFormConfig, setCurrentFormConfig] = useState<FormConfig>({
      name: '',
      entity: '',
      description: '',
      layout: {
        type: 'grid',
        columns: 2,
        spacing: 16
      },
      sections: [],
      permissions: []
    });
    const [selectedEntity, setSelectedEntity] = useState<any>(null);
    const [fieldModalVisible, setFieldModalVisible] = useState(false);
    const [sectionModalVisible, setSectionModalVisible] = useState(false);
    const [editingField, setEditingField] = useState<FormField | null>(null);
    const [editingSection, setEditingSection] = useState<FormSection | null>(null);
    const [fieldForm] = Form.useForm();
    const [sectionForm] = Form.useForm();

    // 初始化表单配置
    useEffect(() => {
      if (mode === 'edit' && formConfig) {
        setCurrentFormConfig(formConfig);
        form.setFieldsValue({
          name: formConfig.name,
          entity: formConfig.entity,
          description: formConfig.description,
          layout: formConfig.layout
        });
        
        // 设置选中的实体
        const entity = entities.find(e => e.name === formConfig.entity);
        setSelectedEntity(entity);
      }
    }, [mode, formConfig, entities, form]);

    // 暴露保存方法给父组件
    useImperativeHandle(ref, () => ({
      handleSave: () => handleSave()
    }));

    // 处理实体选择
    const handleEntityChange = (entityName: string) => {
      const entity = entities.find(e => e.name === entityName);
      setSelectedEntity(entity);
      setCurrentFormConfig(prev => ({
        ...prev,
        entity: entityName,
        sections: [] // 清空已有分组
      }));
    };

    // 添加新分组
    const handleAddSection = () => {
      setEditingSection(null);
      sectionForm.resetFields();
      setSectionModalVisible(true);
    };

    // 编辑分组
    const handleEditSection = (section: FormSection) => {
      setEditingSection(section);
      sectionForm.setFieldsValue(section);
      setSectionModalVisible(true);
    };

    // 删除分组
    const handleDeleteSection = (sectionId: string) => {
      Modal.confirm({
        title: '确认删除',
        content: '删除分组将同时删除其中的所有字段，确定要删除吗？',
        onOk: () => {
          setCurrentFormConfig(prev => ({
            ...prev,
            sections: prev.sections.filter(s => s.id !== sectionId)
          }));
        }
      });
    };

    // 保存分组
    const handleSaveSection = async () => {
      try {
        const values = await sectionForm.validateFields();
        
        if (editingSection) {
          // 编辑现有分组
          setCurrentFormConfig(prev => ({
            ...prev,
            sections: prev.sections.map(s => 
              s.id === editingSection.id 
                ? { ...s, ...values }
                : s
            )
          }));
        } else {
          // 添加新分组
          const newSection: FormSection = {
            id: `section_${Date.now()}`,
            ...values,
            fields: []
          };
          setCurrentFormConfig(prev => ({
            ...prev,
            sections: [...prev.sections, newSection]
          }));
        }
        
        setSectionModalVisible(false);
      } catch (error) {
        console.error('分组保存失败:', error);
      }
    };

    // 添加字段
    const handleAddField = (sectionId: string) => {
      if (!selectedEntity?.fields || selectedEntity.fields.length === 0) {
        message.warning('该实体没有可用字段');
        return;
      }
      
      setEditingField(null);
      fieldForm.resetFields();
      fieldForm.setFieldValue('sectionId', sectionId);
      setFieldModalVisible(true);
    };

    // 编辑字段
    const handleEditField = (field: FormField) => {
      setEditingField(field);
      fieldForm.setFieldsValue(field);
      setFieldModalVisible(true);
    };

    // 删除字段
    const handleDeleteField = (fieldId: string) => {
      setCurrentFormConfig(prev => ({
        ...prev,
        sections: prev.sections.map(section => ({
          ...section,
          fields: section.fields.filter(f => f.id !== fieldId)
        }))
      }));
    };

    // 保存字段
    const handleSaveField = async () => {
      try {
        const values = await fieldForm.validateFields();
        
        if (editingField) {
          // 编辑现有字段
          setCurrentFormConfig(prev => ({
            ...prev,
            sections: prev.sections.map(section => ({
              ...section,
              fields: section.fields.map(f => 
                f.id === editingField.id 
                  ? { ...f, ...values }
                  : f
              )
            }))
          }));
        } else {
          // 添加新字段
          const newField: FormField = {
            id: `field_${Date.now()}`,
            ...values
          };
          
          setCurrentFormConfig(prev => ({
            ...prev,
            sections: prev.sections.map(section => 
              section.id === values.sectionId
                ? { ...section, fields: [...section.fields, newField] }
                : section
            )
          }));
        }
        
        setFieldModalVisible(false);
      } catch (error) {
        console.error('字段保存失败:', error);
      }
    };

    // 保存表单配置
    const handleSave = async () => {
      try {
        setLoading(true);
        const formValues = await form.validateFields();
        
        const formData = {
          ...formValues,
          ...currentFormConfig,
          name: formValues.name,
          entity: formValues.entity,
          description: formValues.description,
          layout: formValues.layout
        };

        let response;
        if (mode === 'create') {
          response = await formAPI.createForm(formData);
        } else {
          response = await formAPI.updateForm(formConfig!.id!, formData);
        }

        if (response.code === 200 || response.code === 201) {
          onSave?.();
        } else {
          message.error('保存失败');
        }
      } catch (error) {
        console.error('保存表单配置失败:', error);
        message.error('保存失败');
      } finally {
        setLoading(false);
      }
    };

    return (
      <div className="form-designer">
        <Layout>
          <Content className="designer-content">
            <div className="designer-header">
              <Title level={4}>
                {mode === 'create' ? '创建表单' : '编辑表单'}
              </Title>
              <Space>
                <Button icon={<UndoOutlined />} onClick={onCancel}>
                  取消
                </Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  loading={loading}
                  onClick={handleSave}
                >
                  保存
                </Button>
              </Space>
            </div>

            <Form
              form={form}
              layout="vertical"
              className="basic-config-form"
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="name"
                    label="表单名称"
                    rules={[{ required: true, message: '请输入表单名称' }]}
                  >
                    <Input placeholder="请输入表单名称" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="entity"
                    label="关联实体"
                    rules={[{ required: true, message: '请选择关联实体' }]}
                  >
                    <Select
                      placeholder="请选择关联实体"
                      onChange={handleEntityChange}
                    >
                      {entities.map(entity => (
                        <Option key={entity.id} value={entity.name}>
                          {entity.displayName || entity.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={['layout', 'type']} label="布局类型">
                    <Select>
                      {Object.entries(FORM_LAYOUT_TYPES).map(([key, config]) => (
                        <Option key={key} value={key}>
                          {config.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={16}>
                  <Form.Item name="description" label="表单描述">
                    <TextArea placeholder="请输入表单描述" rows={2} />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item name={['layout', 'columns']} label="列数">
                    <InputNumber min={1} max={4} />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item name={['layout', 'spacing']} label="间距">
                    <InputNumber min={8} max={32} />
                  </Form.Item>
                </Col>
              </Row>
            </Form>

            <Divider />

            <div className="sections-container">
              <div className="sections-header">
                <Title level={5}>表单分组</Title>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddSection}
                >
                  添加分组
                </Button>
              </div>

              {currentFormConfig.sections.length === 0 ? (
                <div className="empty-sections">
                  <Text type="secondary">请先添加表单分组</Text>
                </div>
              ) : (
                <Collapse>
                  {currentFormConfig.sections.map(section => (
                    <Panel
                      key={section.id}
                      header={
                        <div className="section-header">
                          <span>{section.title}</span>
                          <span className="field-count">
                            ({section.fields.length} 个字段)
                          </span>
                        </div>
                      }
                      extra={
                        <Space>
                          <Button
                            type="text"
                            size="small"
                            icon={<PlusOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddField(section.id);
                            }}
                          >
                            添加字段
                          </Button>
                          <Button
                            type="text"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditSection(section);
                            }}
                          />
                          <Button
                            type="text"
                            size="small"
                            icon={<DeleteOutlined />}
                            danger
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteSection(section.id);
                            }}
                          />
                        </Space>
                      }
                    >
                      <div className="fields-container">
                        {section.fields.length === 0 ? (
                          <div className="empty-fields">
                            <Text type="secondary">该分组暂无字段</Text>
                          </div>
                        ) : (
                          section.fields.map((field) => (
                            <FieldItem
                              key={field.id}
                              field={field}
                              onEdit={handleEditField}
                              onDelete={handleDeleteField}
                            />
                          ))
                        )}
                      </div>
                    </Panel>
                  ))}
                </Collapse>
              )}
            </div>
          </Content>
        </Layout>

        {/* 分组编辑模态框 */}
        <Modal
          title={editingSection ? '编辑分组' : '添加分组'}
          open={sectionModalVisible}
          onOk={handleSaveSection}
          onCancel={() => setSectionModalVisible(false)}
        >
          <Form form={sectionForm} layout="vertical">
            <Form.Item
              name="title"
              label="分组标题"
              rules={[{ required: true, message: '请输入分组标题' }]}
            >
              <Input placeholder="请输入分组标题" />
            </Form.Item>
            <Form.Item name="collapsible" label="是否可折叠" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Form>
        </Modal>

        {/* 字段编辑模态框 */}
        <Modal
          title={editingField ? '编辑字段' : '添加字段'}
          open={fieldModalVisible}
          onOk={handleSaveField}
          onCancel={() => setFieldModalVisible(false)}
          width={800}
        >
          <Form form={fieldForm} layout="vertical">
            {!editingField && (
              <Form.Item name="sectionId" hidden>
                <Input />
              </Form.Item>
            )}
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="entityField"
                  label="实体字段"
                  rules={[{ required: true, message: '请选择实体字段' }]}
                >
                  <Select placeholder="请选择实体字段">
                    {selectedEntity?.fields?.map((field: any) => (
                      <Option key={field.name} value={field.name}>
                        {field.displayName || field.name} ({field.type})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="displayType"
                  label="显示类型"
                  rules={[{ required: true, message: '请选择显示类型' }]}
                >
                  <Select placeholder="请选择显示类型">
                    {Object.entries(FORM_DISPLAY_TYPES).map(([key, config]) => (
                      <Option key={key} value={key}>
                        {config.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="label"
                  label="字段标签"
                  rules={[{ required: true, message: '请输入字段标签' }]}
                >
                  <Input placeholder="请输入字段标签" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="placeholder" label="占位符">
                  <Input placeholder="请输入占位符" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="required" label="是否必填" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="readonly" label="是否只读" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="hidden" label="是否隐藏" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    );
  }
);

FormDesigner.displayName = 'FormDesigner';

export default FormDesigner;
