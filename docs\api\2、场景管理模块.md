# 场景管理模块 API 文档

## 📋 概述

场景管理模块提供业务场景的配置和管理功能，支持创建、更新、查询和验证业务场景配置。

## 🎯 API 端点列表

### 1. 获取当前活跃场景配置
### 2. 创建或更新场景配置
### 3. 获取特定场景配置
### 4. 验证当前场景配置的有效性
### 5. 从预设模板创建场景配置

---

## API 详细文档

### 1. 获取当前活跃场景配置

**GET** `/api/scenario`

#### 描述
获取当前系统中活跃的场景配置信息。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| include_details | boolean | 否 | 是否包含详细配置信息，默认false |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/scenario?include_details=true" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取场景配置成功",
  "data": {
    "scenario": {
      "id": "scenario_1705123456789",
      "name": "电商管理系统",
      "type": "ecommerce",
      "description": "完整的电子商务管理系统",
      "status": "active",
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:30:00.000Z",
      "config": {
        "business_domain": "电子商务",
        "target_users": ["管理员", "销售人员", "客服"],
        "key_features": ["商品管理", "订单处理", "用户管理"]
      }
    }
  }
}
```

**无场景响应 (404 Not Found):**
```json
{
  "code": 404,
  "message": "未找到活跃的场景配置",
  "data": {
    "error": "no_active_scenario",
    "details": "系统中没有配置活跃的业务场景"
  }
}
```

---

### 2. 创建或更新场景配置

**POST** `/api/scenario`

#### 描述
创建新的场景配置或更新现有的场景配置。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "type": "string",
  "description": "string",
  "config": {
    "business_domain": "string",
    "target_users": ["string"],
    "key_features": ["string"],
    "custom_settings": {}
  }
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 场景名称 |
| type | string | 是 | 场景类型 (ecommerce, hospital, restaurant, etc.) |
| description | string | 否 | 场景描述 |
| config | object | 是 | 场景配置对象 |
| config.business_domain | string | 是 | 业务领域 |
| config.target_users | array | 是 | 目标用户群体 |
| config.key_features | array | 是 | 关键功能列表 |
| config.custom_settings | object | 否 | 自定义设置 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/scenario" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "餐厅管理系统",
    "type": "restaurant",
    "description": "完整的餐厅管理解决方案",
    "config": {
      "business_domain": "餐饮服务",
      "target_users": ["店长", "服务员", "厨师"],
      "key_features": ["菜品管理", "订单管理", "桌台管理"],
      "custom_settings": {
        "table_count": 20,
        "support_takeout": true
      }
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "场景配置创建成功",
  "data": {
    "scenario": {
      "id": "scenario_1705123456790",
      "name": "餐厅管理系统",
      "type": "restaurant",
      "description": "完整的餐厅管理解决方案",
      "status": "active",
      "created_at": "2024-01-20T11:00:00.000Z",
      "updated_at": "2024-01-20T11:00:00.000Z",
      "config": {
        "business_domain": "餐饮服务",
        "target_users": ["店长", "服务员", "厨师"],
        "key_features": ["菜品管理", "订单管理", "桌台管理"],
        "custom_settings": {
          "table_count": 20,
          "support_takeout": true
        }
      }
    }
  }
}
```

**更新响应 (200 OK):**
```json
{
  "code": 200,
  "message": "场景配置更新成功",
  "data": {
    "scenario": {
      "id": "scenario_1705123456790",
      "name": "餐厅管理系统",
      "type": "restaurant",
      "description": "完整的餐厅管理解决方案",
      "status": "active",
      "created_at": "2024-01-20T11:00:00.000Z",
      "updated_at": "2024-01-20T11:15:00.000Z",
      "config": {
        "business_domain": "餐饮服务",
        "target_users": ["店长", "服务员", "厨师", "收银员"],
        "key_features": ["菜品管理", "订单管理", "桌台管理", "收银管理"],
        "custom_settings": {
          "table_count": 25,
          "support_takeout": true,
          "support_delivery": true
        }
      }
    }
  }
}
```

---

### 3. 获取特定场景配置

**GET** `/api/scenario/{scenario_id}`

#### 描述
根据场景ID获取特定场景的详细配置信息。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| scenario_id | string | 是 | 场景唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/scenario/scenario_1705123456790" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取场景配置成功",
  "data": {
    "scenario": {
      "id": "scenario_1705123456790",
      "name": "餐厅管理系统",
      "type": "restaurant",
      "description": "完整的餐厅管理解决方案",
      "status": "active",
      "created_at": "2024-01-20T11:00:00.000Z",
      "updated_at": "2024-01-20T11:15:00.000Z",
      "config": {
        "business_domain": "餐饮服务",
        "target_users": ["店长", "服务员", "厨师", "收银员"],
        "key_features": ["菜品管理", "订单管理", "桌台管理", "收银管理"],
        "custom_settings": {
          "table_count": 25,
          "support_takeout": true,
          "support_delivery": true
        }
      }
    }
  }
}
```

**未找到响应 (404 Not Found):**
```json
{
  "code": 404,
  "message": "场景配置不存在",
  "data": {
    "error": "scenario_not_found",
    "details": "指定的场景ID不存在"
  }
}
```

---

### 4. 验证当前场景配置的有效性

**POST** `/api/scenario/validate`

#### 描述
验证当前场景配置的完整性和有效性，检查配置是否符合要求。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "scenario_id": "string",
  "check_dependencies": boolean,
  "check_completeness": boolean
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/scenario/validate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "scenario_id": "scenario_1705123456789",
    "check_dependencies": true,
    "check_completeness": true
  }'
```

#### 响应格式

**验证通过 (200 OK):**
```json
{
  "code": 200,
  "message": "场景配置验证通过",
  "data": {
    "valid": true,
    "scenario_id": "scenario_1705123456789",
    "checks": {
      "basic_info": "passed",
      "entities": "passed",
      "workflows": "passed",
      "forms": "passed",
      "permissions": "passed"
    },
    "warnings": [],
    "suggestions": [
      "建议添加更多测试数据",
      "考虑优化工作流步骤"
    ]
  }
}
```

**验证失败 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "场景配置验证失败",
  "data": {
    "valid": false,
    "scenario_id": "scenario_1705123456789",
    "errors": [
      "缺少必需的实体定义",
      "工作流配置不完整",
      "权限配置存在冲突"
    ],
    "warnings": [
      "部分表单字段未配置验证规则"
    ]
  }
}
```

---

### 5. 从预设模板创建场景配置

**POST** `/api/scenario/from-template`

#### 描述
基于预设模板快速创建场景配置。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "template_key": "string",
  "name": "string",
  "description": "string",
  "customizations": {}
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| template_key | string | 是 | 模板标识符 |
| name | string | 是 | 场景名称 |
| description | string | 否 | 场景描述 |
| customizations | object | 否 | 自定义配置覆盖 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/scenario/from-template" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "template_key": "ecommerce_basic",
    "name": "我的电商系统",
    "description": "基于基础电商模板的定制系统",
    "customizations": {
      "support_multi_currency": true,
      "default_language": "zh-CN"
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "基于模板创建场景成功",
  "data": {
    "scenario": {
      "id": "scenario_1705123456791",
      "name": "我的电商系统",
      "type": "ecommerce",
      "description": "基于基础电商模板的定制系统",
      "status": "active",
      "template_key": "ecommerce_basic",
      "created_at": "2024-01-20T12:00:00.000Z",
      "updated_at": "2024-01-20T12:00:00.000Z",
      "config": {
        "business_domain": "电子商务",
        "target_users": ["管理员", "销售人员"],
        "key_features": ["商品管理", "订单处理", "用户管理"],
        "custom_settings": {
          "support_multi_currency": true,
          "default_language": "zh-CN"
        }
      }
    }
  }
}
```

---

## 📝 场景类型说明

| 类型 | 描述 | 适用场景 |
|------|------|----------|
| ecommerce | 电子商务 | 在线商城、零售管理 |
| hospital | 医疗管理 | 医院、诊所管理系统 |
| restaurant | 餐饮管理 | 餐厅、咖啡厅管理 |
| education | 教育管理 | 学校、培训机构 |
| logistics | 物流管理 | 货运、仓储管理 |
| finance | 金融管理 | 银行、保险业务 |
| custom | 自定义 | 其他业务场景 |

---

## 🔄 状态说明

| 状态 | 描述 |
|------|------|
| active | 活跃状态，当前正在使用的场景 |
| inactive | 非活跃状态，已配置但未启用 |
| draft | 草稿状态，配置未完成 |
| archived | 已归档，不再使用但保留配置 |

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| no_active_scenario | 404 | 没有活跃场景 | 创建或激活一个场景配置 |
| scenario_not_found | 404 | 场景不存在 | 检查场景ID是否正确 |
| template_not_found | 404 | 模板不存在 | 检查模板标识符是否正确 |
| validation_error | 400 | 参数验证失败 | 检查请求参数格式和必填项 |
| duplicate_scenario | 409 | 场景名称重复 | 使用不同的场景名称 |
