/**
 * 节点编辑器组件 - Apple风格设计
 */

import React from 'react';
import { Form, Input, Select, Card, Space, Button, Divider } from 'antd';
import { DeleteOutlined, SaveOutlined } from '@ant-design/icons';
import type { WorkflowNode } from '../../types/developer/workflowTypes';

const { Option } = Select;
const { TextArea } = Input;

interface NodeEditorProps {
  node: WorkflowNode | null;
  onSave?: (node: WorkflowNode) => void;
  onDelete?: (nodeId: string) => void;
  onCancel?: () => void;
}

const NodeEditor: React.FC<NodeEditorProps> = ({
  node,
  onSave,
  onDelete,
  onCancel
}) => {
  const [form] = Form.useForm();

  // 节点类型选项
  const nodeTypeOptions = [
    { value: 'start', label: '开始节点', icon: '▶' },
    { value: 'api_call', label: 'API调用', icon: '🔗' },
    { value: 'user_input', label: '用户输入', icon: '📝' },
    { value: 'condition', label: '条件判断', icon: '❓' },
    { value: 'notification', label: '通知节点', icon: '📢' },
    { value: 'end', label: '结束节点', icon: '⏹' }
  ];

  // 当节点变化时更新表单
  React.useEffect(() => {
    if (node) {
      form.setFieldsValue({
        name: node.name,
        description: node.description,
        type: node.type,
        api_endpoint: (node.config as any)?.api_endpoint || '',
        api_method: (node.config as any)?.method || 'GET',
        condition_expression: (node.config as any)?.evaluation_expression || '',
        user_message: node.ai_instructions?.user_message || '',
        success_handling: node.ai_instructions?.success_handling || '',
        error_handling: node.ai_instructions?.error_handling || ''
      });
    }
  }, [node, form]);

  // 保存节点
  const handleSave = async (values: any) => {
    if (!node) return;

    const updatedNode: WorkflowNode = {
      ...node,
      name: values.name,
      description: values.description,
      type: values.type,
      config: {
        ...node.config,
        api_endpoint: values.api_endpoint,
        method: values.api_method,
        evaluation_expression: values.condition_expression
      },
      ai_instructions: {
        ...node.ai_instructions,
        user_message: values.user_message,
        success_handling: values.success_handling,
        error_handling: values.error_handling
      }
    };

    onSave?.(updatedNode);
  };

  // 删除节点
  const handleDelete = () => {
    if (node) {
      onDelete?.(node.id);
    }
  };

  if (!node) {
    return (
      <div className="node-editor-empty">
        <div style={{ textAlign: 'center', padding: '40px 20px', color: '#86868b' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚪</div>
          <h3>选择一个节点进行编辑</h3>
          <p>点击画布中的节点来编辑其属性和配置</p>
        </div>
      </div>
    );
  }

  return (
    <div className="node-editor">
      <Card
        title={`编辑节点: ${node.name}`}
        extra={
          <Space>
            <Button icon={<SaveOutlined />} type="primary" onClick={() => form.submit()}>
              保存
            </Button>
            <Button icon={<DeleteOutlined />} danger onClick={handleDelete}>
              删除
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          {/* 基本信息 */}
          <Card size="small" title="基本信息" style={{ marginBottom: 16 }}>
            <Form.Item
              name="name"
              label="节点名称"
              rules={[{ required: true, message: '请输入节点名称' }]}
            >
              <Input placeholder="输入节点名称" />
            </Form.Item>

            <Form.Item
              name="description"
              label="节点描述"
            >
              <TextArea rows={2} placeholder="输入节点描述" />
            </Form.Item>

            <Form.Item
              name="type"
              label="节点类型"
              rules={[{ required: true, message: '请选择节点类型' }]}
            >
              <Select placeholder="选择节点类型">
                {nodeTypeOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.icon} {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Card>

          {/* API调用配置 */}
          {node.type === 'api_call' && (
            <Card size="small" title="API配置" style={{ marginBottom: 16 }}>
              <Form.Item
                name="api_endpoint"
                label="API端点"
                rules={[{ required: true, message: '请输入API端点' }]}
              >
                <Input placeholder="/api/example" />
              </Form.Item>

              <Form.Item
                name="api_method"
                label="请求方法"
              >
                <Select>
                  <Option value="GET">GET</Option>
                  <Option value="POST">POST</Option>
                  <Option value="PUT">PUT</Option>
                  <Option value="DELETE">DELETE</Option>
                </Select>
              </Form.Item>
            </Card>
          )}

          {/* 条件判断配置 */}
          {node.type === 'condition' && (
            <Card size="small" title="条件配置" style={{ marginBottom: 16 }}>
              <Form.Item
                name="condition_expression"
                label="条件表达式"
                rules={[{ required: true, message: '请输入条件表达式' }]}
              >
                <TextArea 
                  rows={3} 
                  placeholder="例如: {{result.status}} === 'success'"
                />
              </Form.Item>
            </Card>
          )}

          {/* AI指令配置 */}
          <Card size="small" title="AI指令配置" style={{ marginBottom: 16 }}>
            <Form.Item
              name="user_message"
              label="用户提示消息"
            >
              <TextArea rows={2} placeholder="向用户显示的消息" />
            </Form.Item>

            <Form.Item
              name="success_handling"
              label="成功处理指令"
            >
              <TextArea rows={2} placeholder="成功时的AI处理指令" />
            </Form.Item>

            <Form.Item
              name="error_handling"
              label="错误处理指令"
            >
              <TextArea rows={2} placeholder="错误时的AI处理指令" />
            </Form.Item>
          </Card>

          {/* 操作按钮 */}
          <div className="editor-actions">
            <Space>
              <Button onClick={onCancel}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存更改
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default NodeEditor;
