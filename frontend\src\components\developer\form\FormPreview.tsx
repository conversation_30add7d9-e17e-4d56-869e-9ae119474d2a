/**
 * 表单预览组件
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Form,
  Input,
  Select,
  Switch,
  DatePicker,
  InputNumber,
  Upload,
  Space,
  Typography,
  Collapse,
  Row,
  Col,
  message,
  Spin
} from 'antd';
import {
  EyeOutlined,
  CloseOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { FormConfig, formAPI } from '../../../services/developer/formAPI';
import './FormPreview.css';

const { Title, Text } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;

interface FormPreviewProps {
  formConfig: FormConfig;
  onClose?: () => void;
}

// 根据显示类型渲染字段组件
const renderField = (field: any) => {
  const commonProps = {
    name: field.entityField,
    label: field.label,
    rules: field.required ? [{ required: true, message: `${field.label}不能为空` }] : [],
    hidden: field.hidden
  };

  switch (field.displayType) {
    case 'input':
      return (
        <Form.Item {...commonProps}>
          <Input
            placeholder={field.placeholder}
            readOnly={field.readonly}
          />
        </Form.Item>
      );

    case 'textarea':
      return (
        <Form.Item {...commonProps}>
          <TextArea
            placeholder={field.placeholder}
            readOnly={field.readonly}
            rows={3}
          />
        </Form.Item>
      );

    case 'select':
      return (
        <Form.Item {...commonProps}>
          <Select
            placeholder={field.placeholder}
            disabled={field.readonly}
          >
            <Option value="option1">选项1</Option>
            <Option value="option2">选项2</Option>
            <Option value="option3">选项3</Option>
          </Select>
        </Form.Item>
      );

    case 'radio':
      return (
        <Form.Item {...commonProps}>
          <Select
            placeholder={field.placeholder}
            disabled={field.readonly}
            style={{ width: '100%' }}
          >
            <Option value="option1">选项1</Option>
            <Option value="option2">选项2</Option>
            <Option value="option3">选项3</Option>
          </Select>
        </Form.Item>
      );

    case 'checkbox':
    case 'switch':
      return (
        <Form.Item {...commonProps} valuePropName="checked">
          <Switch disabled={field.readonly} />
        </Form.Item>
      );

    case 'date-picker':
      return (
        <Form.Item {...commonProps}>
          <DatePicker
            placeholder={field.placeholder}
            disabled={field.readonly}
            style={{ width: '100%' }}
          />
        </Form.Item>
      );

    case 'upload':
      return (
        <Form.Item {...commonProps}>
          <Upload disabled={field.readonly}>
            <Button icon={<UploadOutlined />}>选择文件</Button>
          </Upload>
        </Form.Item>
      );

    case 'rich-text':
      return (
        <Form.Item {...commonProps}>
          <TextArea
            placeholder={field.placeholder}
            readOnly={field.readonly}
            rows={6}
          />
        </Form.Item>
      );

    default:
      return (
        <Form.Item {...commonProps}>
          <Input
            placeholder={field.placeholder}
            readOnly={field.readonly}
          />
        </Form.Item>
      );
  }
};

const FormPreview: React.FC<FormPreviewProps> = ({ formConfig, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [renderSchema, setRenderSchema] = useState<any>(null);

  // 获取表单渲染配置
  useEffect(() => {
    if (formConfig?.id) {
      loadRenderSchema();
    }
  }, [formConfig]);

  const loadRenderSchema = async () => {
    try {
      setLoading(true);
      const response = await formAPI.getFormRender(formConfig.id!, { mode: 'create' });
      if (response.code === 200) {
        setRenderSchema(response.data.schema);
      }
    } catch (error) {
      console.error('获取渲染配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      console.log('预览表单提交数据:', values);
      message.success('表单预览提交成功（仅用于预览）');
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('表单提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染表单分组
  const renderSection = (section: any) => {
    const { layout } = formConfig;
    
    if (layout.type === 'tabs') {
      return (
        <div key={section.id} className="section-content">
          {renderFields(section.fields, layout)}
        </div>
      );
    }

    return (
      <Panel
        key={section.id}
        header={section.title}
        className="form-section"
      >
        {renderFields(section.fields, layout)}
      </Panel>
    );
  };

  // 渲染字段
  const renderFields = (fields: any[], layout: any) => {
    if (layout.type === 'grid') {
      const chunks = [];
      for (let i = 0; i < fields.length; i += layout.columns) {
        chunks.push(fields.slice(i, i + layout.columns));
      }

      return chunks.map((chunk, chunkIndex) => (
        <Row key={chunkIndex} gutter={layout.spacing || 16}>
          {chunk.map(field => (
            <Col key={field.id} span={24 / layout.columns}>
              {renderField(field)}
            </Col>
          ))}
        </Row>
      ));
    }

    if (layout.type === 'inline') {
      return (
        <Row gutter={layout.spacing || 16}>
          {fields.map(field => (
            <Col key={field.id} flex="auto">
              {renderField(field)}
            </Col>
          ))}
        </Row>
      );
    }

    return fields.map(field => (
      <div key={field.id} style={{ marginBottom: layout.spacing || 16 }}>
        {renderField(field)}
      </div>
    ));
  };

  return (
    <div className="form-preview">
      <div className="preview-header">
        <div className="header-left">
          <EyeOutlined />
          <Title level={4} style={{ margin: 0 }}>
            表单预览
          </Title>
        </div>
        <div className="header-right">
          <Button icon={<CloseOutlined />} onClick={onClose}>
            关闭
          </Button>
        </div>
      </div>

      <div className="preview-content">
        <Spin spinning={loading}>
          <Card
            title={formConfig.name}
            className="preview-form-card"
          >
            <div className="form-description">
              <Text type="secondary">{formConfig.description}</Text>
            </div>

            <Form
              form={form}
              layout={formConfig.layout.type === 'horizontal' ? 'horizontal' : 'vertical'}
              onFinish={handleSubmit}
              className="preview-form"
            >
              {formConfig.layout.type === 'tabs' ? (
                <div className="tabs-container">
                  {formConfig.sections.map(section => renderSection(section))}
                </div>
              ) : (
                <Collapse
                  defaultActiveKey={formConfig.sections
                    .filter(s => !s.collapsible)
                    .map(s => s.id)
                  }
                  ghost={formConfig.layout.type === 'vertical'}
                >
                  {formConfig.sections.map(section => renderSection(section))}
                </Collapse>
              )}

              <div className="form-actions">
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    提交表单
                  </Button>
                  <Button onClick={() => form.resetFields()}>
                    重置
                  </Button>
                </Space>
              </div>
            </Form>
          </Card>
        </Spin>
      </div>

      <div className="preview-footer">
        <div className="form-info">
          <div className="info-item">
            <Text strong>关联实体:</Text>
            <Text>{formConfig.entity}</Text>
          </div>
          <div className="info-item">
            <Text strong>布局类型:</Text>
            <Text>{formConfig.layout.type}</Text>
          </div>
          <div className="info-item">
            <Text strong>分组数量:</Text>
            <Text>{formConfig.sections.length}</Text>
          </div>
          <div className="info-item">
            <Text strong>字段数量:</Text>
            <Text>{formConfig.sections.reduce((total, section) => total + section.fields.length, 0)}</Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormPreview;
