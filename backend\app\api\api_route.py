"""
API路由管理模块API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from app.core.auth import verify_developer_token
from app.services.api_route_service import APIRouteService
from app.schemas.api_route import (
    APIRouteRegisterRequest, APIRouteUpdateRequest, APIRouteStatusRequest,
    APIRouteRegisterResponse, APIRouteListResponse, APIRouteDetailResponse,
    APIRouteHealthResponse
)

router = APIRouter(prefix="/api/routes", tags=["API路由管理"])

# 创建服务实例
api_route_service = APIRouteService()


@router.post("/register", response_model=dict, status_code=201)
async def register_route(
    request: APIRouteRegisterRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    动态注册新的API路由
    
    - **api_id**: API标识符，全局唯一
    - **name**: 路由名称
    - **endpoint**: API端点路径
    - **method**: HTTP方法
    - **description**: 路由描述
    - **auth_required**: 是否需要认证
    - **handler**: 处理器配置
    - **parameters**: 参数列表
    - **responses**: 响应配置
    """
    result = api_route_service.register_route(request)
    
    if not result["success"]:
        if result["error"] == "route_conflict":
            raise HTTPException(status_code=409, detail={
                "code": 409,
                "message": "API路由已存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"],
                    "existing_route": result.get("existing_route")
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "注册API路由失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 201,
        "message": "API路由注册成功",
        "data": result["data"]
    }


@router.get("", response_model=dict)
async def get_routes(
    status: Optional[str] = Query(None, description="筛选路由状态 (active/inactive)"),
    method: Optional[str] = Query(None, description="筛选HTTP方法"),
    entity: Optional[str] = Query(None, description="筛选关联实体"),
    page: int = Query(1, ge=1, description="页码，默认1"),
    limit: int = Query(20, ge=1, le=100, description="每页数量，默认20"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取当前已注册的所有API路由
    
    支持按状态、方法、实体筛选，支持分页查询
    """
    result = api_route_service.get_routes_list(status, method, entity, page, limit)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "获取API路由列表失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    return {
        "code": 200,
        "message": "获取API路由列表成功",
        "data": result["data"]
    }


@router.get("/health", response_model=dict)
async def check_routes_health(
    _: dict = Depends(verify_developer_token)
):
    """
    检查所有API路由健康状态

    返回所有路由的健康状态信息，包括响应时间、错误信息等
    """
    result = api_route_service.check_routes_health()

    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "检查API路由健康状态失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })

    return {
        "code": 200,
        "message": "检查API路由健康状态成功",
        "data": result["data"]
    }


@router.get("/{route_id}", response_model=dict)
async def get_route_detail(
    route_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取API路由详情

    - **route_id**: 路由ID
    """
    result = api_route_service.get_route_detail(route_id)

    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "获取API路由详情失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })

    return {
        "code": 200,
        "message": "获取API路由详情成功",
        "data": result["data"]
    }


@router.put("/{route_id}", response_model=dict)
async def update_route(
    route_id: str,
    request: APIRouteUpdateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    更新已注册的API路由
    
    - **route_id**: 路由ID
    - **name**: 路由名称（可选）
    - **description**: 路由描述（可选）
    - **auth_required**: 是否需要认证（可选）
    - **handler**: 处理器配置（可选）
    - **parameters**: 参数列表（可选）
    - **responses**: 响应配置（可选）
    """
    result = api_route_service.update_route(route_id, request)
    
    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "更新API路由失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "更新API路由成功",
        "data": result["data"]
    }


@router.delete("/{route_id}", response_model=dict)
async def delete_route(
    route_id: str,
    force: bool = Query(False, description="是否强制删除，默认false"),
    _: dict = Depends(verify_developer_token)
):
    """
    删除已注册的API路由

    - **route_id**: 路由ID
    - **force**: 是否强制删除，默认false
    """
    result = api_route_service.delete_route(route_id, force)
    
    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "删除API路由失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "删除API路由成功",
        "data": result["data"]
    }


@router.post("/{route_id}/activate", response_model=dict)
async def activate_route(
    route_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    激活API路由
    
    - **route_id**: 路由ID
    """
    result = api_route_service.activate_route(route_id)
    
    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "激活API路由失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "激活API路由成功",
        "data": result["data"]
    }


@router.post("/{route_id}/deactivate", response_model=dict)
async def deactivate_route(
    route_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    停用API路由
    
    - **route_id**: 路由ID
    """
    result = api_route_service.deactivate_route(route_id)
    
    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "停用API路由失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "停用API路由成功",
        "data": result["data"]
    }


@router.get("/{route_id}/status", response_model=dict)
async def get_route_status(
    route_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取API路由状态

    - **route_id**: 路由ID
    """
    result = api_route_service.get_route_status(route_id)

    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "获取API路由状态失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })

    return {
        "code": 200,
        "message": "获取API路由状态成功",
        "data": result["data"]
    }


@router.post("/{route_id}/test", response_model=dict)
async def test_route(
    route_id: str,
    test_data: dict = {},
    _: dict = Depends(verify_developer_token)
):
    """
    测试API路由

    - **route_id**: 路由ID
    - **test_data**: 测试数据
    """
    result = api_route_service.test_route(route_id, test_data)

    if not result["success"]:
        if result["error"] == "route_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API路由不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "API路由测试失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })

    return {
        "code": 200,
        "message": "API路由测试成功",
        "data": result["data"]
    }

