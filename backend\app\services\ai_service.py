"""
AI服务层
协调各个AI服务模块，提供统一的AI功能接口
确保amis生成的schema能够正确调用已有的API，并考虑用户权限
"""
import logging
from typing import Dict, Any, Optional, List
import json

from app.core.ai.bailian_client import bailian_client
from app.core.ai.prompt_templates import prompt_manager, PromptType
from app.services.ai_code_service import ai_code_service
from app.services.ai_amis_service import ai_amis_service
from app.services.user_permission_service import user_permission_service

logger = logging.getLogger(__name__)


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.client = bailian_client
        self.code_service = ai_code_service
        self.amis_service = ai_amis_service
        self.permission_service = user_permission_service
    
    async def process_command(self, command: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理用户命令 - 核心方法
        
        Args:
            command: 用户输入的自然语言命令
            context: 上下文信息，包含用户权限、可访问API等
            
        Returns:
            处理结果，包含生成的amis schema和响应文本
        """
        try:
            logger.info(f"开始处理用户命令: {command}")
            
            # 1. 分析用户意图
            intent = await self._analyze_intent(command, context)
            if not intent.get("success"):
                return {
                    "success": False,
                    "error": "意图分析失败",
                    "details": intent
                }
            
            intent_data = intent.get("intent", {})
            logger.info(f"意图分析结果: {intent_data}")
            
            # 2. 获取用户权限信息（从context中获取或使用默认值）
            user_permissions = context.get("user_permissions", []) if context else []
            accessible_apis = context.get("accessible_apis", []) if context else []
            user_role = context.get("user_role", {"id": "guest", "name": "访客", "level": 0}) if context else {"id": "guest", "name": "访客", "level": 0}

            logger.info(f"用户权限: {len(user_permissions)} 个, 可访问API: {len(accessible_apis)} 个")
            print(f"🔍 AI服务 - 用户权限: {user_permissions}")
            print(f"🔍 AI服务 - 用户角色: {user_role}")
            print(f"🔍 AI服务 - 可访问API: {len(accessible_apis)} 个")
            
            # 3. 获取相关实体信息
            entities = await self._get_entities_for_intent(intent_data)
            
            # 4. 使用AI AMIS服务生成schema（考虑用户权限）
            schema_result = await self.amis_service.generate_schema_with_permissions(
                command=command,
                intent=intent_data,
                user_permissions=user_permissions,
                accessible_apis=accessible_apis,
                user_role=user_role,
                entities=entities
            )
            
            if not schema_result.get("success"):
                return {
                    "success": False,
                    "error": "Schema生成失败",
                    "details": schema_result
                }
            
            # 5. 返回完整结果
            from datetime import datetime
            return {
                "success": True,
                "data": {
                    "intent": intent_data,
                    "amis_schema": schema_result.get("amis_schema"),
                    "response_text": schema_result.get("response_text"),
                    "timestamp": datetime.now().isoformat(),
                    "validation": schema_result.get("validation"),
                    "user_context": {
                        "permissions": user_permissions,
                        "accessible_apis": accessible_apis,
                        "role": user_role
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"处理用户命令失败: {str(e)}")
            from datetime import datetime
            return {
                "success": False,
                "error": str(e),
                "data": {
                    "amis_schema": {
                        "type": "page",
                        "title": "处理失败",
                        "body": [
                            {
                                "type": "alert",
                                "level": "danger",
                                "body": f"命令处理失败: {str(e)}"
                            }
                        ]
                    },
                    "response_text": f"抱歉，处理您的命令时出现错误: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
            }
    
    async def _analyze_intent(self, command: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """分析用户意图"""
        try:
            # 构建意图分析提示词
            messages = prompt_manager.get_messages(
                PromptType.INTENT_ANALYSIS,
                command=command,
                context=json.dumps(context or {}, ensure_ascii=False)
            )
            
            # 调用AI分析意图
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.1,
                max_tokens=500
            )
            
            if not response.get("success"):
                # AI调用失败，使用备用意图分析
                fallback_intent = self._fallback_intent_analysis(command)
                logger.info(f"AI调用失败，使用备用意图分析: {fallback_intent}")
                return {
                    "success": True,
                    "intent": fallback_intent,
                    "fallback": True
                }
            
            # 解析意图分析结果
            try:
                intent_content = response.get("content", "{}")
                # 尝试提取JSON部分
                if "```json" in intent_content:
                    json_start = intent_content.find("```json") + 7
                    json_end = intent_content.find("```", json_start)
                    intent_content = intent_content[json_start:json_end].strip()
                elif "{" in intent_content:
                    start = intent_content.find("{")
                    intent_content = intent_content[start:]
                
                intent_data = json.loads(intent_content)
            except json.JSONDecodeError:
                # 如果解析失败，使用简单的关键词匹配
                intent_data = self._fallback_intent_analysis(command)
            
            return {
                "success": True,
                "intent": intent_data,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"意图分析失败: {str(e)}")
            # 使用备用意图分析
            fallback_intent = self._fallback_intent_analysis(command)
            logger.info(f"使用备用意图分析: {fallback_intent}")
            return {
                "success": True,
                "intent": fallback_intent,
                "fallback": True
            }
    
    def _fallback_intent_analysis(self, command: str) -> Dict[str, Any]:
        """备用意图分析（基于关键词匹配）"""
        command_lower = command.lower()

        # 先检查动作类型（优先级更高）
        if any(keyword in command_lower for keyword in ["创建", "新增", "添加"]):
            # 确定实体类型
            if any(keyword in command_lower for keyword in ["用户", "user"]):
                entity = "users"
            elif any(keyword in command_lower for keyword in ["角色", "role"]):
                entity = "roles"
            else:
                entity = "unknown"

            return {
                "intent_type": "data_create",
                "entity": entity,
                "action": "create",
                "confidence": 0.8
            }
        elif any(keyword in command_lower for keyword in ["编辑", "修改", "更新"]):
            # 确定实体类型
            if any(keyword in command_lower for keyword in ["用户", "user"]):
                entity = "users"
            elif any(keyword in command_lower for keyword in ["角色", "role"]):
                entity = "roles"
            else:
                entity = "unknown"

            return {
                "intent_type": "data_update",
                "entity": entity,
                "action": "update",
                "confidence": 0.8
            }
        elif any(keyword in command_lower for keyword in ["用户", "user"]):
            return {
                "intent_type": "data_query",
                "entity": "users",
                "action": "list",
                "confidence": 0.7
            }
        elif any(keyword in command_lower for keyword in ["角色", "role"]):
            return {
                "intent_type": "data_query",
                "entity": "roles",
                "action": "list",
                "confidence": 0.7
            }
        elif any(keyword in command_lower for keyword in ["火车票", "车票", "订票", "买票", "预订"]):
            return {
                "intent_type": "workflow_execute",
                "entity": "train_ticket",
                "action": "booking",
                "business_scenario": "train_ticket_booking",
                "confidence": 0.9
            }
        else:
            return {
                "intent_type": "general_query",
                "entity": "unknown",
                "action": "query",
                "confidence": 0.5
            }
    
    async def _get_entities_for_intent(self, intent: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取意图相关的实体信息"""
        try:
            entity_name = intent.get("entity", "")
            if not entity_name:
                return []
            
            # 这里可以从数据库或配置中获取实体信息
            # 暂时返回默认实体结构
            if entity_name == "users":
                return [{
                    "name": "users",
                    "label": "用户",
                    "fields": [
                        {"name": "id", "label": "ID", "type": "integer", "primary": True, "readonly": True},
                        {"name": "username", "label": "用户名", "type": "string", "required": True},
                        {"name": "email", "label": "邮箱", "type": "string", "required": True},
                        {"name": "real_name", "label": "真实姓名", "type": "string"},
                        {"name": "role_name", "label": "角色", "type": "string", "readonly": True},
                        {"name": "is_active", "label": "状态", "type": "boolean"},
                        {"name": "created_at", "label": "创建时间", "type": "datetime", "readonly": True}
                    ]
                }]
            elif entity_name == "roles":
                return [{
                    "name": "roles",
                    "label": "角色",
                    "fields": [
                        {"name": "id", "label": "ID", "type": "string", "primary": True, "readonly": True},
                        {"name": "name", "label": "角色名称", "type": "string", "required": True},
                        {"name": "level", "label": "级别", "type": "integer", "required": True},
                        {"name": "description", "label": "描述", "type": "string"},
                        {"name": "status", "label": "状态", "type": "string"},
                        {"name": "created_at", "label": "创建时间", "type": "datetime", "readonly": True}
                    ]
                }]
            else:
                return [{
                    "name": entity_name,
                    "label": entity_name,
                    "fields": [
                        {"name": "id", "label": "ID", "type": "integer", "primary": True, "readonly": True},
                        {"name": "name", "label": "名称", "type": "string", "required": True},
                        {"name": "description", "label": "描述", "type": "string"},
                        {"name": "created_at", "label": "创建时间", "type": "datetime", "readonly": True}
                    ]
                }]
            
        except Exception as e:
            logger.warning(f"获取实体信息失败: {str(e)}")
            return []

    # 代码生成相关方法（委托给ai_code_service）
    async def generate_code(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """生成代码"""
        return await self.code_service.generate_code(prompt, context)

    async def generate_api_code(self, entity_config: Dict[str, Any], api_type: str = "crud") -> Dict[str, Any]:
        """生成API代码"""
        return await self.code_service.generate_api_code(entity_config, api_type)

    async def generate_model_code(self, entity_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成模型代码"""
        return await self.code_service.generate_model_code(entity_config)

    async def review_generated_code(self, code: str, review_type: str = "security") -> Dict[str, Any]:
        """代码审查"""
        return await self.code_service.review_generated_code(code, review_type)

    # AI推荐相关方法
    async def suggest_entities(self, business_description: str) -> Dict[str, Any]:
        """推荐业务实体"""
        try:
            messages = prompt_manager.get_messages(
                PromptType.ENTITY_SUGGESTION,
                business_description=business_description
            )

            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )

            if response.get("success"):
                return {
                    "success": True,
                    "suggestions": response.get("content", ""),
                    "raw_response": response
                }
            else:
                return {
                    "success": False,
                    "error": response.get("error", "Unknown error")
                }
        except Exception as e:
            logger.error(f"实体推荐失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def suggest_workflows(self, entities: List[Dict[str, Any]], business_scenario: str) -> Dict[str, Any]:
        """推荐工作流"""
        try:
            messages = prompt_manager.get_messages(
                PromptType.WORKFLOW_SUGGESTION,
                entities=json.dumps(entities, ensure_ascii=False),
                business_scenario=business_scenario
            )

            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )

            if response.get("success"):
                return {
                    "success": True,
                    "suggestions": response.get("content", ""),
                    "raw_response": response
                }
            else:
                return {
                    "success": False,
                    "error": response.get("error", "Unknown error")
                }
        except Exception as e:
            logger.error(f"工作流推荐失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def suggest_apis(self, entities: List[Dict[str, Any]], operations: List[str]) -> Dict[str, Any]:
        """推荐API设计"""
        try:
            messages = prompt_manager.get_messages(
                PromptType.API_SUGGESTION,
                entities=json.dumps(entities, ensure_ascii=False),
                operations=json.dumps(operations, ensure_ascii=False)
            )

            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )

            if response.get("success"):
                return {
                    "success": True,
                    "suggestions": response.get("content", ""),
                    "raw_response": response
                }
            else:
                return {
                    "success": False,
                    "error": response.get("error", "Unknown error")
                }
        except Exception as e:
            logger.error(f"API推荐失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    # 健康检查
    async def health_check(self) -> Dict[str, Any]:
        """AI服务健康检查"""
        try:
            client_status = self.client.get_health_status()
            return {
                "success": True,
                "ai_client": client_status
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "ai_client": {"status": "error", "message": str(e)}
            }

    def get_health_status(self) -> Dict[str, Any]:
        """获取AI服务健康状态"""
        return {
            "service": "ai_service",
            "status": "healthy",
            "components": {
                "bailian_client": self.client.get_health_status(),
                "code_service": self.code_service.get_health_status(),
                "amis_service": self.amis_service.get_health_status()
            }
        }


# 全局AI服务实例
ai_service = AIService()
