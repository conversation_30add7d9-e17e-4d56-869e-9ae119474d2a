"""
AILF Backend Configuration
配置管理模块
"""
import os
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量文件
load_dotenv()


class Settings:
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "AILF Backend"
    VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "true").lower() == "true"  # 默认开启调试模式
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "5000"))
    
    # JWT配置
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "AILF_JWT_SECRET_KEY_2024_SECURE")
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_HOURS: int = 1  # 1小时有效期
    
    # 开发者认证配置
    DEVELOPER_PASSWORD: str = os.getenv("DEVELOPER_PASSWORD", "AILF_DEV_2024_SECURE")

    # 数据库配置
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: int = int(os.getenv("DB_PORT", "3306"))
    DB_USER: str = os.getenv("DB_USER", "root")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "123456")
    DB_NAME: str = os.getenv("DB_NAME", "ailf_db")

    @property
    def DATABASE_URL(self) -> str:
        """数据库连接URL"""
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset=utf8mb4"

    # CORS配置
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
    ]

    # 阿里云百炼API配置
    DASHSCOPE_API_KEY: str = os.getenv("DASHSCOPE_API_KEY", "your_api_key_here")
    DASHSCOPE_BASE_URL: str = os.getenv("DASHSCOPE_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    DASHSCOPE_MODEL: str = os.getenv("DASHSCOPE_MODEL", "qwen3-coder-plus")
    DASHSCOPE_TIMEOUT: int = int(os.getenv("DASHSCOPE_TIMEOUT", "60"))
    DASHSCOPE_MAX_RETRIES: int = int(os.getenv("DASHSCOPE_MAX_RETRIES", "3"))

    # 代码生成配置
    GENERATED_CODE_DIR: str = os.getenv("GENERATED_CODE_DIR", "./generated")
    CODE_REVIEW_DIR: str = os.getenv("CODE_REVIEW_DIR", "./code_review")
    APPROVED_CODE_DIR: str = os.getenv("APPROVED_CODE_DIR", "./approved_code")


# 全局配置实例
settings = Settings()
