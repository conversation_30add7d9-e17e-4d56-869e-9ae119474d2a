```json
{
  "type": "page",
  "title": "商品管理",
  "body": [
    {
      "type": "crud",
      "api": "/api/products",
      "syncLocation": false,
      "filter": {
        "title": "搜索",
        "submitText": "",
        "controls": [
          {
            "type": "text",
            "name": "name",
            "label": "商品名称",
            "placeholder": "请输入商品名称"
          },
          {
            "type": "text",
            "name": "category",
            "label": "分类",
            "placeholder": "请输入分类"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "type": "text"
        },
        {
          "name": "name",
          "label": "商品名称",
          "type": "text"
        },
        {
          "name": "price",
          "label": "价格",
          "type": "text"
        },
        {
          "name": "category",
          "label": "分类",
          "type": "text"
        },
        {
          "type": "operation",
          "label": "操作",
          "buttons": [
            {
              "type": "button",
              "actionType": "dialog",
              "label": "查看",
              "dialog": {
                "title": "查看详情",
                "body": {
                  "type": "form",
                  "controls": [
                    {
                      "type": "static",
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "type": "static",
                      "name": "name",
                      "label": "商品名称"
                    },
                    {
                      "type": "static",
                      "name": "price",
                      "label": "价格"
                    },
                    {
                      "type": "static",
                      "name": "category",
                      "label": "分类"
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "actionType": "dialog",
              "label": "编辑",
              "dialog": {
                "title": "编辑商品",
                "body": {
                  "type": "form",
                  "api": "/api/products/${id}",
                  "controls": [
                    {
                      "type": "hidden",
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "type": "text",
                      "name": "name",
                      "label": "商品名称",
                      "required": true
                    },
                    {
                      "type": "number",
                      "name": "price",
                      "label": "价格",
                      "required": true
                    },
                    {
                      "type": "text",
                      "name": "category",
                      "label": "分类",
                      "required": true
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "actionType": "ajax",
              "label": "删除",
              "confirmText": "确定要删除该商品？",
              "api": "delete:/api/products/${id}"
            }
          ]
        }
      ],
      "headerToolbar": [
        {
          "type": "button",
          "actionType": "dialog",
          "label": "新增商品",
          "icon": "fa fa-plus",
          "dialog": {
            "title": "新增商品",
            "body": {
              "type": "form",
              "api": "/api/products",
              "controls": [
                {
                  "type": "text",
                  "name": "name",
                  "label": "商品名称",
                  "required": true
                },
                {
                  "type": "number",
                  "name": "price",
                  "label": "价格",
                  "required": true
                },
                {
                  "type": "text",
                  "name": "category",
                  "label": "分类",
                  "required": true
                }
              ]
            }
          }
        },
        "bulkActions",
        "pagination"
      ],
      "footerToolbar": [
        "statistics",
        "pagination"
      ]
    }
  ]
}
```