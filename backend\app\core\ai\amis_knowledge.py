"""
AMIS框架知识库
基于官方文档 https://aisuda.bce.baidu.com/amis/zh-CN/docs/index
提供amis组件的详细配置知识和最佳实践
"""
from typing import Dict, Any, List, Optional, Union
from enum import Enum
import re
import json


class AmisComponentType(Enum):
    """AMIS组件类型枚举 - 基于官方文档完整分类"""

    # 布局组件
    PAGE = "page"
    CONTAINER = "container"
    COLLAPSE = "collapse"
    DIVIDER = "divider"
    FLEX = "flex"
    GRID = "grid"
    GRID_2D = "grid-2d"
    HBOX = "hbox"
    PANEL = "panel"
    TABS = "tabs"
    WRAPPER = "wrapper"
    PORTLET = "portlet"

    # 功能组件
    ACTION = "action"
    APP = "app"
    BUTTON = "button"
    BUTTON_GROUP = "button-group"
    BREADCRUMB = "breadcrumb"
    CUSTOM = "custom"
    DROPDOWN_BUTTON = "dropdown-button"
    SERVICE = "service"
    NAV = "nav"
    ANCHOR_NAV = "anchor-nav"
    TOOLTIP_WRAPPER = "tooltip"
    POPOVER = "popover"

    # 数据输入组件
    FORM = "form"
    INPUT_TEXT = "input-text"
    TEXTAREA = "textarea"
    INPUT_NUMBER = "input-number"
    INPUT_PASSWORD = "input-password"
    SELECT = "select"
    CHECKBOX = "checkbox"
    CHECKBOXES = "checkboxes"
    RADIO = "radio"
    RADIOS = "radios"
    SWITCH = "switch"
    INPUT_DATE = "input-date"
    INPUT_DATETIME = "input-datetime"
    INPUT_TIME = "input-time"
    INPUT_FILE = "input-file"
    INPUT_IMAGE = "input-image"
    INPUT_RICH_TEXT = "input-rich-text"
    EDITOR = "editor"
    COMBO = "combo"
    INPUT_TABLE = "input-table"
    PICKER = "picker"
    TRANSFER = "transfer"
    INPUT_TREE = "input-tree"
    TREE_SELECT = "treeselect"
    INPUT_TAG = "input-tag"
    INPUT_RATING = "input-rating"
    INPUT_RANGE = "input-range"
    INPUT_COLOR = "input-color"
    INPUT_CITY = "input-city"
    LOCATION_PICKER = "location-picker"
    UUID = "uuid"
    HIDDEN = "hidden"
    STATIC = "static"

    # 数据展示组件
    CRUD = "crud"
    TABLE = "table"
    TABLE2 = "table2"
    TABLE_VIEW = "table-view"
    CALENDAR = "calendar"
    CARD = "card"
    CARDS = "cards"
    CAROUSEL = "carousel"
    CHART = "chart"
    CODE = "code"
    COLOR = "color"
    DATE = "date"
    EACH = "each"
    HTML = "html"
    ICON = "icon"
    IMAGE = "image"
    IMAGES = "images"
    JSON = "json"
    LINK = "link"
    LIST = "list"
    LOG = "log"
    MAPPING = "mapping"
    MARKDOWN = "markdown"
    PROGRESS = "progress"
    QR_CODE = "qr-code"
    SPARKLINE = "sparkline"
    STATUS = "status"
    STEPS = "steps"
    TABLE_CELL = "table-cell"
    TAGINPUT = "tag"
    TIMELINE = "timeline"
    SHAPE = "shape"

    # 反馈组件
    ALERT = "alert"
    DIALOG = "dialog"
    DRAWER = "drawer"
    SPINNER = "spinner"
    TOAST = "toast"

    # 其他组件
    AVATAR = "avatar"
    AUDIO = "audio"
    TASKS = "tasks"
    BADGE = "badge"
    WIZARD = "wizard"
    WEB_COMPONENT = "web-component"
    AMIS = "amis"


class AmisFormItemType(Enum):
    """AMIS表单项类型枚举 - 基于官方文档完整分类"""

    # 基础输入
    INPUT_TEXT = "input-text"
    TEXTAREA = "textarea"
    INPUT_NUMBER = "input-number"
    INPUT_PASSWORD = "input-password"
    HIDDEN = "hidden"
    STATIC = "static"

    # 选择器
    SELECT = "select"
    CHECKBOX = "checkbox"
    CHECKBOXES = "checkboxes"
    RADIO = "radio"
    RADIOS = "radios"
    CHART_RADIOS = "chart-radios"
    SWITCH = "switch"
    BUTTON_GROUP_SELECT = "button-group-select"
    CHAINED_SELECT = "chain-select"
    NESTED_SELECT = "nestedselect"
    LIST_SELECT = "list-select"
    MATRIX_CHECKBOXES = "matrix-checkboxes"

    # 日期时间
    INPUT_DATE = "input-date"
    INPUT_DATETIME = "input-datetime"
    INPUT_TIME = "input-time"
    INPUT_MONTH = "input-month"
    INPUT_QUARTER = "input-quarter"
    INPUT_YEAR = "input-year"
    INPUT_DATE_RANGE = "input-date-range"
    INPUT_DATETIME_RANGE = "input-datetime-range"
    INPUT_TIME_RANGE = "input-time-range"
    INPUT_MONTH_RANGE = "input-month-range"
    INPUT_QUARTER_RANGE = "input-quarter-range"
    INPUT_YEAR_RANGE = "input-year-range"

    # 文件上传
    INPUT_FILE = "input-file"
    INPUT_IMAGE = "input-image"
    INPUT_EXCEL = "input-excel"

    # 富文本编辑
    INPUT_RICH_TEXT = "input-rich-text"
    EDITOR = "editor"
    DIFF_EDITOR = "diff-editor"

    # 复合组件
    COMBO = "combo"
    INPUT_TABLE = "input-table"
    INPUT_SUB_FORM = "input-sub-form"
    INPUT_ARRAY = "input-array"

    # 选择器高级
    PICKER = "picker"
    TRANSFER = "transfer"
    TRANSFER_PICKER = "transfer-picker"
    TABS_TRANSFER = "tabs-transfer"
    TABS_TRANSFER_PICKER = "tabs-transfer-picker"
    INPUT_TREE = "input-tree"
    TREE_SELECT = "treeselect"

    # 特殊输入
    INPUT_TAG = "input-tag"
    INPUT_RATING = "input-rating"
    INPUT_RANGE = "input-range"
    INPUT_COLOR = "input-color"
    INPUT_CITY = "input-city"
    LOCATION_PICKER = "location-picker"
    INPUT_KV = "input-kv"
    INPUT_KVS = "input-kvs"
    INPUT_FORMULA = "input-formula"
    INPUT_SIGNATURE = "input-signature"
    INPUT_VERIFICATION_CODE = "input-verification-code"
    INPUT_REPEAT = "input-repeat"

    # 工具组件
    UUID = "uuid"
    FORMULA = "formula"
    CONDITION_BUILDER = "condition-builder"
    FIELDSET = "fieldset"
    GROUP = "group"
    INPUT_GROUP = "input-group"
    BUTTON_TOOLBAR = "button-toolbar"

    # JSON Schema
    JSON_SCHEMA = "json-schema"
    JSON_SCHEMA_EDITOR = "json-schema-editor"


class AmisKnowledgeBase:
    """amis框架知识库"""

    def __init__(self):
        self.component_templates = self._load_component_templates()
        self.best_practices = self._load_best_practices()
        self.common_patterns = self._load_common_patterns()

    def _load_component_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载组件模板"""
        return {
            AmisComponentType.PAGE.value: {
                "basic": {
                    "type": "page",
                    "title": "页面标题",
                    "body": []
                },
                "with_toolbar": {
                    "type": "page",
                    "title": "页面标题",
                    "toolbar": [
                        {
                            "type": "button",
                            "label": "新增",
                            "level": "primary",
                            "actionType": "dialog",
                            "dialog": {
                                "title": "新增记录",
                                "body": []
                            }
                        }
                    ],
                    "body": []
                }
            },

            AmisComponentType.FORM.value: {
                "basic": {
                    "type": "form",
                    "api": "POST /api/submit",
                    "body": []
                },
                "with_validation": {
                    "type": "form",
                    "api": "POST /api/submit",
                    "mode": "horizontal",
                    "labelWidth": 100,
                    "body": [],
                    "actions": [
                        {
                            "type": "submit",
                            "label": "提交",
                            "level": "primary"
                        },
                        {
                            "type": "reset",
                            "label": "重置"
                        }
                    ]
                }
            },

            AmisComponentType.CRUD.value: {
                "table": {
                    "type": "crud",
                    "api": "/api/list",
                    "mode": "table",
                    "columns": [],
                    "headerToolbar": [
                        {
                            "type": "button",
                            "label": "新增",
                            "level": "primary",
                            "actionType": "dialog",
                            "dialog": {
                                "title": "新增记录",
                                "body": {
                                    "type": "form",
                                    "api": "POST /api/create",
                                    "body": []
                                }
                            }
                        }
                    ],
                    "footerToolbar": ["pagination"]
                },
                "with_filter": {
                    "type": "crud",
                    "api": "/api/list",
                    "mode": "table",
                    "filter": {
                        "type": "form",
                        "mode": "inline",
                        "body": []
                    },
                    "columns": [],
                    "headerToolbar": [
                        "filter-toggler",
                        {
                            "type": "button",
                            "label": "新增",
                            "level": "primary"
                        }
                    ]
                }
            }
        }

    def _load_best_practices(self) -> Dict[str, List[str]]:
        """加载最佳实践"""
        return {
            "form_design": [
                "根据字段类型选择合适的表单项组件",
                "为必填字段添加required验证",
                "使用合适的placeholder提示用户输入",
                "对于长表单考虑分组或分步骤",
                "添加实时验证提升用户体验",
                "使用visibleOn实现字段联动显示"
            ],
            "crud_design": [
                "根据数据量选择合适的展示模式",
                "为重要操作添加确认提示",
                "使用批量操作提高效率",
                "添加搜索和筛选功能",
                "合理设置分页大小",
                "考虑移动端的响应式设计"
            ],
            "api_integration": [
                "统一API响应格式：{status: 0, msg: '', data: {}}",
                "使用合适的HTTP方法",
                "添加加载状态和错误处理",
                "考虑数据缓存和刷新策略",
                "实现乐观更新提升体验"
            ],
            "user_experience": [
                "使用合适的组件层次和布局",
                "添加适当的动画和过渡效果",
                "提供清晰的操作反馈",
                "考虑无障碍访问需求",
                "优化加载性能"
            ]
        }

    def _load_common_patterns(self) -> Dict[str, Dict[str, Any]]:
        """加载常用模式"""
        return {
            "master_detail": {
                "description": "主从表模式",
                "template": {
                    "type": "page",
                    "body": [
                        {
                            "type": "grid",
                            "columns": [
                                {
                                    "md": 4,
                                    "body": [
                                        {
                                            "type": "crud",
                                            "name": "master",
                                            "api": "/api/master/list",
                                            "columns": []
                                        }
                                    ]
                                },
                                {
                                    "md": 8,
                                    "body": [
                                        {
                                            "type": "crud",
                                            "api": "/api/detail/list?master_id=${master.id}",
                                            "columns": []
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            }
        }

    def get_component_template(self, component_type: AmisComponentType, variant: str = "basic") -> Dict[str, Any]:
        """获取组件模板"""
        templates = self.component_templates.get(component_type.value, {})
        return templates.get(variant, {})

    def get_form_item_config(self, field_type: str, field_name: str, field_label: str, **kwargs) -> Dict[str, Any]:
        """获取表单项配置"""
        base_config = {
            "name": field_name,
            "label": field_label
        }

        # 根据字段类型设置默认配置
        if field_type in ["string", "text"]:
            base_config.update({
                "type": "input-text",
                "placeholder": f"请输入{field_label}"
            })
        elif field_type == "textarea":
            base_config.update({
                "type": "textarea",
                "placeholder": f"请输入{field_label}",
                "minRows": 3
            })
        elif field_type == "select":
            base_config.update({
                "type": "select",
                "placeholder": f"请选择{field_label}",
                "options": kwargs.get("options", [])
            })
        elif field_type == "date":
            base_config.update({
                "type": "input-date",
                "format": "YYYY-MM-DD"
            })
        elif field_type == "datetime":
            base_config.update({
                "type": "input-datetime",
                "format": "YYYY-MM-DD HH:mm:ss"
            })
        elif field_type == "boolean":
            base_config.update({
                "type": "switch",
                "trueValue": True,
                "falseValue": False
            })
        elif field_type == "number":
            base_config.update({
                "type": "input-number",
                "placeholder": f"请输入{field_label}"
            })
        elif field_type == "file":
            base_config.update({
                "type": "input-file",
                "accept": "*",
                "maxSize": 10485760  # 10MB
            })

        # 应用额外配置
        base_config.update(kwargs)

        return base_config

    def get_crud_column_config(self, field_name: str, field_label: str, field_type: str, **kwargs) -> Dict[str, Any]:
        """获取CRUD列配置"""
        base_config = {
            "name": field_name,
            "label": field_label,
            "sortable": kwargs.get("sortable", False)
        }

        # 根据字段类型设置列类型
        if field_type == "date":
            base_config.update({
                "type": "date",
                "format": "YYYY-MM-DD"
            })
        elif field_type == "datetime":
            base_config.update({
                "type": "datetime",
                "format": "YYYY-MM-DD HH:mm:ss"
            })
        elif field_type == "boolean":
            base_config.update({
                "type": "status",
                "map": {
                    "true": {"label": "是", "status": "success"},
                    "false": {"label": "否", "status": "default"}
                }
            })
        elif field_type == "image":
            base_config.update({
                "type": "image",
                "enlargeAble": True
            })
        elif field_type == "link":
            base_config.update({
                "type": "link",
                "href": kwargs.get("href", "#")
            })

        # 应用额外配置
        base_config.update(kwargs)

        return base_config

    def get_best_practices(self, category: str) -> List[str]:
        """获取最佳实践建议"""
        return self.best_practices.get(category, [])

    def get_common_pattern(self, pattern_name: str) -> Dict[str, Any]:
        """获取常用模式"""
        return self.common_patterns.get(pattern_name, {})

    def generate_api_config(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """生成API配置"""
        config = {
            "method": method.upper(),
            "url": url
        }

        # 添加认证头部
        if not kwargs.get("headers"):
            kwargs["headers"] = {}

        # 自动添加Authorization头部
        kwargs["headers"]["Authorization"] = "Bearer ${ls:access_token}"

        if kwargs.get("data"):
            config["data"] = kwargs["data"]

        if kwargs.get("headers"):
            config["headers"] = kwargs["headers"]

        if kwargs.get("sendOn"):
            config["sendOn"] = kwargs["sendOn"]

        # 添加错误处理
        if "onError" not in config:
            config["onError"] = {
                "type": "toast",
                "position": "top-right",
                "title": "请求失败",
                "body": "${responseMsg || '请求失败，请稍后重试'}"
            }

        return config

    def generate_crud_with_permissions(self, entity_name: str, accessible_apis: List[Dict[str, Any]],
                                     entity_fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根据用户权限生成CRUD组件"""
        # 检查用户可访问的API
        can_list = any(api.get("resource") == entity_name and api.get("action") == "list" for api in accessible_apis)
        can_create = any(api.get("resource") == entity_name and api.get("action") == "create" for api in accessible_apis)
        can_update = any(api.get("resource") == entity_name and api.get("action") == "update" for api in accessible_apis)
        can_delete = any(api.get("resource") == entity_name and api.get("action") == "delete" for api in accessible_apis)

        if not can_list:
            return {
                "type": "alert",
                "level": "warning",
                "body": "您没有权限查看此数据"
            }

        # 构建CRUD配置
        crud_config = {
            "type": "crud",
            "api": self.generate_api_config("GET", f"/api/{entity_name.lower()}"),
            "columns": self._generate_columns_from_fields(entity_fields),
            "headerToolbar": [],
            "footerToolbar": ["pagination"]
        }

        # 根据权限添加操作按钮
        if can_create:
            crud_config["headerToolbar"].append({
                "type": "button",
                "label": "新增",
                "level": "primary",
                "actionType": "dialog",
                "dialog": {
                    "title": f"新增{entity_name}",
                    "size": "lg",
                    "body": {
                        "type": "form",
                        "api": self.generate_api_config("POST", f"/api/{entity_name.lower()}"),
                        "body": self._generate_form_items_from_fields(entity_fields, exclude_readonly=True)
                    }
                }
            })

        # 添加行操作
        actions = []
        if can_update:
            actions.append({
                "type": "button",
                "label": "编辑",
                "level": "link",
                "actionType": "dialog",
                "dialog": {
                    "title": f"编辑{entity_name}",
                    "size": "lg",
                    "body": {
                        "type": "form",
                        "api": self.generate_api_config("PUT", f"/api/{entity_name.lower()}/${{id}}"),
                        "initApi": self.generate_api_config("GET", f"/api/{entity_name.lower()}/${{id}}"),
                        "body": self._generate_form_items_from_fields(entity_fields, exclude_readonly=True)
                    }
                }
            })

        if can_delete:
            actions.append({
                "type": "button",
                "label": "删除",
                "level": "link",
                "className": "text-danger",
                "actionType": "ajax",
                "api": self.generate_api_config("DELETE", f"/api/{entity_name.lower()}/${{id}}"),
                "confirmText": f"确定要删除这条{entity_name}记录吗？"
            })

        if actions:
            crud_config["columns"].append({
                "type": "operation",
                "label": "操作",
                "buttons": actions
            })

        return crud_config

    def _generate_columns_from_fields(self, fields: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从字段生成表格列"""
        columns = []
        for field in fields:
            if field.get("primary") or field.get("readonly"):
                continue  # 跳过主键和只读字段在列表中显示

            column = self.get_crud_column_config(
                field["name"],
                field["label"],
                field["type"],
                sortable=True
            )
            columns.append(column)

        return columns

    def _generate_form_items_from_fields(self, fields: List[Dict[str, Any]], exclude_readonly: bool = False) -> List[Dict[str, Any]]:
        """从字段生成表单项"""
        form_items = []
        for field in fields:
            if exclude_readonly and (field.get("readonly") or field.get("primary")):
                continue

            form_item = self.get_form_item_config(
                field["type"],
                field["name"],
                field["label"],
                required=field.get("required", False),
                options=field.get("options", [])
            )
            form_items.append(form_item)

        return form_items


# 全局amis知识库实例
amis_knowledge = AmisKnowledgeBase()