# AILF 文档中心

## 📋 概述

AILF (AI Language Frontend) 是一个通用业务场景配置平台，支持零代码创建任意业务系统，从简单的电商管理到复杂的工作流系统。

## 📚 文档体系

### 🏗️ 核心架构文档

#### [前端架构文档](./FRONTEND_ARCHITECTURE.md)
- **开发者配置界面** - 八步骤配置流程、可视化设计器
- **最终用户界面** - AI语音交互、amis动态界面
- **技术栈**: React + TypeScript + Ant Design + React Flow + amis
- **核心组件**: 实体建模器、工作流设计器、表单设计器

#### [后端架构文档](./BACKEND_ARCHITECTURE.md)
- **整体架构** - API层、服务层、核心层、数据层
- **核心流程** - 配置流程、代码生成流程、AI交互流程
- **前后端对接** - API规范、认证权限、动态路由
- **技术栈**: FastAPI + SQLite + 阿里云百炼API

#### [API概览文档](./API_OVERVIEW.md)
- **85个API端点** - 核心功能完整覆盖
- **模块分类** - 认证、场景、实体、工作流、表单、权限等
- **功能说明** - 每个API的作用和用途
- **使用流程** - 开发者配置流程、最终用户使用流程

### 📋 详细技术文档

#### [详细API文档](./api/)
AILF系统的完整RESTful API文档，按模块分类：

**核心配置模块**
- [1、认证模块](./api/1、认证模块.md) - 开发者身份验证和令牌管理
- [2、场景管理模块](./api/2、场景管理模块.md) - 业务场景配置和管理
- [3、模板管理模块](./api/3、模板管理模块.md) - 预设模板查询和使用

**业务建模模块**
- [4、实体建模模块](./api/4、实体建模模块.md) - 业务实体定义和数据管理
- [5、工作流设计模块](./api/5、工作流设计模块.md) - 业务流程设计和执行
- [6、表单配置模块](./api/6、表单配置模块.md) - 动态表单配置和渲染

**系统管理模块**
- [7、API路由管理模块](./api/7、API路由管理模块.md) - 动态API路由注册和管理
- [8、角色管理模块](./api/8、角色管理模块.md) - 用户角色定义和权限分配
- [9、权限控制模块](./api/9、权限控制模块.md) - RBAC权限检查和配置

**功能扩展模块**
- [10、代码生成模块](./api/10、代码生成模块.md) - 完整系统代码自动生成
- [11、AI交互模块](./api/11、AI交互模块.md) - 自然语言处理和智能推荐
- [12、系统健康检查模块](./api/12、系统健康检查模块.md) - 系统监控和状态检查

#### [完整开发配置规划](./COMPLETE_DEVELOPMENT_CONFIGURATION_PLAN.md)
- **通用业务建模** - 支持任意业务场景
- **八步骤配置** - 从场景配置到系统生成
- **实现路线图** - 分阶段开发计划
- **数据模型扩展** - 完整的类型定义

## 🎯 文档使用指南

### 👨‍💻 开发者阅读路径

1. **了解整体架构**
   - 先读 [API概览文档](./API_OVERVIEW.md) 了解系统全貌
   - 再读 [前端架构文档](./FRONTEND_ARCHITECTURE.md) 和 [后端架构文档](./BACKEND_ARCHITECTURE.md)

2. **开始开发**
   - 查看 [完整API文档](./API_DOCUMENTATION.md) 了解接口详情
   - 参考 [完整开发配置规划](./COMPLETE_DEVELOPMENT_CONFIGURATION_PLAN.md) 进行功能扩展
### 🏢 产品经理阅读路径

1. **了解产品能力**
   - 读 [API概览文档](./API_OVERVIEW.md) 了解功能范围
   - 读 [完整开发配置规划](./COMPLETE_DEVELOPMENT_CONFIGURATION_PLAN.md) 了解产品愿景

2. **规划产品功能**
   - 参考八步骤配置流程设计用户体验
   - 基于通用业务建模能力规划应用场景

### 🎨 UI/UX设计师阅读路径

1. **了解界面架构**
   - 重点读 [前端架构文档](./FRONTEND_ARCHITECTURE.md)
   - 了解开发者配置界面和最终用户界面的设计

2. **设计界面交互**
   - 参考可视化设计器的交互模式
   - 基于amis schema设计动态界面

## 🚀 AILF核心能力

### ✨ 通用业务建模
- **任意业务场景** - 不局限于预设模板
- **实体关系设计** - 可视化数据建模
- **字段类型丰富** - 支持20+种字段类型

### 🔄 可视化工作流
- **拖拽式设计** - React Flow工作流画布
- **复杂业务流程** - 支持条件分支、并行处理
- **实时执行** - 工作流引擎驱动业务流程

### 📝 动态表单生成
- **零代码配置** - 可视化表单设计器
- **智能验证** - 自动生成验证规则
- **响应式布局** - 自适应各种设备

### 🤖 AI驱动界面
- **语音交互** - 自然语言生成界面
- **智能推荐** - AI辅助配置优化
- **动态渲染** - amis schema实时生成

### 🔐 完整权限体系
- **角色管理** - 灵活的角色定义
- **权限矩阵** - 直观的权限配置
- **API级权限** - 精细化访问控制

### 🚀 一键代码生成
- **完整系统** - 前后端代码自动生成
- **数据库结构** - 根据实体自动建表
- **API接口** - 动态路由自动注册

## 🎯 应用场景

### 🛒 电商系统
- 商品管理、订单处理、用户管理
- 购物流程、支付流程、物流跟踪

### 🏥 医院管理
- 患者管理、医生排班、预约挂号
- 就诊流程、检查流程、药品管理

### 🍽️ 餐厅管理
- 菜品管理、订单管理、桌台管理
- 点餐流程、制作流程、结账流程

### 🚄 票务系统
- 车次管理、座位管理、用户管理
- 查询流程、预订流程、退票流程

### 🏋️ 健身房管理
- 会员管理、教练管理、课程管理
- 预约流程、签到流程、续费流程

## 📈 技术优势

- **零代码配置** - 业务人员也能快速上手
- **高度可扩展** - 支持任意业务场景扩展
- **AI智能化** - 语音交互提升用户体验
- **快速交付** - 从配置到上线只需几小时
- **成本低廉** - 大幅降低开发和维护成本

## 📝 版本信息

- **当前版本**: v2.0.0 (规划版本)
- **上次更新**: 2024-01-20
- **文档维护**: AILF开发团队

---

💡 **提示**: 建议按照上述阅读路径循序渐进地阅读文档，这样能更好地理解AILF的整体架构和设计理念。
│   开发者配置     │    │   权限管理      │    │   业务API      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- **前端**: Node.js 16+, React 18+, TypeScript
- **后端**: Python 3.8+, FastAPI, uvicorn
- **AI服务**: 阿里云百炼API

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ailf
```

2. **安装前端依赖**
```bash
cd frontend
pnpm install
```

3. **安装后端依赖**
```bash
cd backend
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 后端配置
cp .env.example .env
# 编辑 .env 文件，配置阿里云API密钥
```

5. **启动服务**
```bash
# 启动后端 (端口5000)
cd backend
python main.py

# 启动前端 (端口3000)
cd frontend
pnpm start
```

### 访问地址

- **主应用**: http://localhost:3000
- **开发者配置**: http://localhost:3000/config
- **API文档**: http://localhost:5000/docs

## ⚙️ 开发者配置

### 四步骤配置流程

1. **场景配置** - 选择业务模板或自定义场景
2. **API配置** - 配置业务接口信息
3. **角色配置** - 定义用户角色和权限级别
4. **权限设置** - 配置角色-API访问权限矩阵

### 预设模板

- **电商系统** - 商品管理、订单处理、用户管理
- **体育馆管理** - 会员管理、课程预约、设备管理
- **票务系统** - 车票查询、预订、退票管理

### 权限矩阵

```
API接口          │ 管理员 │ 会员 │ 顾客
商品列表 GET     │   ✓   │  ✓  │  ✓
创建订单 POST    │   ✓   │  ✓  │  ✓
管理商品 PUT     │   ✓   │  ✗  │  ✗
删除订单 DELETE  │   ✓   │  ✗  │  ✗
```

## 🎤 用户交互

### 语音交互示例

```
用户: "我想查看商品列表"
AI: 为您显示商品列表页面
界面: 自动生成商品列表表格

用户: "帮我创建一个新订单"
AI: 为您打开订单创建表单
界面: 自动生成订单表单页面

用户: "显示我的个人信息"
AI: 为您显示个人信息页面
界面: 自动生成用户信息卡片
```

### 支持的交互方式

- 🎤 **语音输入** - 点击麦克风按钮说话
- ⌨️ **文本输入** - 直接输入文字命令
- 🔄 **连续对话** - 支持多轮对话交互
- 📱 **触摸操作** - 生成的界面支持正常操作

## 🔐 权限系统

### 认证流程

1. **开发者认证** - 密码: `AILF_DEV_2024_SECURE`
2. **用户认证** - 基于JWT的用户登录
3. **权限验证** - 基于RBAC的API访问控制

### 角色权限

- **管理员** - 完全访问权限
- **经理** - 部门管理权限
- **会员** - 基础操作权限
- **顾客** - 只读访问权限

## 📚 技术栈

### 前端技术

- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - UI组件库
- **amis** - 动态表单和页面渲染
- **axios** - HTTP客户端

### 后端技术

- **FastAPI** - 现代Python Web框架
- **uvicorn** - ASGI服务器
- **pydantic** - 数据验证
- **JWT** - 身份认证
- **阿里云百炼** - AI语言模型

### 开发工具

- **pnpm** - 包管理器
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Git** - 版本控制

## 📖 文档

- **[API文档](./API_DOCUMENTATION.md)** - 完整的API接口文档
- **[开发指南](../README.md)** - 项目开发说明
- **[部署指南](../deployment.md)** - 生产环境部署

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看 [API文档](./API_DOCUMENTATION.md)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 🎯 路线图

### v1.1 计划
- [ ] 支持更多AI模型
- [ ] 增加组件库扩展
- [ ] 支持多语言界面
- [ ] 添加数据可视化

### v2.0 计划
- [ ] 支持插件系统
- [ ] 增加工作流引擎
- [ ] 支持移动端APP
- [ ] 云端部署方案

---

**AILF** - 让AI理解你的需求，为你生成完美的界面 🚀
