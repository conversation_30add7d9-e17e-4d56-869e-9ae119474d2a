"""
AILF Backend FastAPI应用入口
"""
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from datetime import datetime
import traceback

from app.config import settings
from app.api.v1.auth import router as auth_router
from app.api.v1.user_auth import router as user_auth_router
from app.api.v1.scenario import router as scenario_router
from app.api.v1.template import router as template_router
from app.api.v1.entity import router as entity_router
from app.api.v1.relationship import router as relationship_router
from app.api.v1.ai import router as ai_router
from app.api.v1.generate import router as generate_router
from app.api.v1.code_review import router as code_review_router
from app.api.workflow import router as workflow_router
from app.api.form import router as form_router
from app.api.api_route import router as api_route_router
from app.api.role import router as role_router
from app.api.permission_control import router as permission_control_router
from app.api.business import router as business_router
from app.core.dynamic_router import init_dynamic_router
from app.core.role_db_init import init_role_database
from app.core.permission_control_db_init import init_permission_control_database
from app.core.user_db_init import init_user_database
from app.core.database import init_database, db_manager


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""

    # 初始化数据库
    if not init_database():
        raise RuntimeError("数据库初始化失败")

    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.VERSION,
        description="AILF (AI Language Frontend) 后端API服务",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 初始化动态路由管理器
    init_dynamic_router(app)

    # 初始化角色管理数据库
    print("🔧 初始化角色管理数据库...")
    init_role_database()

    # 初始化权限控制数据库
    print("🔧 初始化权限控制数据库...")
    init_permission_control_database()

    # 初始化用户认证数据库
    print("🔧 初始化用户认证数据库...")
    init_user_database()

    # 注册路由 (工作流和表单路由需要在前面，避免路径冲突)
    app.include_router(workflow_router)
    app.include_router(form_router, prefix="/api")
    app.include_router(api_route_router)
    app.include_router(role_router)
    app.include_router(permission_control_router)
    app.include_router(business_router, prefix="/api")
    app.include_router(auth_router, prefix="/api")
    app.include_router(user_auth_router)  # 用户认证路由已包含prefix
    app.include_router(scenario_router, prefix="/api")
    app.include_router(template_router, prefix="/api")
    app.include_router(entity_router, prefix="/api")
    app.include_router(relationship_router, prefix="/api")
    app.include_router(ai_router, prefix="/api")  # AI路由
    app.include_router(generate_router)  # 代码生成路由已包含prefix
    app.include_router(code_review_router)  # 代码审查路由已包含prefix
    
    # 全局异常处理器
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.detail
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """数据验证异常处理器"""
        print(f"🔍 收到422验证错误: {request.method} {request.url}")
        print(f"📝 验证错误详情: {exc.errors()}")
        print(f"📊 请求体: {await request.body()}")

        return JSONResponse(
            status_code=422,
            content={
                "code": 422,
                "message": "数据验证失败",
                "data": {
                    "error": "validation_error",
                    "details": exc.errors()
                },
                "timestamp": datetime.now().isoformat()
            }
        )

    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """全局异常处理器"""
        import traceback
        error_details = f"{str(exc)}\n{traceback.format_exc()}" if settings.DEBUG else "服务器内部错误"

        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": "内部服务器错误",
                "data": {
                    "error": "internal_server_error",
                    "details": error_details
                },
                "timestamp": datetime.now().isoformat()
            }
        )
    
    # 健康检查接口
    @app.get("/health", tags=["健康检查"])
    async def health_check():
        """健康检查接口"""
        return {
            "code": 200,
            "message": "服务正常",
            "data": {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": settings.VERSION
            }
        }

    @app.get("/health/database", tags=["健康检查"])
    async def database_health_check():
        """数据库健康检查端点"""
        import time
        health = db_manager.health_check()
        connection_info = db_manager.get_connection_info()

        return {
            "code": 200,
            "message": "数据库状态检查",
            "data": {
                "database": health,
                "connection_pool": connection_info,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
            }
        }
    
    # 根路径
    @app.get("/", tags=["根路径"])
    async def root():
        """根路径接口"""
        return {
            "code": 200,
            "message": "AILF Backend API",
            "data": {
                "name": settings.APP_NAME,
                "version": settings.VERSION,
                "docs": "/docs",
                "redoc": "/redoc"
            }
        }
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
