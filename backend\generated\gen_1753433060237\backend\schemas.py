```python
from typing import List, Optional
from decimal import Decimal
from pydantic import BaseModel, Field, validator


class ProductBase(BaseModel):
    """
    商品基础模型
    """
    name: str = Field(
        ...,
        title="商品名称",
        description="商品的名称",
        example="苹果手机"
    )
    price: Decimal = Field(
        ...,
        title="价格",
        description="商品的价格",
        example=999.99,
        gt=0
    )
    category: str = Field(
        ...,
        title="分类",
        description="商品的分类",
        example="电子产品"
    )

    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('商品名称不能为空')
        return v.strip()

    @validator('category')
    def category_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('分类不能为空')
        return v.strip()


class ProductCreate(ProductBase):
    """
    创建商品模型
    """
    pass


class ProductUpdate(BaseModel):
    """
    更新商品模型
    """
    name: Optional[str] = Field(
        None,
        title="商品名称",
        description="商品的名称",
        example="苹果手机"
    )
    price: Optional[Decimal] = Field(
        None,
        title="价格",
        description="商品的价格",
        example=999.99,
        gt=0
    )
    category: Optional[str] = Field(
        None,
        title="分类",
        description="商品的分类",
        example="电子产品"
    )

    @validator('name')
    def name_must_not_be_empty(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('商品名称不能为空')
        return v.strip() if v else v

    @validator('category')
    def category_must_not_be_empty(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('分类不能为空')
        return v.strip() if v else v


class ProductResponse(ProductBase):
    """
    商品响应模型
    """
    id: int = Field(
        ...,
        title="ID",
        description="商品的唯一标识符",
        example=1
    )

    class Config:
        orm_mode = True
        schema_extra = {
            "example": {
                "id": 1,
                "name": "苹果手机",
                "price": 999.99,
                "category": "电子产品"
            }
        }


class ProductListResponse(BaseModel):
    """
    商品列表响应模型
    """
    products: List[ProductResponse] = Field(
        ...,
        title="商品列表",
        description="商品信息列表"
    )
    total: int = Field(
        ...,
        title="总数",
        description="商品总数量",
        example=100
    )

    class Config:
        schema_extra = {
            "example": {
                "products": [
                    {
                        "id": 1,
                        "name": "苹果手机",
                        "price": 999.99,
                        "category": "电子产品"
                    }
                ],
                "total": 1
            }
        }
```