/**
 * 表单配置编辑器组件
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  Card,
  Row,
  Col,
  Tabs,
  Switch,
  InputNumber,
  message,
  Divider,
  Typography,
  Tag,
  Collapse
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  DragOutlined,
  SettingOutlined,
  FormOutlined
} from '@ant-design/icons';
// import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import type { 
  FormConfig, 
  FormConfigCreateRequest, 
  FormFieldConfig, 
  FormSectionConfig,
  FormLayoutConfig,
  FormPermissionConfig,
  FormFieldDisplayType
} from '../../../types/developer/form';
import type { Entity } from '../../../types/developer/entity';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

interface FormConfigEditorProps {
  form?: FormConfig | null;
  entities: Entity[];
  visible: boolean;
  onSave: (formData: FormConfigCreateRequest) => void;
  onCancel: () => void;
}

// 字段显示类型选项
const DISPLAY_TYPE_OPTIONS: { value: FormFieldDisplayType; label: string; description: string }[] = [
  { value: 'input', label: '输入框', description: '单行文本输入' },
  { value: 'textarea', label: '多行文本', description: '多行文本输入' },
  { value: 'select', label: '下拉选择', description: '单选下拉框' },
  { value: 'radio', label: '单选按钮', description: '单选按钮组' },
  { value: 'checkbox', label: '复选框', description: '多选复选框' },
  { value: 'switch', label: '开关', description: '布尔值开关' },
  { value: 'date-picker', label: '日期选择', description: '日期时间选择器' },
  { value: 'upload', label: '文件上传', description: '文件上传组件' },
  { value: 'rich-text', label: '富文本', description: '富文本编辑器' }
];

// 布局类型选项
const LAYOUT_TYPE_OPTIONS = [
  { value: 'vertical', label: '垂直布局' },
  { value: 'horizontal', label: '水平布局' },
  { value: 'grid', label: '网格布局' },
  { value: 'inline', label: '内联布局' },
  { value: 'tabs', label: '标签页布局' }
];

// 权限操作选项
const PERMISSION_ACTIONS = [
  { value: 'create', label: '创建' },
  { value: 'read', label: '查看' },
  { value: 'update', label: '更新' },
  { value: 'delete', label: '删除' },
  { value: 'export', label: '导出' },
  { value: 'import', label: '导入' }
];

const FormConfigEditor: React.FC<FormConfigEditorProps> = ({
  form,
  entities,
  visible,
  onSave,
  onCancel
}) => {
  const [formInstance] = Form.useForm();
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null);
  const [sections, setSections] = useState<FormSectionConfig[]>([]);
  const [activeTab, setActiveTab] = useState('basic');
  const [editingField, setEditingField] = useState<FormFieldConfig | null>(null);
  const [showFieldEditor, setShowFieldEditor] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (form) {
      formInstance.setFieldsValue({
        name: form.name,
        entity: form.entity,
        description: form.description,
        layout: form.layout,
        permissions: form.permissions
      });
      setSections(form.sections);
      
      // 查找对应的实体
      const entity = entities.find(e => e.name === form.entity);
      setSelectedEntity(entity || null);
    } else {
      formInstance.resetFields();
      setSections([]);
      setSelectedEntity(null);
    }
  }, [form, entities, formInstance]);

  // 处理实体选择
  const handleEntityChange = (entityName: string) => {
    const entity = entities.find(e => e.name === entityName);
    setSelectedEntity(entity || null);
    
    if (entity) {
      // 自动创建基础分组和字段
      const basicSection: FormSectionConfig = {
        id: 'section_basic',
        title: '基本信息',
        collapsible: false,
        fields: entity.fields.slice(0, 5).map((field, index) => ({
          id: `field_${field.name}`,
          entityField: field.name,
          displayType: getDefaultDisplayType(field.type),
          label: field.displayName,
          placeholder: `请输入${field.displayName}`,
          required: field.required,
          readonly: false,
          hidden: false,
          validation: {
            rules: field.required ? ['required'] : [],
            messages: field.required ? { required: `${field.displayName}不能为空` } : { required: '' }
          }
        })),
        order: 0
      };
      setSections([basicSection]);
    }
  };

  // 获取默认显示类型
  const getDefaultDisplayType = (fieldType: string): FormFieldDisplayType => {
    switch (fieldType) {
      case 'text':
        return 'textarea';
      case 'number':
        return 'input';
      case 'boolean':
        return 'switch';
      case 'date':
      case 'datetime':
        return 'date-picker';
      case 'file':
      case 'image':
        return 'upload';
      default:
        return 'input';
    }
  };

  // 添加新分组
  const handleAddSection = () => {
    const newSection: FormSectionConfig = {
      id: `section_${Date.now()}`,
      title: `分组 ${sections.length + 1}`,
      collapsible: true,
      fields: [],
      order: sections.length
    };
    setSections([...sections, newSection]);
  };

  // 删除分组
  const handleDeleteSection = (sectionId: string) => {
    setSections(sections.filter(s => s.id !== sectionId));
  };

  // 添加字段到分组
  const handleAddFieldToSection = (sectionId: string) => {
    if (!selectedEntity) {
      message.warning('请先选择实体');
      return;
    }

    // 获取未使用的字段
    const usedFields = sections.flatMap(s => s.fields.map(f => f.entityField));
    const availableFields = selectedEntity.fields.filter(f => !usedFields.includes(f.name));

    if (availableFields.length === 0) {
      message.warning('所有字段都已添加');
      return;
    }

    const field = availableFields[0];
    const newField: FormFieldConfig = {
      id: `field_${field.name}_${Date.now()}`,
      entityField: field.name,
      displayType: getDefaultDisplayType(field.type),
      label: field.displayName,
      placeholder: `请输入${field.displayName}`,
      required: field.required,
      readonly: false,
      hidden: false,
      validation: {
        rules: field.required ? ['required'] : [],
        messages: field.required ? { required: `${field.displayName}不能为空` } : {}
      }
    };

    setSections(sections.map(section => 
      section.id === sectionId 
        ? { ...section, fields: [...section.fields, newField] }
        : section
    ));
  };

  // 编辑字段
  const handleEditField = (field: FormFieldConfig) => {
    setEditingField(field);
    setShowFieldEditor(true);
  };

  // 删除字段
  const handleDeleteField = (sectionId: string, fieldId: string) => {
    setSections(sections.map(section => 
      section.id === sectionId 
        ? { ...section, fields: section.fields.filter(f => f.id !== fieldId) }
        : section
    ));
  };

  // 拖拽排序 - 暂时禁用
  const handleDragEnd = (result: any) => {
    // 暂时禁用拖拽功能
    console.log('拖拽功能暂时禁用', result);
  };

  // 保存表单配置
  const handleSave = async () => {
    try {
      const values = await formInstance.validateFields();
      
      const formData: FormConfigCreateRequest = {
        name: values.name,
        entity: values.entity,
        description: values.description,
        layout: values.layout || {
          type: 'vertical',
          columns: 1,
          spacing: 16,
          responsive: true
        },
        sections: sections.map((section, index) => ({
          title: section.title,
          description: section.description,
          collapsible: section.collapsible,
          fields: section.fields,
          order: index
        })),
        permissions: values.permissions || []
      };

      onSave(formData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={form ? '编辑表单配置' : '创建表单配置'}
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存
        </Button>
      ]}
    >
      <Form form={formInstance} layout="vertical">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="基本信息" key="basic">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="表单名称"
                  rules={[{ required: true, message: '请输入表单名称' }]}
                >
                  <Input placeholder="请输入表单名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="entity"
                  label="关联实体"
                  rules={[{ required: true, message: '请选择关联实体' }]}
                >
                  <Select
                    placeholder="请选择关联实体"
                    onChange={handleEntityChange}
                  >
                    {entities.map(entity => (
                      <Option key={entity.id} value={entity.name}>
                        {entity.displayName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item
              name="description"
              label="表单描述"
            >
              <TextArea rows={3} placeholder="请输入表单描述" />
            </Form.Item>
          </TabPane>

          <TabPane tab="布局配置" key="layout">
            <Form.Item name={['layout', 'type']} label="布局类型">
              <Select placeholder="请选择布局类型">
                {LAYOUT_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name={['layout', 'columns']} label="列数">
                  <InputNumber min={1} max={6} placeholder="列数" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name={['layout', 'spacing']} label="间距">
                  <InputNumber min={0} max={48} placeholder="间距" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name={['layout', 'responsive']} label="响应式" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="字段配置" key="fields">
            <div className="fields-config">
              <div className="section-header">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddSection}
                >
                  添加分组
                </Button>
              </div>

              <div className="sections-list">
                {sections.map(section => (
                  <Card
                    key={section.id}
                    title={
                      <Space>
                        <DragOutlined />
                        <Input
                          value={section.title}
                          onChange={(e) => {
                            setSections(sections.map(s =>
                              s.id === section.id
                                ? { ...s, title: e.target.value }
                                : s
                            ));
                          }}
                          style={{ width: 200 }}
                        />
                      </Space>
                    }
                    extra={
                      <Space>
                        <Button
                          type="text"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddFieldToSection(section.id)}
                        >
                          添加字段
                        </Button>
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteSection(section.id)}
                        />
                      </Space>
                    }
                    className="section-card"
                  >
                    <div className="fields-list">
                      {section.fields.map((field, index) => (
                        <div key={field.id} className="field-item">
                          <Card size="small">
                            <Row align="middle">
                              <Col flex="auto">
                                <Space>
                                  <DragOutlined />
                                  <Text strong>{field.label}</Text>
                                  <Tag>{field.displayType}</Tag>
                                  {field.required && <Tag color="red">必填</Tag>}
                                </Space>
                              </Col>
                              <Col>
                                <Space>
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<EditOutlined />}
                                    onClick={() => handleEditField(field)}
                                  />
                                  <Button
                                    type="text"
                                    size="small"
                                    danger
                                    icon={<DeleteOutlined />}
                                    onClick={() => handleDeleteField(section.id, field.id)}
                                  />
                                </Space>
                              </Col>
                            </Row>
                          </Card>
                        </div>
                      ))}
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </TabPane>

          <TabPane tab="权限配置" key="permissions">
            <Form.List name="permissions">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Card key={key} size="small" style={{ marginBottom: 16 }}>
                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item
                            {...restField}
                            name={[name, 'role']}
                            label="角色"
                            rules={[{ required: true, message: '请输入角色名称' }]}
                          >
                            <Input placeholder="角色名称" />
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item
                            {...restField}
                            name={[name, 'actions']}
                            label="权限"
                            rules={[{ required: true, message: '请选择权限' }]}
                          >
                            <Select
                              mode="multiple"
                              placeholder="请选择权限"
                              options={PERMISSION_ACTIONS}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => remove(name)}
                            style={{ marginTop: 30 }}
                          />
                        </Col>
                      </Row>
                    </Card>
                  ))}
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    添加权限配置
                  </Button>
                </>
              )}
            </Form.List>
          </TabPane>
        </Tabs>
      </Form>
    </Modal>
  );
};

export default FormConfigEditor;
