"""
MySQL数据库初始化脚本
创建AILF数据库和必要的表结构
"""
import pymysql
import sys
import os
from app.config import settings

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {settings.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 {settings.DB_NAME} 创建成功")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {str(e)}")
        return False

def init_tables():
    """初始化数据库表"""
    try:
        # 导入数据库模型
        from app.core.database import engine, Base
        from app.models.user import User
        from app.models.role import RoleDBModel
        from app.models.permission import Permission
        from app.models.user_permission import UserPermission
        from app.models.scenario import Scenario
        from app.models.template import Template
        from app.models.entity import Entity
        from app.models.relationship import Relationship
        from app.models.workflow import Workflow
        from app.models.form_config import FormConfig
        from app.models.api_route import APIRoute
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库表失败: {str(e)}")
        return False

def init_default_data():
    """初始化默认数据"""
    try:
        from app.core.role_db_init import init_role_database
        from app.core.permission_control_db_init import init_permission_control_database
        from app.core.user_db_init import init_user_database
        
        # 初始化角色数据
        print("🔄 初始化角色数据...")
        init_role_database()
        
        # 初始化权限数据
        print("🔄 初始化权限数据...")
        init_permission_control_database()
        
        # 初始化用户数据
        print("🔄 初始化用户数据...")
        init_user_database()
        
        print("✅ 默认数据初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 初始化默认数据失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始初始化MySQL数据库...")
    print(f"📊 数据库配置:")
    print(f"   主机: {settings.DB_HOST}:{settings.DB_PORT}")
    print(f"   用户: {settings.DB_USER}")
    print(f"   数据库: {settings.DB_NAME}")
    print()
    
    # 1. 创建数据库
    if not create_database():
        sys.exit(1)
    
    # 2. 创建表结构
    if not init_tables():
        sys.exit(1)
    
    # 3. 初始化默认数据
    if not init_default_data():
        sys.exit(1)
    
    print()
    print("🎉 MySQL数据库初始化完成！")
    print("💡 现在可以启动AILF后端服务了")

if __name__ == "__main__":
    main()
