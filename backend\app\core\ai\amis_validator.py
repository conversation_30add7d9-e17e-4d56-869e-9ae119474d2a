"""
AMIS Schema 校验器
基于官方文档规范校验amis schema的正确性
"""
from typing import Dict, Any, List, Tuple, Optional, Union
import json
import re
from enum import Enum

from app.core.ai.amis_knowledge import AmisComponentType, AmisFormItemType


class ValidationLevel(Enum):
    """校验级别"""
    ERROR = "error"      # 错误：必须修复
    WARNING = "warning"  # 警告：建议修复
    INFO = "info"        # 信息：优化建议


class ValidationResult:
    """校验结果"""
    def __init__(self):
        self.errors: List[Dict[str, Any]] = []
        self.warnings: List[Dict[str, Any]] = []
        self.infos: List[Dict[str, Any]] = []
        self.is_valid: bool = True
    
    def add_issue(self, level: ValidationLevel, message: str, path: str = "", 
                  component_type: str = "", suggestion: str = ""):
        """添加校验问题"""
        issue = {
            "level": level.value,
            "message": message,
            "path": path,
            "component_type": component_type,
            "suggestion": suggestion
        }
        
        if level == ValidationLevel.ERROR:
            self.errors.append(issue)
            self.is_valid = False
        elif level == ValidationLevel.WARNING:
            self.warnings.append(issue)
        else:
            self.infos.append(issue)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "is_valid": self.is_valid,
            "errors": self.errors,
            "warnings": self.warnings,
            "infos": self.infos,
            "summary": {
                "error_count": len(self.errors),
                "warning_count": len(self.warnings),
                "info_count": len(self.infos)
            }
        }


class AmisSchemaValidator:
    """AMIS Schema 校验器"""
    
    def __init__(self):
        self.component_types = {ct.value for ct in AmisComponentType}
        self.form_item_types = {fit.value for fit in AmisFormItemType}
        
        # 必需字段映射
        self.required_fields = {
            "page": ["type"],
            "form": ["type"],
            "crud": ["type", "api"],
            "table": ["type", "columns"],
            "dialog": ["type", "body"],
            "drawer": ["type", "body"],
            "action": ["type"],
            "button": ["type"],
            "input-text": ["type", "name"],
            "select": ["type", "name"],
            "textarea": ["type", "name"],
            "checkbox": ["type", "name"],
            "radio": ["type", "name"],
            "switch": ["type", "name"],
        }
        
        # 字段类型映射
        self.field_types = {
            "type": str,
            "name": str,
            "label": str,
            "value": [str, int, float, bool, list, dict],
            "required": bool,
            "disabled": bool,
            "visible": bool,
            "hidden": bool,
            "className": str,
            "size": str,
            "placeholder": str,
            "description": str,
            "api": [str, dict],
            "data": [dict, list],
            "columns": list,
            "body": [list, dict],
            "actions": list,
            "toolbar": list,
            "headerToolbar": list,
            "footerToolbar": list,
        }
    
    def validate(self, schema: Dict[str, Any], path: str = "root") -> ValidationResult:
        """校验amis schema"""
        result = ValidationResult()
        
        if not isinstance(schema, dict):
            result.add_issue(
                ValidationLevel.ERROR,
                "Schema必须是一个JSON对象",
                path
            )
            return result
        
        # 校验根组件
        self._validate_component(schema, result, path)
        
        return result
    
    def _validate_component(self, component: Dict[str, Any], result: ValidationResult, path: str):
        """校验单个组件"""
        if not isinstance(component, dict):
            result.add_issue(
                ValidationLevel.ERROR,
                "组件配置必须是对象",
                path
            )
            return
        
        # 校验type字段
        component_type = component.get("type")
        if not component_type:
            result.add_issue(
                ValidationLevel.ERROR,
                "组件缺少必需的'type'字段",
                path
            )
            return
        
        if not isinstance(component_type, str):
            result.add_issue(
                ValidationLevel.ERROR,
                "组件type字段必须是字符串",
                path,
                component_type
            )
            return
        
        # 校验组件类型是否有效
        if component_type not in self.component_types and component_type not in self.form_item_types:
            result.add_issue(
                ValidationLevel.WARNING,
                f"未知的组件类型: {component_type}",
                path,
                component_type,
                "请检查组件类型是否正确，或者是否为自定义组件"
            )
        
        # 校验必需字段
        self._validate_required_fields(component, result, path, component_type)
        
        # 校验字段类型
        self._validate_field_types(component, result, path, component_type)
        
        # 校验特定组件的规则
        self._validate_component_specific_rules(component, result, path, component_type)
        
        # 递归校验子组件
        self._validate_child_components(component, result, path)
    
    def _validate_required_fields(self, component: Dict[str, Any], result: ValidationResult, 
                                  path: str, component_type: str):
        """校验必需字段"""
        required = self.required_fields.get(component_type, [])
        
        for field in required:
            if field not in component:
                result.add_issue(
                    ValidationLevel.ERROR,
                    f"组件 {component_type} 缺少必需字段: {field}",
                    path,
                    component_type,
                    f"请添加 {field} 字段"
                )
    
    def _validate_field_types(self, component: Dict[str, Any], result: ValidationResult,
                              path: str, component_type: str):
        """校验字段类型"""
        for field, value in component.items():
            expected_types = self.field_types.get(field)
            if expected_types is None:
                continue
            
            if not isinstance(expected_types, list):
                expected_types = [expected_types]
            
            if not any(isinstance(value, t) for t in expected_types):
                type_names = [t.__name__ for t in expected_types]
                result.add_issue(
                    ValidationLevel.WARNING,
                    f"字段 {field} 类型不匹配，期望: {'/'.join(type_names)}，实际: {type(value).__name__}",
                    f"{path}.{field}",
                    component_type
                )
    
    def _validate_component_specific_rules(self, component: Dict[str, Any], result: ValidationResult,
                                           path: str, component_type: str):
        """校验特定组件的规则"""
        
        # Page组件特殊校验
        if component_type == "page":
            self._validate_page_component(component, result, path)
        
        # Form组件特殊校验
        elif component_type == "form":
            self._validate_form_component(component, result, path)
        
        # CRUD组件特殊校验
        elif component_type == "crud":
            self._validate_crud_component(component, result, path)
        
        # Table组件特殊校验
        elif component_type == "table":
            self._validate_table_component(component, result, path)
        
        # 表单项组件特殊校验
        elif component_type in self.form_item_types:
            self._validate_form_item_component(component, result, path, component_type)
    
    def _validate_page_component(self, component: Dict[str, Any], result: ValidationResult, path: str):
        """校验Page组件"""
        # 检查是否有内容
        if not any(key in component for key in ["body", "aside", "toolbar"]):
            result.add_issue(
                ValidationLevel.WARNING,
                "页面组件没有内容区域",
                path,
                "page",
                "建议添加 body、aside 或 toolbar 内容"
            )
        
        # 检查标题
        if "title" not in component:
            result.add_issue(
                ValidationLevel.INFO,
                "建议为页面添加标题",
                path,
                "page"
            )
    
    def _validate_form_component(self, component: Dict[str, Any], result: ValidationResult, path: str):
        """校验Form组件"""
        # 检查表单项
        if "body" not in component:
            result.add_issue(
                ValidationLevel.WARNING,
                "表单缺少表单项",
                path,
                "form",
                "请添加 body 字段包含表单项"
            )
        
        # 检查提交API
        if "api" not in component:
            result.add_issue(
                ValidationLevel.INFO,
                "表单没有配置提交API",
                path,
                "form",
                "如需提交数据，请配置 api 字段"
            )
    
    def _validate_crud_component(self, component: Dict[str, Any], result: ValidationResult, path: str):
        """校验CRUD组件"""
        # 检查数据源API
        if "api" not in component:
            result.add_issue(
                ValidationLevel.ERROR,
                "CRUD组件缺少数据源API",
                path,
                "crud",
                "请配置 api 字段指定数据源"
            )
        
        # 检查列配置
        if "columns" not in component:
            result.add_issue(
                ValidationLevel.WARNING,
                "CRUD组件缺少列配置",
                path,
                "crud",
                "建议配置 columns 字段定义表格列"
            )
    
    def _validate_table_component(self, component: Dict[str, Any], result: ValidationResult, path: str):
        """校验Table组件"""
        columns = component.get("columns", [])
        if not columns:
            result.add_issue(
                ValidationLevel.ERROR,
                "表格组件缺少列配置",
                path,
                "table",
                "请配置 columns 字段定义表格列"
            )
        elif isinstance(columns, list):
            for i, col in enumerate(columns):
                if isinstance(col, dict) and "name" not in col:
                    result.add_issue(
                        ValidationLevel.WARNING,
                        f"表格列 {i} 缺少 name 字段",
                        f"{path}.columns[{i}]",
                        "table"
                    )
    
    def _validate_form_item_component(self, component: Dict[str, Any], result: ValidationResult,
                                      path: str, component_type: str):
        """校验表单项组件"""
        # 检查name字段
        if "name" not in component:
            result.add_issue(
                ValidationLevel.ERROR,
                f"表单项 {component_type} 缺少 name 字段",
                path,
                component_type,
                "表单项必须有 name 字段用于数据绑定"
            )
        
        # 检查label字段
        if "label" not in component:
            result.add_issue(
                ValidationLevel.INFO,
                f"建议为表单项 {component_type} 添加 label",
                path,
                component_type
            )
        
        # 特定表单项校验
        if component_type == "select" and "options" not in component and "source" not in component:
            result.add_issue(
                ValidationLevel.WARNING,
                "选择框缺少选项配置",
                path,
                component_type,
                "请配置 options 或 source 字段"
            )
    
    def _validate_child_components(self, component: Dict[str, Any], result: ValidationResult, path: str):
        """递归校验子组件"""
        # 校验body中的子组件
        if "body" in component:
            body = component["body"]
            if isinstance(body, list):
                for i, child in enumerate(body):
                    if isinstance(child, dict):
                        self._validate_component(child, result, f"{path}.body[{i}]")
            elif isinstance(body, dict):
                self._validate_component(body, result, f"{path}.body")
        
        # 校验其他可能包含子组件的字段
        for field in ["columns", "actions", "toolbar", "headerToolbar", "footerToolbar"]:
            if field in component and isinstance(component[field], list):
                for i, item in enumerate(component[field]):
                    if isinstance(item, dict) and "type" in item:
                        self._validate_component(item, result, f"{path}.{field}[{i}]")


# 全局校验器实例
amis_validator = AmisSchemaValidator()
