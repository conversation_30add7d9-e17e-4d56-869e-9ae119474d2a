/**
 * 工作流可视化画布组件 - 基于Butterfly-DAG
 * Apple风格设计，支持拖拽、连线、节点编辑
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Canvas } from 'butterfly-dag';
import { Button, Space, message, Drawer, Form, Input, Select, Card } from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  SettingOutlined
} from '@ant-design/icons';
import WorkflowNode from './WorkflowNode';
import WorkflowEdge from './WorkflowEdge';
import NodeEditor from './NodeEditor';
import type { WorkflowDefinition, WorkflowNode as WorkflowNodeType } from '../../types/developer/workflowTypes';
import './WorkflowCanvas.css';
import 'butterfly-dag/dist/index.css';

const { Option } = Select;

interface WorkflowCanvasProps {
  workflow?: WorkflowDefinition | null;
  onSave?: (workflow: WorkflowDefinition) => void;
  onNodeSelect?: (node: WorkflowNodeType | null) => void;
}

const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  workflow,
  onSave,
  onNodeSelect
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const canvasInstance = useRef<Canvas | null>(null);
  const [selectedNode, setSelectedNode] = useState<WorkflowNodeType | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [nodeForm] = Form.useForm();

  // 节点类型配置
  const nodeTypes = [
    { type: 'start', label: '开始节点', color: '#52c41a', icon: '▶' },
    { type: 'api_call', label: 'API调用', color: '#1890ff', icon: '🔗' },
    { type: 'user_input', label: '用户输入', color: '#722ed1', icon: '📝' },
    { type: 'condition', label: '条件判断', color: '#fa8c16', icon: '❓' },
    { type: 'notification', label: '通知节点', color: '#eb2f96', icon: '📢' },
    { type: 'end', label: '结束节点', color: '#f5222d', icon: '⏹' }
  ];

  // 初始化画布
  const initCanvas = useCallback(() => {
    if (!canvasRef.current) return;

    const canvas = new Canvas({
      root: canvasRef.current,
      disLinkable: true,
      linkable: true,
      draggable: true,
      zoomable: true,
      moveable: true,
      theme: {
        edge: {
          shapeType: 'AdvancedBezier',
          arrow: true,
          arrowPosition: 0.5,
          arrowOffset: 10
        },
        endpoint: {
          position: [0, 0.5],
          linkableHighlight: true
        }
      }
    });

    // 监听节点选择事件
    canvas.on('node:click', (data: any) => {
      const nodeData = data.node.options;
      setSelectedNode(nodeData);
      onNodeSelect?.(nodeData);
    });

    // 监听画布点击事件（取消选择）
    canvas.on('canvas:click', () => {
      setSelectedNode(null);
      onNodeSelect?.(null);
    });

    // 监听连线事件
    canvas.on('link:connect', (data: any) => {
      console.log('Connected:', data);
      // 这里可以添加连线逻辑
    });

    canvasInstance.current = canvas;

    // 绘制初始数据
    const mockData = {
      nodes: [
        {
          id: 'start_node',
          label: '开始节点',
          left: 100,
          top: 100,
          Class: WorkflowNode,
          nodeType: 'start',
          endpoints: [
            {
              id: 'right',
              orientation: [1, 0],
              pos: [0, 0.5]
            }
          ]
        },
        {
          id: 'api_node',
          label: 'API调用',
          left: 300,
          top: 100,
          Class: WorkflowNode,
          nodeType: 'api_call',
          endpoints: [
            {
              id: 'left',
              orientation: [-1, 0],
              pos: [0, 0.5]
            },
            {
              id: 'right',
              orientation: [1, 0],
              pos: [0, 0.5]
            }
          ]
        },
        {
          id: 'end_node',
          label: '结束节点',
          left: 500,
          top: 100,
          Class: WorkflowNode,
          nodeType: 'end',
          endpoints: [
            {
              id: 'left',
              orientation: [-1, 0],
              pos: [0, 0.5]
            }
          ]
        }
      ],
      edges: [
        {
          source: 'right',
          target: 'left',
          sourceNode: 'start_node',
          targetNode: 'api_node',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowEdge
        },
        {
          source: 'right',
          target: 'left',
          sourceNode: 'api_node',
          targetNode: 'end_node',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowEdge
        }
      ]
    };

    canvas.draw(mockData);

    return canvas;
  }, [workflow, onNodeSelect]);

  // 加载工作流数据到画布
  const loadWorkflowData = (canvas: Canvas, workflowData: WorkflowDefinition) => {
    const nodes = workflowData.nodes?.map(node => ({
      id: node.id,
      label: node.name,
      left: node.position?.x || 100,
      top: node.position?.y || 100,
      Class: WorkflowNode,
      nodeType: node.type,
      nodeData: node,
      endpoints: getNodeEndpoints(node.type)
    })) || [];

    const edges = workflowData.nodes?.flatMap(node => 
      node.child_nodes?.map(child => ({
        source: 'right',
        target: 'left',
        sourceNode: node.id,
        targetNode: child.node_id,
        type: 'endpoint',
        arrow: true,
        Class: WorkflowEdge,
        label: child.condition?.description || ''
      })) || []
    ) || [];

    canvas.draw({ nodes, edges });
  };

  // 获取节点端点配置
  const getNodeEndpoints = (nodeType: string) => {
    const baseEndpoints = [
      { id: 'left', orientation: [-1, 0], pos: [0, 0.5] },
      { id: 'right', orientation: [1, 0], pos: [0, 0.5] }
    ];

    if (nodeType === 'condition') {
      return [
        ...baseEndpoints,
        { id: 'bottom-true', orientation: [0, 1], pos: [0.3, 0] },
        { id: 'bottom-false', orientation: [0, 1], pos: [0.7, 0] }
      ];
    }

    return baseEndpoints;
  };

  // 添加节点
  const addNode = (nodeType: string) => {
    if (!canvasInstance.current) return;

    const nodeId = `node_${Date.now()}`;
    const nodeConfig = nodeTypes.find(type => type.type === nodeType);
    
    const newNode = {
      id: nodeId,
      label: nodeConfig?.label || '新节点',
      left: 200 + Math.random() * 200,
      top: 100 + Math.random() * 200,
      Class: WorkflowNode,
      nodeType: nodeType,
      nodeData: {
        id: nodeId,
        name: nodeConfig?.label || '新节点',
        type: nodeType,
        description: '',
        position: { x: 200, y: 100 },
        parent_nodes: [],
        child_nodes: [],
        config: {},
        outputs: [],
        ai_instructions: {}
      },
      endpoints: getNodeEndpoints(nodeType)
    };

    canvasInstance.current.addNode(newNode);
    message.success(`已添加${nodeConfig?.label}`);
  };

  // 编辑节点
  const editNode = () => {
    if (!selectedNode) {
      message.warning('请先选择一个节点');
      return;
    }

    nodeForm.setFieldsValue({
      name: selectedNode.name,
      description: selectedNode.description,
      type: selectedNode.type
    });
    setDrawerVisible(true);
  };

  // 保存节点编辑
  const saveNodeEdit = async (values: any) => {
    if (!selectedNode || !canvasInstance.current) return;

    try {
      // 更新节点数据
      const updatedNode = {
        ...selectedNode,
        ...values
      };

      // 更新画布中的节点
      const canvasNode = canvasInstance.current.getNode(selectedNode.id);
      if (canvasNode) {
        canvasNode.options.label = values.name;
        canvasNode.options.nodeData = updatedNode;
        canvasNode.draw();
      }

      setSelectedNode(updatedNode);
      setDrawerVisible(false);
      message.success('节点更新成功');
    } catch (error) {
      message.error('节点更新失败');
    }
  };

  // 删除选中节点
  const deleteSelectedNode = () => {
    if (!selectedNode || !canvasInstance.current) {
      message.warning('请先选择一个节点');
      return;
    }

    canvasInstance.current.removeNode(selectedNode.id);
    setSelectedNode(null);
    message.success('节点已删除');
  };

  // 保存工作流
  const saveWorkflow = () => {
    if (!canvasInstance.current || !workflow) return;

    try {
      const canvasData = canvasInstance.current.getDataMap();
      console.log('💾 获取到的画布数据:', canvasData);
      console.log('💾 画布节点数:', canvasData.nodes.length);
      console.log('💾 画布边数:', canvasData.edges.length);
      
      if (canvasData.edges.length > 0) {
        console.log('💾 边数据详情:', canvasData.edges);
      }
      
      // 初始化所有节点的父子关系
      const nodesMap = new Map();
      canvasData.nodes.forEach((node: any) => {
        const nodeData = {
          ...node.options.nodeData,
          position: {
            x: Math.round(node.left),
            y: Math.round(node.top)
          },
          parent_nodes: [],
          child_nodes: []
        };
        nodesMap.set(node.id, nodeData);
      });

      // 处理边数据，建立父子关系
      canvasData.edges.forEach((edge: any) => {
        console.log('💾 处理边:', edge);
        const sourceId = edge.sourceNode || edge.source;
        const targetId = edge.targetNode || edge.target;
        
        if (sourceId && targetId) {
          const sourceNode = nodesMap.get(sourceId);
          const targetNode = nodesMap.get(targetId);
          
          if (sourceNode && targetNode) {
            // 添加父子关系
            sourceNode.child_nodes.push({
              node_id: targetId,
              condition: {
                description: edge.label || ''
              }
            });
            targetNode.parent_nodes.push(sourceId);
            console.log(`💾 建立连接: ${sourceId} -> ${targetId}`);
          }
        }
      });

      const updatedNodes = Array.from(nodesMap.values());
      
      const updatedWorkflow = {
        ...workflow,
        nodes: updatedNodes
      };

      console.log('💾 最终工作流数据:', updatedWorkflow);
      onSave?.(updatedWorkflow);
      message.success('工作流保存成功');
    } catch (error) {
      console.error('💾 保存失败:', error);
      message.error('工作流保存失败');
    }
  };

  // 画布操作
  const zoomIn = () => canvasInstance.current?.zoom(1.2);
  const zoomOut = () => canvasInstance.current?.zoom(0.8);
  const fitToScreen = () => canvasInstance.current?.focusCenterWithAnimate();

  useEffect(() => {
    const canvas = initCanvas();
    return () => {
      if (canvas) {
        canvas.destroy();
      }
    };
  }, [initCanvas]);

  return (
    <div className="workflow-canvas-container">
      {/* 工具栏 */}
      <div className="canvas-toolbar">
        <div className="toolbar-left">
          <Space>
            <span className="toolbar-title">节点类型:</span>
            {nodeTypes.map(nodeType => (
              <Button
                key={nodeType.type}
                size="small"
                onClick={() => addNode(nodeType.type)}
                style={{ 
                  borderColor: nodeType.color,
                  color: nodeType.color
                }}
              >
                {nodeType.icon} {nodeType.label}
              </Button>
            ))}
          </Space>
        </div>
        
        <div className="toolbar-right">
          <Space>
            <Button icon={<SettingOutlined />} onClick={editNode} disabled={!selectedNode}>
              编辑节点
            </Button>
            <Button icon={<SaveOutlined />} type="primary" onClick={saveWorkflow}>
              保存工作流
            </Button>
            <Button icon={<ZoomInOutlined />} onClick={zoomIn} />
            <Button icon={<ZoomOutOutlined />} onClick={zoomOut} />
            <Button icon={<FullscreenOutlined />} onClick={fitToScreen} />
          </Space>
        </div>
      </div>

      {/* 画布区域 */}
      <div className="canvas-area" ref={canvasRef} />

      {/* 节点编辑抽屉 */}
      <Drawer
        title="编辑节点"
        placement="right"
        width={400}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
      >
        <Form
          form={nodeForm}
          layout="vertical"
          onFinish={saveNodeEdit}
        >
          <Form.Item
            name="name"
            label="节点名称"
            rules={[{ required: true, message: '请输入节点名称' }]}
          >
            <Input placeholder="输入节点名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="节点描述"
          >
            <Input.TextArea rows={3} placeholder="输入节点描述" />
          </Form.Item>

          <Form.Item
            name="type"
            label="节点类型"
          >
            <Select disabled>
              {nodeTypes.map(type => (
                <Option key={type.type} value={type.type}>
                  {type.icon} {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div className="drawer-actions">
            <Space>
              <Button onClick={() => setDrawerVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button danger onClick={deleteSelectedNode}>
                删除节点
              </Button>
            </Space>
          </div>
        </Form>
      </Drawer>
    </div>
  );
};

export default WorkflowCanvas;
