/**
 * API配置文件
 */

// API基础URL
export const API_BASE_URL = 'http://localhost:5000';

// API端点
export const API_ENDPOINTS = {
  // 认证模块
  AUTH: {
    DEVELOPER: '/api/auth/developer',
    VERIFY_TOKEN: '/api/auth/developer/verify-token',
  },
  
  // 场景管理
  SCENARIO: {
    BASE: '/api/scenario',
    BY_ID: (id: string) => `/api/scenario/${id}`,
    LIST: '/api/scenarios',
    ACTIVATE: (id: string) => `/api/scenario/${id}/activate`,
    FROM_TEMPLATE: '/api/scenario/from-template',
    VALIDATE: '/api/scenario/validate',
  },
  
  // 模板管理
  TEMPLATES: {
    BASE: '/api/templates',
    BY_KEY: (key: string) => `/api/templates/${key}`,
    CATEGORIES: '/api/templates/categories',
    BY_CATEGORY: (category: string) => `/api/templates/category/${category}`,
    SEARCH: (keyword: string) => `/api/templates/search/${keyword}`,
    RELOAD: '/api/templates/reload',
  },
  
  // 代码生成
  GENERATE: {
    DATABASE: '/api/generate-database',
    APIS: '/api/generate-apis',
    PERMISSIONS: '/api/generate-permissions',
    REGISTER_ROUTES: '/api/register-routes',
    ACTIVATE: '/api/activate',
    CODE: '/api/generate-code',
    CODE_PREVIEW: '/api/code-preview',
  },
  
  // 路由管理
  ROUTES: {
    BASE: '/api/routes',
    REGISTER: '/api/routes/register',
    BY_ID: (id: string) => `/api/routes/${id}`,
  },
  
  // 角色管理
  ROLES: {
    BASE: '/api/roles',
    BY_ID: (id: string) => `/api/roles/${id}`,
  },
  
  // 权限管理
  PERMISSIONS: {
    BASE: '/api/permissions',
    CHECK: '/api/permissions/check',
    USER_APIS: '/api/permissions/user-apis',
    ROLE_API: '/api/permissions/role-api',
    BATCH_UPDATE: '/api/permissions/batch-update',
  },
  
  // AI交互
  AI: {
    COMMAND: '/api/command',
  },
  
  // 健康检查
  HEALTH: '/api/health',
};

// 构建完整URL的辅助函数
export const buildUrl = (endpoint: string): string => {
  return `${API_BASE_URL}${endpoint}`;
};

// 常用的API调用函数
export const apiUrls = {
  // 认证
  developerAuth: () => buildUrl(API_ENDPOINTS.AUTH.DEVELOPER),
  verifyToken: () => buildUrl(API_ENDPOINTS.AUTH.VERIFY_TOKEN),
  
  // 场景管理
  scenario: () => buildUrl(API_ENDPOINTS.SCENARIO.BASE),
  scenarioById: (id: string) => buildUrl(API_ENDPOINTS.SCENARIO.BY_ID(id)),
  scenarios: () => buildUrl(API_ENDPOINTS.SCENARIO.LIST),
  activateScenario: (id: string) => buildUrl(API_ENDPOINTS.SCENARIO.ACTIVATE(id)),
  scenarioFromTemplate: () => buildUrl(API_ENDPOINTS.SCENARIO.FROM_TEMPLATE),
  validateScenario: () => buildUrl(API_ENDPOINTS.SCENARIO.VALIDATE),
  
  // 模板管理
  templates: () => buildUrl(API_ENDPOINTS.TEMPLATES.BASE),
  templateByKey: (key: string) => buildUrl(API_ENDPOINTS.TEMPLATES.BY_KEY(key)),
  templateCategories: () => buildUrl(API_ENDPOINTS.TEMPLATES.CATEGORIES),
  templatesByCategory: (category: string) => buildUrl(API_ENDPOINTS.TEMPLATES.BY_CATEGORY(category)),
  searchTemplates: (keyword: string) => buildUrl(API_ENDPOINTS.TEMPLATES.SEARCH(keyword)),
  reloadTemplates: () => buildUrl(API_ENDPOINTS.TEMPLATES.RELOAD),
  
  // 代码生成
  generateDatabase: () => buildUrl(API_ENDPOINTS.GENERATE.DATABASE),
  generateApis: () => buildUrl(API_ENDPOINTS.GENERATE.APIS),
  generatePermissions: () => buildUrl(API_ENDPOINTS.GENERATE.PERMISSIONS),
  registerRoutes: () => buildUrl(API_ENDPOINTS.GENERATE.REGISTER_ROUTES),
  activate: () => buildUrl(API_ENDPOINTS.GENERATE.ACTIVATE),
  generateCode: () => buildUrl(API_ENDPOINTS.GENERATE.CODE),
  codePreview: () => buildUrl(API_ENDPOINTS.GENERATE.CODE_PREVIEW),
  
  // 路由管理
  routes: () => buildUrl(API_ENDPOINTS.ROUTES.BASE),
  registerRoute: () => buildUrl(API_ENDPOINTS.ROUTES.REGISTER),
  routeById: (id: string) => buildUrl(API_ENDPOINTS.ROUTES.BY_ID(id)),
  
  // 角色管理
  roles: () => buildUrl(API_ENDPOINTS.ROLES.BASE),
  roleById: (id: string) => buildUrl(API_ENDPOINTS.ROLES.BY_ID(id)),
  
  // 权限管理
  permissions: () => buildUrl(API_ENDPOINTS.PERMISSIONS.BASE),
  checkPermission: () => buildUrl(API_ENDPOINTS.PERMISSIONS.CHECK),
  userApis: () => buildUrl(API_ENDPOINTS.PERMISSIONS.USER_APIS),
  roleApiPermission: () => buildUrl(API_ENDPOINTS.PERMISSIONS.ROLE_API),
  batchUpdatePermissions: () => buildUrl(API_ENDPOINTS.PERMISSIONS.BATCH_UPDATE),
  
  // AI交互
  aiCommand: () => buildUrl(API_ENDPOINTS.AI.COMMAND),
  
  // 健康检查
  health: () => buildUrl(API_ENDPOINTS.HEALTH),
};
