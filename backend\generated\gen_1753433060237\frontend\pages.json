```json
{
  "type": "page",
  "title": "商品管理",
  "body": [
    {
      "type": "crud",
      "api": "/api/products",
      "syncLocation": false,
      "filter": {
        "title": "搜索",
        "submitText": "",
        "controls": [
          {
            "type": "text",
            "name": "name",
            "label": "商品名称",
            "placeholder": "请输入商品名称"
          },
          {
            "type": "text",
            "name": "category",
            "label": "分类",
            "placeholder": "请输入分类"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "type": "text"
        },
        {
          "name": "name",
          "label": "商品名称",
          "type": "text"
        },
        {
          "name": "price",
          "label": "价格",
          "type": "text"
        },
        {
          "name": "category",
          "label": "分类",
          "type": "text"
        },
        {
          "type": "operation",
          "label": "操作",
          "buttons": [
            {
              "type": "button",
              "actionType": "drawer",
              "label": "查看",
              "drawer": {
                "title": "查看详情",
                "size": "lg",
                "body": {
                  "type": "form",
                  "initApi": "/api/products/${id}",
                  "controls": [
                    {
                      "type": "static",
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "type": "static",
                      "name": "name",
                      "label": "商品名称"
                    },
                    {
                      "type": "static",
                      "name": "price",
                      "label": "价格"
                    },
                    {
                      "type": "static",
                      "name": "category",
                      "label": "分类"
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "actionType": "drawer",
              "label": "编辑",
              "drawer": {
                "title": "编辑商品",
                "size": "lg",
                "body": {
                  "type": "form",
                  "api": "put:/api/products/${id}",
                  "controls": [
                    {
                      "type": "hidden",
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "type": "text",
                      "name": "name",
                      "label": "商品名称",
                      "required": true
                    },
                    {
                      "type": "number",
                      "name": "price",
                      "label": "价格",
                      "required": true
                    },
                    {
                      "type": "text",
                      "name": "category",
                      "label": "分类",
                      "required": true
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "actionType": "ajax",
              "label": "删除",
              "confirmText": "您确认要删除该商品?",
              "api": "delete:/api/products/${id}"
            }
          ]
        }
      ],
      "headerToolbar": [
        {
          "type": "button",
          "actionType": "drawer",
          "label": "新增商品",
          "primary": true,
          "drawer": {
            "title": "新增商品",
            "size": "lg",
            "body": {
              "type": "form",
              "api": "post:/api/products",
              "controls": [
                {
                  "type": "text",
                  "name": "name",
                  "label": "商品名称",
                  "required": true
                },
                {
                  "type": "number",
                  "name": "price",
                  "label": "价格",
                  "required": true
                },
                {
                  "type": "text",
                  "name": "category",
                  "label": "分类",
                  "required": true
                }
              ]
            }
          }
        },
        "bulkActions",
        "pagination"
      ],
      "footerToolbar": [
        "statistics",
        "pagination"
      ]
    }
  ]
}
```