"""
代码生成相关的Pydantic Schema
"""
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class GenerationOptions(BaseModel):
    """代码生成选项"""
    include_database: bool = Field(True, description="是否包含数据库代码")
    include_backend: bool = Field(True, description="是否包含后端代码")
    include_frontend: bool = Field(True, description="是否包含前端代码")
    include_tests: bool = Field(False, description="是否包含测试代码")
    include_documentation: bool = Field(True, description="是否包含文档")
    target_directory: Optional[str] = Field(None, description="目标目录")


class CompleteGenerationRequest(BaseModel):
    """完整系统生成请求"""
    scenario_id: Optional[str] = Field(None, description="场景ID")
    options: Optional[GenerationOptions] = Field(None, description="生成选项")


class DatabaseGenerationRequest(BaseModel):
    """数据库生成请求"""
    entities: Optional[List[str]] = Field(None, description="实体ID列表")
    options: Optional[Dict[str, Any]] = Field(None, description="生成选项")


class APIGenerationRequest(BaseModel):
    """API生成请求"""
    apis: Optional[List[str]] = Field(None, description="API ID列表")
    options: Optional[Dict[str, Any]] = Field(None, description="生成选项")


class PermissionGenerationRequest(BaseModel):
    """权限生成请求"""
    roles: Optional[List[str]] = Field(None, description="角色列表")
    permissions: Optional[List[str]] = Field(None, description="权限列表")
    options: Optional[Dict[str, Any]] = Field(None, description="生成选项")


class ComponentResult(BaseModel):
    """组件生成结果"""
    success: bool = Field(..., description="是否成功")
    files_generated: Optional[List[str]] = Field(None, description="生成的文件列表")
    usage: Optional[Dict[str, Any]] = Field(None, description="API使用统计")
    error: Optional[str] = Field(None, description="错误信息")


class GenerationResult(BaseModel):
    """代码生成结果"""
    generation_id: str = Field(..., description="生成ID")
    scenario_id: Optional[str] = Field(None, description="场景ID")
    target_directory: str = Field(..., description="目标目录")
    components: Dict[str, ComponentResult] = Field(..., description="各组件生成结果")
    files_generated: List[str] = Field(..., description="所有生成的文件")
    started_at: str = Field(..., description="开始时间")
    completed_at: Optional[str] = Field(None, description="完成时间")
    status: str = Field(..., description="状态")


class GenerationResponse(BaseModel):
    """生成响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[GenerationResult] = Field(None, description="生成结果")
    error: Optional[str] = Field(None, description="错误信息")


class ActivationRequest(BaseModel):
    """激活请求"""
    generation_id: str = Field(..., description="生成ID")
    options: Optional[Dict[str, Any]] = Field(None, description="激活选项")


class ActivationResponse(BaseModel):
    """激活响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="激活结果")
    error: Optional[str] = Field(None, description="错误信息")


class GenerationStatusResponse(BaseModel):
    """生成状态响应"""
    success: bool = Field(..., description="是否成功")
    current_generation: Optional[GenerationResult] = Field(None, description="当前生成任务")
    recent_generations: Optional[List[GenerationResult]] = Field(None, description="最近的生成任务")
    error: Optional[str] = Field(None, description="错误信息")
