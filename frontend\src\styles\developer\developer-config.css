/**
 * 开发者配置界面样式
 */

:root {
  /* 浅色玻璃主题配色 */
  --dev-bg-primary: #f8f9fa;
  --dev-bg-secondary: rgba(255, 255, 255, 0.8);
  --dev-bg-tertiary: rgba(255, 255, 255, 0.6);
  --dev-bg-hover: rgba(0, 122, 255, 0.1);
  --dev-bg-glass: rgba(255, 255, 255, 0.25);

  /* 文字颜色 */
  --dev-text-primary: #1d1d1f;
  --dev-text-secondary: #6e6e73;
  --dev-text-muted: #8e8e93;

  /* 强调色 */
  --dev-accent-blue: #007aff;
  --dev-accent-green: #34c759;
  --dev-accent-orange: #ff9500;
  --dev-accent-red: #ff3b30;

  /* 边框和分割线 */
  --dev-border: rgba(0, 0, 0, 0.1);
  --dev-divider: rgba(0, 0, 0, 0.05);

  /* 阴影 */
  --dev-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --dev-shadow-md: 0 4px 20px rgba(0, 0, 0, 0.15);
  --dev-shadow-lg: 0 8px 40px rgba(0, 0, 0, 0.2);

  /* 玻璃效果 */
  --dev-glass-blur: blur(20px);
  --dev-glass-border: 1px solid rgba(255, 255, 255, 0.3);

  /* 圆角 */
  --dev-radius-sm: 8px;
  --dev-radius-md: 12px;
  --dev-radius-lg: 20px;
  --dev-radius-xl: 24px;

  /* 动画 */
  --dev-transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 认证步骤样式 */
.authentication-step {
  background: #ffffff;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.authentication-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.authentication-step .ant-card {
  background: var(--dev-bg-glass);
  backdrop-filter: var(--dev-glass-blur);
  -webkit-backdrop-filter: var(--dev-glass-blur);
  border: var(--dev-glass-border);
  border-radius: var(--dev-radius-xl);
  box-shadow: var(--dev-shadow-lg);
  position: relative;
  overflow: hidden;
}

.authentication-step .ant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.authentication-step .ant-card-body {
  padding: 40px;
}

.authentication-step .ant-form-item-label > label {
  color: var(--dev-text-primary);
  font-weight: 600;
  font-size: 14px;
}

.authentication-step .ant-input-affix-wrapper {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--dev-radius-md);
  color: var(--dev-text-primary);
  transition: var(--dev-transition);
}

.authentication-step .ant-input-affix-wrapper:hover {
  border-color: var(--dev-accent-blue);
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.authentication-step .ant-input-affix-wrapper-focused {
  border-color: var(--dev-accent-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1), 0 4px 12px rgba(0, 122, 255, 0.15);
  background: rgba(255, 255, 255, 0.6);
  transform: translateY(-1px);
}

.authentication-step .ant-input {
  background: transparent;
  color: var(--dev-text-primary);
  font-weight: 500;
}

.authentication-step .ant-input::placeholder {
  color: var(--dev-text-muted);
  font-weight: 400;
}

.authentication-step .ant-input-prefix {
  color: var(--dev-text-secondary);
}

.authentication-step .ant-checkbox-wrapper {
  color: var(--dev-text-secondary);
  font-weight: 500;
}

.authentication-step .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--dev-accent-blue);
  border-color: var(--dev-accent-blue);
}

.authentication-step .ant-btn-primary {
  background: linear-gradient(135deg, var(--dev-accent-blue) 0%, #0056b3 100%);
  border: none;
  border-radius: var(--dev-radius-md);
  font-weight: 600;
  transition: var(--dev-transition);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.authentication-step .ant-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.authentication-step .ant-btn-primary:hover::before {
  left: 100%;
}

.authentication-step .ant-btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, var(--dev-accent-blue) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
}

.authentication-step .ant-alert {
  background: rgba(255, 59, 48, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 59, 48, 0.2);
  border-radius: var(--dev-radius-md);
}

.authentication-step .ant-alert-error {
  color: var(--dev-accent-red);
}

.authentication-step .ant-alert-message {
  color: var(--dev-text-primary);
  font-weight: 600;
}

.authentication-step .ant-alert-description {
  color: var(--dev-text-secondary);
}

/* 开发者配置主界面样式 */
.developer-config-main {
  background: #ffffff;
  min-height: 100vh;
  color: var(--dev-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.developer-config-main .ant-layout-header {
  background: var(--dev-bg-glass);
  backdrop-filter: var(--dev-glass-blur);
  -webkit-backdrop-filter: var(--dev-glass-blur);
  border-bottom: var(--dev-glass-border);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.developer-config-main .ant-layout-content {
  background: transparent;
}

.developer-config-main .ant-layout-footer {
  background: var(--dev-bg-glass);
  backdrop-filter: var(--dev-glass-blur);
  -webkit-backdrop-filter: var(--dev-glass-blur);
  border-top: var(--dev-glass-border);
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.developer-config-main .ant-btn-text {
  color: var(--dev-text-secondary);
  transition: var(--dev-transition);
  border-radius: var(--dev-radius-md);
  font-weight: 500;
}

.developer-config-main .ant-btn-text:hover {
  color: var(--dev-text-primary);
  background: var(--dev-bg-hover);
  transform: translateY(-1px);
}

.developer-config-main .ant-btn-primary {
  background: linear-gradient(135deg, var(--dev-accent-blue) 0%, #0056b3 100%);
  border: none;
  border-radius: var(--dev-radius-md);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.developer-config-main .ant-btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, var(--dev-accent-blue) 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.developer-config-main .ant-btn:disabled {
  background: var(--dev-bg-tertiary);
  border-color: var(--dev-border);
  color: var(--dev-text-muted);
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .authentication-step {
    padding: 20px;
  }
  
  .authentication-step .ant-card-body {
    padding: 24px;
  }
  
  .developer-config-main .ant-layout-header {
    padding: 0 16px;
  }
  
  .developer-config-main .ant-layout-footer {
    padding: 12px 16px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes glassShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.authentication-step .ant-card {
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.authentication-step .ant-btn-primary:active {
  transform: translateY(0) scale(0.98);
}

/* 玻璃效果增强 */
.authentication-step .ant-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  animation: glassShimmer 3s ease-in-out infinite;
  pointer-events: none;
}

/* 场景配置步骤样式 */
.scenario-config-step {
  background: transparent;
  min-height: calc(100vh - 200px);
}

.scenario-config-step .ant-card {
  background: var(--dev-bg-glass);
  backdrop-filter: var(--dev-glass-blur);
  -webkit-backdrop-filter: var(--dev-glass-blur);
  border: var(--dev-glass-border);
  border-radius: var(--dev-radius-lg);
  box-shadow: var(--dev-shadow-md);
  margin-bottom: 24px;
}

.scenario-config-step .ant-card-head {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.scenario-config-step .ant-card-head-title {
  color: var(--dev-text-primary);
  font-weight: 600;
  font-size: 16px;
}

.scenario-config-step .ant-form-item-label > label {
  color: var(--dev-text-primary);
  font-weight: 600;
  font-size: 14px;
}

.scenario-config-step .ant-input,
.scenario-config-step .ant-input-affix-wrapper {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--dev-radius-md);
  color: var(--dev-text-primary);
  transition: var(--dev-transition);
}

.scenario-config-step .ant-input:hover,
.scenario-config-step .ant-input-affix-wrapper:hover {
  border-color: var(--dev-accent-blue);
  background: rgba(255, 255, 255, 0.7);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.scenario-config-step .ant-input:focus,
.scenario-config-step .ant-input-affix-wrapper-focused {
  border-color: var(--dev-accent-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1), 0 4px 12px rgba(0, 122, 255, 0.15);
  background: rgba(255, 255, 255, 0.8);
}

.scenario-config-step .ant-select-selector {
  background: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: var(--dev-radius-md) !important;
}

.scenario-config-step .ant-tag {
  background: rgba(0, 122, 255, 0.1);
  border: 1px solid rgba(0, 122, 255, 0.3);
  border-radius: var(--dev-radius-sm);
  color: var(--dev-accent-blue);
  font-weight: 500;
}

.scenario-config-step .ant-tag-close-icon {
  color: var(--dev-accent-blue);
}

.scenario-config-step .ant-tag-close-icon:hover {
  color: var(--dev-accent-red);
}

.scenario-config-step .selected-type {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.2);
}

.scenario-config-step .ant-btn-primary {
  background: linear-gradient(135deg, var(--dev-accent-blue) 0%, #0056b3 100%);
  border: none;
  border-radius: var(--dev-radius-md);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: var(--dev-transition);
}

.scenario-config-step .ant-btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, var(--dev-accent-blue) 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.scenario-config-step .ant-btn {
  border-radius: var(--dev-radius-md);
  font-weight: 500;
  transition: var(--dev-transition);
}

.scenario-config-step .ant-btn:hover {
  transform: translateY(-1px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dev-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--dev-bg-hover);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dev-text-muted);
}
