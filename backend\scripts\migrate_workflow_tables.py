"""
工作流表结构迁移脚本
将旧的工作流表结构迁移到新的AI指导版结构
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import get_db_session, engine
from sqlalchemy import text, inspect


def check_table_exists(table_name: str) -> bool:
    """检查表是否存在"""
    try:
        inspector = inspect(engine)
        return table_name in inspector.get_table_names()
    except Exception as e:
        print(f"❌ 检查表是否存在失败: {e}")
        return False


def check_column_exists(table_name: str, column_name: str) -> bool:
    """检查列是否存在"""
    try:
        inspector = inspect(engine)
        columns = [col['name'] for col in inspector.get_columns(table_name)]
        return column_name in columns
    except Exception as e:
        print(f"❌ 检查列是否存在失败: {e}")
        return False


def migrate_workflows_table():
    """迁移workflows表"""
    print("🔄 迁移workflows表...")
    
    try:
        with get_db_session() as db:
            # 检查是否需要添加新列
            new_columns = [
                ("business_scenario", "VARCHAR(100)", "业务场景标识"),
                ("user_intents", "JSON", "用户意图列表JSON"),
                ("trigger_keywords", "JSON", "触发关键词列表JSON"),
                ("inputs", "JSON", "工作流输入定义JSON"),
                ("workflow_metadata", "JSON", "元数据JSON"),
                ("node_count", "INT DEFAULT 0", "节点数量"),
                ("created_by", "VARCHAR(50)", "创建者ID")
            ]
            
            for column_name, column_type, comment in new_columns:
                if not check_column_exists("workflows", column_name):
                    sql = f"ALTER TABLE workflows ADD COLUMN {column_name} {column_type} COMMENT '{comment}'"
                    db.execute(text(sql))
                    print(f"   ✅ 添加列: {column_name}")
                else:
                    print(f"   ⚠️ 列已存在: {column_name}")
            
            # 删除旧列（如果存在）
            old_columns = ["trigger_type", "steps_config", "conditions_config", "variables_config", "step_count"]
            for column_name in old_columns:
                if check_column_exists("workflows", column_name):
                    try:
                        sql = f"ALTER TABLE workflows DROP COLUMN {column_name}"
                        db.execute(text(sql))
                        print(f"   ✅ 删除旧列: {column_name}")
                    except Exception as e:
                        print(f"   ⚠️ 删除列失败: {column_name} - {e}")
            
            db.commit()
            print("✅ workflows表迁移完成")
            
    except Exception as e:
        print(f"❌ workflows表迁移失败: {e}")
        return False
    
    return True


def create_workflow_nodes_table():
    """创建workflow_nodes表"""
    print("🔄 创建workflow_nodes表...")
    
    if check_table_exists("workflow_nodes"):
        print("   ⚠️ workflow_nodes表已存在")
        return True
    
    try:
        with get_db_session() as db:
            sql = """
            CREATE TABLE workflow_nodes (
                id VARCHAR(50) PRIMARY KEY COMMENT '节点唯一标识符',
                workflow_id VARCHAR(50) NOT NULL COMMENT '工作流ID',
                node_id VARCHAR(50) NOT NULL COMMENT '节点在工作流中的ID',
                name VARCHAR(200) NOT NULL COMMENT '节点名称',
                type ENUM('start', 'api_call', 'user_input', 'condition', 'notification', 'end') NOT NULL COMMENT '节点类型',
                description TEXT COMMENT '节点描述',
                position_x INT COMMENT 'X坐标',
                position_y INT COMMENT 'Y坐标',
                parent_nodes JSON COMMENT '父节点列表JSON',
                child_nodes JSON COMMENT '子节点配置JSON',
                config JSON COMMENT '节点配置JSON',
                outputs JSON COMMENT '节点输出定义JSON',
                ai_instructions JSON COMMENT 'AI执行指令JSON',
                is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_workflow_nodes_workflow (workflow_id),
                INDEX idx_workflow_nodes_node (node_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流节点表 (AI指导版)'
            """
            
            db.execute(text(sql))
            db.commit()
            print("✅ workflow_nodes表创建完成")
            
    except Exception as e:
        print(f"❌ workflow_nodes表创建失败: {e}")
        return False
    
    return True


def migrate_workflow_steps_to_nodes():
    """将workflow_steps数据迁移到workflow_nodes"""
    print("🔄 迁移workflow_steps数据到workflow_nodes...")
    
    if not check_table_exists("workflow_steps"):
        print("   ⚠️ workflow_steps表不存在，跳过数据迁移")
        return True
    
    try:
        with get_db_session() as db:
            # 查询旧数据
            result = db.execute(text("SELECT * FROM workflow_steps"))
            old_steps = result.fetchall()
            
            if not old_steps:
                print("   ⚠️ workflow_steps表中没有数据")
                return True
            
            # 迁移数据
            for step in old_steps:
                # 转换步骤类型到节点类型
                type_mapping = {
                    "start": "start",
                    "end": "end",
                    "form": "user_input",
                    "api_call": "api_call",
                    "condition": "condition",
                    "approval": "user_input",
                    "notification": "notification",
                    "timer": "condition",
                    "parallel": "condition"
                }
                
                node_type = type_mapping.get(step.type, "user_input")
                
                # 插入到新表
                insert_sql = """
                INSERT INTO workflow_nodes 
                (id, workflow_id, node_id, name, type, description, position_x, position_y, 
                 parent_nodes, child_nodes, config, outputs, ai_instructions, is_active, created_at, updated_at)
                VALUES (:id, :workflow_id, :node_id, :name, :type, :description, :position_x, :position_y,
                        :parent_nodes, :child_nodes, :config, :outputs, :ai_instructions, :is_active, :created_at, :updated_at)
                """
                
                db.execute(text(insert_sql), {
                    "id": step.id,
                    "workflow_id": step.workflow_id,
                    "node_id": step.step_id,
                    "name": step.name,
                    "type": node_type,
                    "description": step.description or "",
                    "position_x": step.position_x or 0,
                    "position_y": step.position_y or 0,
                    "parent_nodes": "[]",
                    "child_nodes": step.next_steps or "[]",
                    "config": step.config or "{}",
                    "outputs": "[]",
                    "ai_instructions": "{}",
                    "is_active": step.is_active,
                    "created_at": step.created_at,
                    "updated_at": step.updated_at
                })
            
            db.commit()
            print(f"   ✅ 迁移了 {len(old_steps)} 条记录")
            
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("🔧 AILF 工作流表结构迁移工具")
    print("=" * 60)
    
    success = True
    
    # 1. 迁移workflows表
    if not migrate_workflows_table():
        success = False
    
    # 2. 创建workflow_nodes表
    if not create_workflow_nodes_table():
        success = False
    
    # 3. 迁移数据
    if not migrate_workflow_steps_to_nodes():
        success = False
    
    if success:
        print("\n✅ 所有表结构迁移成功！")
        print("现在可以运行 init_workflow_data.py 来初始化示例数据")
        return 0
    else:
        print("\n❌ 表结构迁移失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
