/**
 * API路由管理步骤样式 - Apple风格设计
 */

.api-route-step {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 步骤头部 */
.api-route-step .step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.api-route-step .step-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.api-route-step .step-icon {
  font-size: 32px;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 12px;
  border-radius: 12px;
}

.api-route-step .step-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1d1d1f;
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.api-route-step .step-title p {
  margin: 4px 0 0 0;
  color: #6e6e73;
  font-size: 16px;
}

.api-route-step .step-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.api-route-step .step-statistics {
  margin-bottom: 24px;
}

.api-route-step .step-statistics .ant-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.api-route-step .step-statistics .ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.api-route-step .step-statistics .ant-statistic-title {
  color: #6e6e73;
  font-weight: 500;
}

.api-route-step .step-statistics .ant-statistic-content {
  color: #1d1d1f;
  font-weight: 600;
}

/* 筛选器 */
.api-route-step .step-filters {
  margin-bottom: 24px;
}

.api-route-step .step-filters .ant-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 内容区域 */
.api-route-step .step-content {
  margin-bottom: 24px;
}

.api-route-step .step-content .ant-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.api-route-step .ant-table {
  background: transparent;
}

.api-route-step .ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: #1d1d1f;
  font-weight: 600;
}

.api-route-step .ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.api-route-step .ant-table-tbody > tr:hover > td {
  background: rgba(255, 255, 255, 0.1);
}

/* API信息单元格 */
.api-route-step .api-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.api-route-step .api-name {
  font-weight: 600;
  color: #1d1d1f;
  font-size: 14px;
}

.api-route-step .api-id {
  font-size: 12px;
  color: #6e6e73;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.api-route-step .api-description {
  font-size: 12px;
  color: #8e8e93;
  margin-top: 2px;
}

/* 端点单元格 */
.api-route-step .endpoint-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.api-route-step .endpoint-method {
  display: flex;
  align-items: center;
}

.api-route-step .endpoint-path {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #1d1d1f;
}

/* 统计单元格 */
.api-route-step .statistics-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.api-route-step .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  line-height: 1.2;
}

.api-route-step .stat-label {
  color: #6e6e73;
  font-weight: 500;
  min-width: 60px;
}

.api-route-step .stat-value {
  color: #1d1d1f;
  font-weight: 500;
  text-align: right;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 步骤底部 */
.api-route-step .step-footer {
  display: flex;
  justify-content: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 模态框样式 */
.api-route-modal .ant-modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
}

.api-route-modal .ant-modal-header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px 20px 0 0;
}

.api-route-modal .ant-modal-title {
  color: #1d1d1f;
  font-weight: 600;
}

.api-route-modal .ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.api-route-form .ant-form-item-label > label {
  color: #1d1d1f;
  font-weight: 500;
}

.api-route-form .ant-input,
.api-route-form .ant-select-selector,
.api-route-form .ant-input-affix-wrapper {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.api-route-form .ant-input:focus,
.api-route-form .ant-select-focused .ant-select-selector,
.api-route-form .ant-input-affix-wrapper:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.api-route-form .form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 测试信息 */
.test-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 8px;
}

.test-info code {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

/* 按钮样式 */
.api-route-step .ant-btn-primary {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.api-route-step .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
}

.api-route-step .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.api-route-step .ant-btn:hover {
  transform: translateY(-1px);
}

/* 标签样式 */
.api-route-step .ant-tag {
  border-radius: 6px;
  font-weight: 500;
  border: none;
}

/* 徽章样式 */
.api-route-step .ant-badge-status-dot {
  width: 8px;
  height: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-route-step {
    padding: 16px;
  }
  
  .api-route-step .step-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .api-route-step .step-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .api-route-step .step-statistics .ant-col {
    margin-bottom: 16px;
  }
}
