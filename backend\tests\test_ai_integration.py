"""
AI集成测试脚本
测试阿里云百炼API集成和AI服务功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.ai.bailian_client import bailian_client
from app.services.ai_service import ai_service
from app.services.generation_service import generation_service


async def test_bailian_client():
    """测试百炼客户端"""
    print("🔧 测试阿里云百炼API客户端...")
    
    try:
        # 健康检查
        health_result = await bailian_client.health_check()
        print(f"健康检查结果: {health_result}")
        
        if health_result["status"] == "healthy":
            print("✅ 百炼API客户端连接正常")
            
            # 测试代码生成
            print("\n🔧 测试代码生成功能...")
            code_result = await bailian_client.generate_code(
                "请生成一个简单的Python函数，用于计算两个数的和"
            )
            print(f"代码生成结果: {code_result['success']}")
            if code_result["success"]:
                print(f"生成的代码:\n{code_result['content'][:200]}...")
            
            # 测试意图分析
            print("\n🔧 测试意图分析功能...")
            intent_result = await bailian_client.analyze_intent(
                "我想查看所有用户的信息"
            )
            print(f"意图分析结果: {intent_result['success']}")
            if intent_result["success"]:
                print(f"分析结果: {intent_result['intent']}")
            
        else:
            print("❌ 百炼API客户端连接失败")
            print(f"错误信息: {health_result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 百炼客户端测试失败: {str(e)}")


async def test_ai_service():
    """测试AI服务"""
    print("\n🔧 测试AI服务...")
    
    try:
        # 测试命令处理
        command_result = await ai_service.process_command(
            "显示用户列表",
            {"user_id": "test_user", "session_id": "test_session"}
        )
        print(f"命令处理结果: {command_result['success']}")
        if command_result["success"]:
            print(f"响应文本: {command_result['data']['response_text']}")
        
        # 测试实体推荐
        print("\n🔧 测试实体推荐功能...")
        entity_result = await ai_service.suggest_entities(
            "电商系统，包含用户、商品、订单管理"
        )
        print(f"实体推荐结果: {entity_result['success']}")
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {str(e)}")


async def test_generation_service():
    """测试代码生成服务"""
    print("\n🔧 测试代码生成服务...")
    
    try:
        # 测试获取生成状态
        status_result = await generation_service.get_generation_status()
        print(f"生成状态查询结果: {status_result['success']}")
        
        # 测试完整系统生成（使用示例配置）
        print("\n🔧 测试完整系统生成...")
        generation_result = await generation_service.generate_complete_system()
        print(f"系统生成结果: {generation_result['success']}")
        if generation_result["success"]:
            print(f"生成ID: {generation_result['data']['generation_id']}")
            print(f"生成目录: {generation_result['data']['target_directory']}")
        
    except Exception as e:
        print(f"❌ 代码生成服务测试失败: {str(e)}")


async def main():
    """主测试函数"""
    print("🚀 开始AI集成测试")
    print("=" * 60)
    
    # 测试百炼客户端
    await test_bailian_client()
    
    # 测试AI服务
    await test_ai_service()
    
    # 测试代码生成服务
    await test_generation_service()
    
    print("\n" + "=" * 60)
    print("✅ AI集成测试完成")


if __name__ == "__main__":
    asyncio.run(main())
