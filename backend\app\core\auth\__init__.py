# Auth Core Package
from .jwt_handler import JWTHandler

# 创建全局JWT处理器实例
jwt_handler = JWTHandler()

# 导出认证依赖函数
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from typing import Dict, Any

security = HTTPBearer()

def verify_developer_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    验证开发者令牌的依赖函数

    Args:
        credentials: HTTP Bearer认证凭据

    Returns:
        解析后的token数据

    Raises:
        HTTPException: 当token无效时抛出401错误
    """
    try:
        # 验证token
        result = jwt_handler.verify_token(credentials.credentials)

        if not result["valid"]:
            error_detail = result.get("details", "无效的认证令牌")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=error_detail,
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 返回payload数据，包含用户信息
        return {
            "user_id": result["payload"].get("sub", "developer"),
            "user_type": result["user_type"],
            "remaining_time": result["remaining_time"]
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证令牌验证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )
