"""
用户权限服务
专门处理用户权限获取和API访问控制
"""
import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.role import RoleDBModel

logger = logging.getLogger(__name__)


class UserPermissionService:
    """用户权限服务"""
    
    def get_user_permissions(self, user: User, db: Session) -> List[str]:
        """
        获取用户权限列表
        
        Args:
            user: 用户对象
            db: 数据库会话
            
        Returns:
            用户权限列表
        """
        try:
            if not user or not user.role_id:
                return ["user:read"]  # 默认最小权限
            
            # 获取用户角色
            user_role = db.query(RoleDBModel).filter(RoleDBModel.id == user.role_id).first()
            if not user_role:
                return ["user:read"]
            
            # 从角色的权限关联中获取权限
            permissions = []
            for permission in user_role.permissions:
                permissions.append(permission.name)
            
            # 如果没有关联权限，根据角色级别返回默认权限
            if not permissions:
                permissions = self._get_default_permissions_by_level(user_role.level)
            
            return permissions
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {str(e)}")
            return ["user:read"]  # 返回最小权限
    
    def get_user_accessible_apis(self, user: User, db: Session) -> List[Dict[str, Any]]:
        """
        获取用户可访问的API列表
        
        Args:
            user: 用户对象
            db: 数据库会话
            
        Returns:
            用户可访问的API列表
        """
        try:
            if not user or not user.role_id:
                return self._get_guest_apis()
            
            # 获取用户角色
            user_role = db.query(RoleDBModel).filter(RoleDBModel.id == user.role_id).first()
            if not user_role:
                return self._get_guest_apis()
            
            # 根据角色级别返回可访问的API
            return self._get_apis_by_role_level(user_role.level)
            
        except Exception as e:
            logger.error(f"获取用户可访问API失败: {str(e)}")
            return self._get_guest_apis()
    
    def get_user_role_info(self, user: User, db: Session) -> Dict[str, Any]:
        """
        获取用户角色信息
        
        Args:
            user: 用户对象
            db: 数据库会话
            
        Returns:
            用户角色信息
        """
        try:
            if not user or not user.role_id:
                return {
                    "id": "guest",
                    "name": "访客",
                    "level": 0
                }
            
            # 获取用户角色
            user_role = db.query(RoleDBModel).filter(RoleDBModel.id == user.role_id).first()
            if not user_role:
                return {
                    "id": "guest",
                    "name": "访客", 
                    "level": 0
                }
            
            return {
                "id": user_role.id,
                "name": user_role.name,
                "level": user_role.level
            }
            
        except Exception as e:
            logger.error(f"获取用户角色信息失败: {str(e)}")
            return {
                "id": "guest",
                "name": "访客",
                "level": 0
            }
    
    def check_api_permission(self, user: User, endpoint: str, method: str, db: Session) -> bool:
        """
        检查用户是否有权限访问指定API
        
        Args:
            user: 用户对象
            endpoint: API端点
            method: HTTP方法
            db: 数据库会话
            
        Returns:
            是否有权限
        """
        try:
            accessible_apis = self.get_user_accessible_apis(user, db)
            
            for api in accessible_apis:
                if (api.get("endpoint") == endpoint and 
                    api.get("method").upper() == method.upper()):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查API权限失败: {str(e)}")
            return False
    
    def _get_default_permissions_by_level(self, level: int) -> List[str]:
        """根据角色级别获取默认权限"""
        if level >= 9:  # 超级管理员
            return ["*:*"]
        elif level >= 5:  # 管理员
            return [
                "user:*", "role:*", "permission:*",
                "data:*", "report:read", "system:config"
            ]
        elif level >= 1:  # 普通用户
            return [
                "user:read", "user:update_self",
                "data:query", "data:read"
            ]
        else:  # 访客
            return ["user:read"]
    
    def _get_apis_by_role_level(self, level: int) -> List[Dict[str, Any]]:
        """根据角色级别获取可访问的API"""
        # 基础API（所有用户都可以访问）
        base_apis = [
            {
                "endpoint": "/api/user/me",
                "method": "GET",
                "description": "获取个人信息",
                "resource": "user",
                "action": "read"
            },
            {
                "endpoint": "/api/user/logout",
                "method": "POST",
                "description": "用户登出",
                "resource": "user",
                "action": "logout"
            }
        ]
        
        if level >= 1:  # 普通用户及以上
            base_apis.extend([
                {
                    "endpoint": "/api/command",
                    "method": "POST",
                    "description": "AI命令处理",
                    "resource": "ai",
                    "action": "command"
                },
                {
                    "endpoint": "/api/ai/validate-schema",
                    "method": "POST",
                    "description": "Schema校验",
                    "resource": "ai",
                    "action": "validate"
                },
                {
                    "endpoint": "/api/user/permissions",
                    "method": "GET",
                    "description": "获取用户权限",
                    "resource": "user",
                    "action": "read"
                }
            ])
        
        if level >= 5:  # 管理员及以上
            base_apis.extend([
                {
                    "endpoint": "/api/users",
                    "method": "GET",
                    "description": "用户列表查询",
                    "resource": "users",
                    "action": "list"
                },
                {
                    "endpoint": "/api/users",
                    "method": "POST",
                    "description": "创建用户",
                    "resource": "users",
                    "action": "create"
                },
                {
                    "endpoint": "/api/users/{id}",
                    "method": "PUT",
                    "description": "更新用户",
                    "resource": "users",
                    "action": "update"
                },
                {
                    "endpoint": "/api/roles",
                    "method": "GET",
                    "description": "角色列表查询",
                    "resource": "roles",
                    "action": "list"
                }
            ])
        
        if level >= 9:  # 超级管理员
            base_apis.extend([
                {
                    "endpoint": "/api/users/{id}",
                    "method": "DELETE",
                    "description": "删除用户",
                    "resource": "users",
                    "action": "delete"
                },
                {
                    "endpoint": "/api/roles",
                    "method": "POST",
                    "description": "创建角色",
                    "resource": "roles",
                    "action": "create"
                },
                {
                    "endpoint": "/api/roles/{id}",
                    "method": "PUT",
                    "description": "更新角色",
                    "resource": "roles",
                    "action": "update"
                },
                {
                    "endpoint": "/api/roles/{id}",
                    "method": "DELETE",
                    "description": "删除角色",
                    "resource": "roles",
                    "action": "delete"
                },
                {
                    "endpoint": "/api/system/config",
                    "method": "GET",
                    "description": "系统配置查询",
                    "resource": "system",
                    "action": "read"
                },
                {
                    "endpoint": "/api/system/config",
                    "method": "PUT",
                    "description": "系统配置更新",
                    "resource": "system",
                    "action": "update"
                }
            ])
        
        return base_apis
    
    def _get_guest_apis(self) -> List[Dict[str, Any]]:
        """获取访客可访问的API"""
        return [
            {
                "endpoint": "/api/user/login",
                "method": "POST",
                "description": "用户登录",
                "resource": "user",
                "action": "login"
            },
            {
                "endpoint": "/api/user/register",
                "method": "POST",
                "description": "用户注册",
                "resource": "user",
                "action": "register"
            },
            {
                "endpoint": "/health",
                "method": "GET",
                "description": "健康检查",
                "resource": "system",
                "action": "health"
            }
        ]
    
    def get_user_context_for_ai(self, user: Optional[User], db: Session) -> Dict[str, Any]:
        """
        获取用于AI的用户上下文信息
        
        Args:
            user: 用户对象（可选）
            db: 数据库会话
            
        Returns:
            用户上下文信息
        """
        if not user:
            return {
                "user_id": None,
                "user_permissions": [],
                "accessible_apis": self._get_guest_apis(),
                "user_role": {
                    "id": "guest",
                    "name": "访客",
                    "level": 0
                }
            }
        
        return {
            "user_id": user.id,
            "user_permissions": self.get_user_permissions(user, db),
            "accessible_apis": self.get_user_accessible_apis(user, db),
            "user_role": self.get_user_role_info(user, db)
        }


# 全局用户权限服务实例
user_permission_service = UserPermissionService()
