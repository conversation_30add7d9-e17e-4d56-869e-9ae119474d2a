/**
 * 开发者认证Hook
 */

import { useState, useCallback, useEffect } from 'react';
import { authAPI } from '../../services/developer/authAPI';
import type { AuthState, AuthFormData } from '../../types/developer/auth';
import { APIError } from '../../services/common/apiClient';

const STORAGE_KEY = 'ailf_developer_auth';

export const useDeveloperAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    token: null,
    expiresAt: null,
    isLoading: false,
    error: null,
  });

  // 从本地存储加载认证状态
  useEffect(() => {
    const savedAuth = localStorage.getItem(STORAGE_KEY);
    if (savedAuth) {
      try {
        const parsed = JSON.parse(savedAuth);
        const expiresAt = new Date(parsed.expiresAt);
        
        // 检查token是否过期和是否为有效token
        if (expiresAt > new Date() && parsed.token && !parsed.token.includes('fake-token')) {
          setAuthState({
            isAuthenticated: true,
            token: parsed.token,
            expiresAt: parsed.expiresAt,
            isLoading: false,
            error: null,
          });
          authAPI.setAuthToken(parsed.token);
          console.log('Auth restored from localStorage, token set to apiClient');
        } else {
          // Token已过期或无效，清除存储
          localStorage.removeItem(STORAGE_KEY);
          authAPI.setAuthToken(null);
          console.log('Invalid or expired token removed, cleared apiClient token');
        }
      } catch (error) {
        console.error('Failed to parse saved auth:', error);
        localStorage.removeItem(STORAGE_KEY);
        authAPI.setAuthToken(null);
      }
    } else {
      // 检查apiClient是否有无效的测试token
      const currentToken = authAPI.getAuthToken();
      if (currentToken && currentToken.includes('fake-token')) {
        authAPI.setAuthToken(null);
        console.log('Removed fake test token from apiClient');
      }
    }
  }, []);

  // 开发者认证
  const authenticate = useCallback(async (formData: AuthFormData) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await authAPI.authenticate({
        password: formData.password,
      });

      const authData = {
        isAuthenticated: true,
        token: response.data.token,
        expiresAt: response.data.expires_at,
        isLoading: false,
        error: null,
      };

      setAuthState(authData);
      authAPI.setAuthToken(response.data.token);

      // 如果选择记住登录，保存到本地存储
      if (formData.rememberMe) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify({
          token: response.data.token,
          expiresAt: response.data.expires_at,
        }));
      }

      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error instanceof APIError 
        ? error.message 
        : '认证失败，请重试';

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));

      return { success: false, error: errorMessage };
    }
  }, []);

  // 退出登录
  const logout = useCallback(() => {
    setAuthState({
      isAuthenticated: false,
      token: null,
      expiresAt: null,
      isLoading: false,
      error: null,
    });
    authAPI.setAuthToken(null);
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // 验证当前token
  const verifyToken = useCallback(async () => {
    if (!authState.token) return false;

    try {
      const response = await authAPI.verifyToken({
        token: authState.token,
      });
      return response.data.valid;
    } catch (error) {
      console.error('Token verification failed:', error);
      logout();
      return false;
    }
  }, [authState.token, logout]);

  return {
    authState,
    authenticate,
    logout,
    clearError,
    verifyToken,
  };
};
