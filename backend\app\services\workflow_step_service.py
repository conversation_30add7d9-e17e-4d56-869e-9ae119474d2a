"""
工作流步骤管理服务
处理工作流步骤的增删改查
"""
import time
from typing import Dict, Any, List
from datetime import datetime
from sqlalchemy.orm import Session


def format_datetime(dt: datetime) -> str:
    """格式化datetime为ISO 8601格式，符合API文档要求"""
    if dt is None:
        return ""
    # 格式化为 "2024-01-20T10:00:00.000Z"
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

from app.core.database import get_db_session
from app.models.workflow import WorkflowDBModel, WorkflowStepDBModel, StepType
from app.schemas.workflow import (
    WorkflowStepCreateRequest, WorkflowStepUpdateRequest, WorkflowStep,
    Position
)


class WorkflowStepService:
    """工作流步骤服务类"""
    
    def _generate_id(self, prefix: str) -> str:
        """生成唯一ID"""
        import random
        timestamp = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)
        return f"{prefix}_{timestamp}_{random_suffix}"
    
    def get_workflow_steps(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流步骤列表"""
        try:
            with get_db_session() as db:
                # 检查工作流是否存在
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()
                if not db_workflow:
                    return {
                        "success": False,
                        "error": "workflow_not_found",
                        "details": "工作流不存在"
                    }
                
                # 查询步骤
                db_steps = db.query(WorkflowStepDBModel).filter(
                    WorkflowStepDBModel.workflow_id == workflow_id
                ).order_by(WorkflowStepDBModel.sort_order).all()
                
                # 构建响应
                steps = []
                for db_step in db_steps:
                    step = WorkflowStep(
                        id=db_step.step_id,
                        name=db_step.name,
                        type=db_step.type.value,
                        position=Position(x=db_step.position_x or 0, y=db_step.position_y or 0),
                        config=db_step.config or {},
                        nextSteps=db_step.next_steps or [],
                        created_at=format_datetime(db_step.created_at),
                        updated_at=format_datetime(db_step.updated_at)
                    )
                    steps.append(step)
                
                return {
                    "success": True,
                    "data": {
                        "steps": steps,
                        "total": len(steps)
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询工作流步骤失败: {str(e)}"
            }
    
    def add_workflow_step(self, workflow_id: str, request: WorkflowStepCreateRequest) -> Dict[str, Any]:
        """添加工作流步骤"""
        try:
            with get_db_session() as db:
                # 检查工作流是否存在
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()
                if not db_workflow:
                    return {
                        "success": False,
                        "error": "workflow_not_found",
                        "details": "工作流不存在"
                    }
                
                # 检查步骤ID是否已存在
                existing_step = db.query(WorkflowStepDBModel).filter(
                    WorkflowStepDBModel.workflow_id == workflow_id,
                    WorkflowStepDBModel.step_id == request.step_id
                ).first()
                
                if existing_step:
                    return {
                        "success": False,
                        "error": "step_id_exists",
                        "details": "步骤ID已存在"
                    }
                
                # 获取最大排序号
                max_sort_order = db.query(WorkflowStepDBModel.sort_order).filter(
                    WorkflowStepDBModel.workflow_id == workflow_id
                ).order_by(WorkflowStepDBModel.sort_order.desc()).first()
                
                next_sort_order = (max_sort_order[0] + 1) if max_sort_order and max_sort_order[0] is not None else 0
                
                # 创建步骤记录
                step_id = self._generate_id("step")
                db_step = WorkflowStepDBModel(
                    id=step_id,
                    workflow_id=workflow_id,
                    step_id=request.step_id,
                    name=request.name,
                    type=StepType(request.type.value),
                    position_x=request.position.x,
                    position_y=request.position.y,
                    config=request.config.model_dump() if request.config else {},
                    next_steps=request.nextSteps or [],
                    sort_order=next_sort_order
                )
                
                db.add(db_step)
                
                # 更新工作流步骤配置
                steps_config = db_workflow.steps_config or []
                step_config = {
                    "id": request.step_id,
                    "name": request.name,
                    "type": request.type.value,
                    "position": {"x": request.position.x, "y": request.position.y},
                    "config": request.config.model_dump() if request.config else {},
                    "nextSteps": request.nextSteps or []
                }
                steps_config.append(step_config)
                
                db_workflow.steps_config = steps_config
                db_workflow.step_count = len(steps_config)
                
                db.commit()
                
                # 构建响应
                step = WorkflowStep(
                    id=db_step.step_id,
                    name=db_step.name,
                    type=db_step.type.value,
                    position=Position(x=db_step.position_x, y=db_step.position_y),
                    config=db_step.config or {},
                    nextSteps=db_step.next_steps or [],
                    created_at=format_datetime(db_step.created_at),
                    updated_at=format_datetime(db_step.updated_at)
                )
                
                return {
                    "success": True,
                    "data": {"step": step}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"添加工作流步骤失败: {str(e)}"
            }
    
    def update_workflow_step(self, workflow_id: str, step_id: str, 
                           request: WorkflowStepUpdateRequest) -> Dict[str, Any]:
        """更新工作流步骤"""
        try:
            with get_db_session() as db:
                # 查询步骤
                db_step = db.query(WorkflowStepDBModel).filter(
                    WorkflowStepDBModel.workflow_id == workflow_id,
                    WorkflowStepDBModel.step_id == step_id
                ).first()
                
                if not db_step:
                    return {
                        "success": False,
                        "error": "step_not_found",
                        "details": "工作流步骤不存在"
                    }
                
                # 更新步骤信息
                if request.name is not None:
                    db_step.name = request.name
                if request.type is not None:
                    db_step.type = StepType(request.type.value)
                if request.position is not None:
                    db_step.position_x = request.position.x
                    db_step.position_y = request.position.y
                if request.config is not None:
                    db_step.config = request.config.model_dump()
                if request.nextSteps is not None:
                    db_step.next_steps = request.nextSteps
                
                # 更新工作流步骤配置
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()
                if db_workflow and db_workflow.steps_config:
                    steps_config = db_workflow.steps_config
                    for i, step_config in enumerate(steps_config):
                        if step_config.get("id") == step_id:
                            if request.name is not None:
                                step_config["name"] = request.name
                            if request.type is not None:
                                step_config["type"] = request.type.value
                            if request.position is not None:
                                step_config["position"] = {"x": request.position.x, "y": request.position.y}
                            if request.config is not None:
                                step_config["config"] = request.config.model_dump()
                            if request.nextSteps is not None:
                                step_config["nextSteps"] = request.nextSteps
                            break
                    
                    db_workflow.steps_config = steps_config
                
                db.commit()
                
                # 构建响应
                step = WorkflowStep(
                    id=db_step.step_id,
                    name=db_step.name,
                    type=db_step.type.value,
                    position=Position(x=db_step.position_x or 0, y=db_step.position_y or 0),
                    config=db_step.config or {},
                    nextSteps=db_step.next_steps or [],
                    created_at=format_datetime(db_step.created_at),
                    updated_at=format_datetime(db_step.updated_at)
                )
                
                return {
                    "success": True,
                    "data": {"step": step}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新工作流步骤失败: {str(e)}"
            }
    
    def delete_workflow_step(self, workflow_id: str, step_id: str) -> Dict[str, Any]:
        """删除工作流步骤"""
        try:
            with get_db_session() as db:
                # 查询步骤
                db_step = db.query(WorkflowStepDBModel).filter(
                    WorkflowStepDBModel.workflow_id == workflow_id,
                    WorkflowStepDBModel.step_id == step_id
                ).first()
                
                if not db_step:
                    return {
                        "success": False,
                        "error": "step_not_found",
                        "details": "工作流步骤不存在"
                    }
                
                # 检查是否为开始或结束步骤
                if db_step.type in [StepType.START, StepType.END]:
                    return {
                        "success": False,
                        "error": "cannot_delete_critical_step",
                        "details": "不能删除开始或结束步骤"
                    }
                
                # 删除步骤
                step_name = db_step.name
                db.delete(db_step)
                
                # 更新工作流步骤配置
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()
                if db_workflow and db_workflow.steps_config:
                    steps_config = [
                        step_config for step_config in db_workflow.steps_config
                        if step_config.get("id") != step_id
                    ]
                    db_workflow.steps_config = steps_config
                    db_workflow.step_count = len(steps_config)
                
                db.commit()
                
                return {
                    "success": True,
                    "data": {
                        "step_id": step_id,
                        "name": step_name,
                        "deleted_at": time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除工作流步骤失败: {str(e)}"
            }


# 全局工作流步骤服务实例
workflow_step_service = WorkflowStepService()
