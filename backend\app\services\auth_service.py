"""
认证服务
处理开发者认证和令牌验证的业务逻辑
"""
from typing import Dict, Any
from app.config import settings
from app.core.auth.jwt_handler import jwt_handler
from app.schemas.auth import (
    DeveloperAuthData, 
    TokenVerifySuccessData, 
    TokenVerifyFailData
)


class AuthService:
    """认证服务类"""
    
    def authenticate_developer(self, password: str) -> Dict[str, Any]:
        """
        开发者认证
        
        Args:
            password: 开发者密码
            
        Returns:
            认证结果字典
        """
        # 验证密码
        if password != settings.DEVELOPER_PASSWORD:
            return {
                "success": False,
                "error": "invalid_password",
                "details": "提供的密码不正确"
            }
        
        # 生成JWT token
        token_data = jwt_handler.create_access_token(
            data={"sub": "developer", "type": "developer"}
        )
        
        return {
            "success": True,
            "data": DeveloperAuthData(**token_data)
        }
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """
        验证令牌
        
        Args:
            token: 要验证的JWT令牌
            
        Returns:
            验证结果字典
        """
        # 验证token
        result = jwt_handler.verify_token(token)
        
        if result["valid"]:
            # 验证成功
            return {
                "success": True,
                "data": TokenVerifySuccessData(
                    valid=result["valid"],
                    user_type=result["user_type"],
                    issued_at=result["issued_at"],
                    expires_at=result["expires_at"],
                    remaining_time=result["remaining_time"]
                )
            }
        else:
            # 验证失败
            return {
                "success": False,
                "data": TokenVerifyFailData(
                    valid=result["valid"],
                    error=result["error"],
                    details=result["details"]
                )
            }


# 全局认证服务实例
auth_service = AuthService()
