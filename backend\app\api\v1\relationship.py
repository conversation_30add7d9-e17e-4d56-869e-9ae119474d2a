"""
实体关系管理API路由
独立的关系管理端点，按照API文档规范实现
"""
from fastapi import APIRouter, HTTPException, Header, Path
from fastapi.responses import JSONResponse
from typing import Optional
from app.schemas.entity import EntityRelationshipCreateRequest
from app.services.entity_service import entity_service

router = APIRouter(prefix="/relationships", tags=["实体关系管理"])


@router.put(
    "/{relationship_id}",
    summary="更新实体关系",
    description="更新指定的实体关系定义"
)
async def update_relationship(
    relationship_id: str = Path(..., description="关系唯一标识符"),
    request: EntityRelationshipCreateRequest = ...,
    authorization: Optional[str] = Header(None)
):
    """
    更新实体关系接口
    
    - **relationship_id**: 关系唯一标识符
    - **request**: 关系更新请求数据
    
    返回更新后的关系详情
    """
    # 调用实体服务更新关系
    result = entity_service.update_entity_relationship_by_id(relationship_id, request)
    
    if result["success"]:
        # 更新成功
        return JSONResponse(
            status_code=200,
            content={
                "code": 200,
                "message": "更新关系成功",
                "data": {
                    "relationship": result["data"]["relationship"].model_dump()
                }
            }
        )
    else:
        # 更新失败
        if result["error"] == "relationship_not_found":
            status_code = 404
            message = "关系不存在"
        elif result["error"] == "target_entity_not_found":
            status_code = 404
            message = "目标实体不存在"
        else:
            status_code = 500
            message = "更新关系失败"
        
        raise HTTPException(
            status_code=status_code,
            detail={
                "code": status_code,
                "message": message,
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            }
        )


@router.delete(
    "/{relationship_id}",
    summary="删除实体关系",
    description="删除指定的实体关系定义"
)
async def delete_relationship_by_id(
    relationship_id: str = Path(..., description="关系唯一标识符"),
    authorization: Optional[str] = Header(None)
):
    """
    删除实体关系接口
    
    - **relationship_id**: 关系唯一标识符
    
    返回删除结果
    """
    # 调用实体服务删除关系
    result = entity_service.delete_entity_relationship_by_id(relationship_id)
    
    if result["success"]:
        # 删除成功
        return JSONResponse(
            status_code=200,
            content={
                "code": 200,
                "message": "删除关系成功",
                "data": result["data"]
            }
        )
    else:
        # 删除失败
        if result["error"] == "relationship_not_found":
            status_code = 404
            message = "关系不存在"
        else:
            status_code = 500
            message = "删除关系失败"
        
        raise HTTPException(
            status_code=status_code,
            detail={
                "code": status_code,
                "message": message,
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            }
        )
