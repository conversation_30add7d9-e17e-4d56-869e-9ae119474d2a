# API路由管理模块 API 文档

## 📋 概述

API路由管理模块提供动态API路由的注册、管理和状态控制功能，支持运行时动态添加和修改API端点。

## 🔗 API 端点列表

### API路由管理
1. 动态注册新的API路由
2. 获取当前已注册的所有API路由
3. 更新已注册的API路由
4. 删除已注册的API路由

### API路由状态管理
5. 激活API路由
6. 停用API路由
7. 获取API路由状态
8. 检查所有API路由健康状态

---

## API 详细文档

### 1. 动态注册新的API路由

**POST** `/api/routes/register`

#### 描述
动态注册新的API路由，支持CRUD操作和自定义业务逻辑。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "api_id": "string",
  "name": "string",
  "endpoint": "string",
  "method": "string",
  "description": "string",
  "auth_required": boolean,
  "handler": {
    "type": "string",
    "config": {}
  },
  "parameters": [
    {
      "name": "string",
      "type": "string",
      "location": "string",
      "required": boolean,
      "description": "string"
    }
  ],
  "responses": {
    "200": {
      "description": "string",
      "schema": {}
    }
  }
}
```

#### 处理器类型说明

| 类型 | 描述 | 配置示例 |
|------|------|----------|
| entity_crud | 实体CRUD操作 | {"entity": "product", "operation": "list"} |
| custom_function | 自定义函数 | {"function": "calculate_discount"} |
| proxy | 代理转发 | {"target_url": "http://api.example.com"} |
| workflow | 工作流触发 | {"workflow_id": "workflow_123"} |
| static_data | 静态数据返回 | {"data": {...}} |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/routes/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "api_id": "product_list_api",
    "name": "商品列表API",
    "endpoint": "/api/products",
    "method": "GET",
    "description": "获取商品列表，支持分页和筛选",
    "auth_required": true,
    "handler": {
      "type": "entity_crud",
      "config": {
        "entity": "product",
        "operation": "list",
        "default_limit": 20,
        "max_limit": 100,
        "allowed_filters": ["category", "status", "price_range"],
        "allowed_sorts": ["name", "price", "created_at"]
      }
    },
    "parameters": [
      {
        "name": "page",
        "type": "integer",
        "location": "query",
        "required": false,
        "description": "页码，默认为1"
      },
      {
        "name": "limit",
        "type": "integer",
        "location": "query",
        "required": false,
        "description": "每页数量，默认为20"
      },
      {
        "name": "category",
        "type": "string",
        "location": "query",
        "required": false,
        "description": "商品分类筛选"
      }
    ],
    "responses": {
      "200": {
        "description": "成功返回商品列表",
        "schema": {
          "type": "object",
          "properties": {
            "code": {"type": "integer"},
            "message": {"type": "string"},
            "data": {
              "type": "object",
              "properties": {
                "products": {"type": "array"},
                "pagination": {"type": "object"}
              }
            }
          }
        }
      }
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "API路由注册成功",
  "data": {
    "route": {
      "id": "route_1705123456789",
      "api_id": "product_list_api",
      "name": "商品列表API",
      "endpoint": "/api/products",
      "method": "GET",
      "description": "获取商品列表，支持分页和筛选",
      "auth_required": true,
      "status": "active",
      "handler": {
        "type": "entity_crud",
        "config": {
          "entity": "product",
          "operation": "list"
        }
      },
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

**冲突响应 (409 Conflict):**
```json
{
  "code": 409,
  "message": "API路由已存在",
  "data": {
    "error": "route_conflict",
    "details": "端点 '/api/products' 的 GET 方法已被注册",
    "existing_route": {
      "id": "route_existing",
      "api_id": "existing_product_api"
    }
  }
}
```

---

### 2. 获取当前已注册的所有API路由

**GET** `/api/routes`

#### 描述
获取系统中所有已注册的API路由列表，支持筛选和分页。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| status | string | 否 | 筛选路由状态 (active/inactive) |
| method | string | 否 | 筛选HTTP方法 |
| entity | string | 否 | 筛选关联实体 |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/routes?status=active&method=GET&page=1&limit=10" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取API路由列表成功",
  "data": {
    "routes": [
      {
        "id": "route_1705123456789",
        "api_id": "product_list_api",
        "name": "商品列表API",
        "endpoint": "/api/products",
        "method": "GET",
        "description": "获取商品列表，支持分页和筛选",
        "auth_required": true,
        "status": "active",
        "handler_type": "entity_crud",
        "call_count": 1247,
        "last_called": "2024-01-20T11:30:00.000Z",
        "avg_response_time": 156,
        "created_at": "2024-01-20T10:00:00.000Z",
        "updated_at": "2024-01-20T10:00:00.000Z"
      },
      {
        "id": "route_1705123456790",
        "api_id": "product_create_api",
        "name": "创建商品API",
        "endpoint": "/api/products",
        "method": "POST",
        "description": "创建新的商品记录",
        "auth_required": true,
        "status": "active",
        "handler_type": "entity_crud",
        "call_count": 89,
        "last_called": "2024-01-20T11:25:00.000Z",
        "avg_response_time": 234,
        "created_at": "2024-01-20T10:15:00.000Z",
        "updated_at": "2024-01-20T10:15:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_routes": 25,
      "active_routes": 23,
      "inactive_routes": 2,
      "total_calls": 15678,
      "avg_response_time": 189
    }
  }
}
```

---

### 3. 更新已注册的API路由

**PUT** `/api/routes/{route_id}`

#### 描述
更新已注册的API路由配置，包括处理器、参数和响应定义。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| route_id | string | 是 | 路由唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "description": "string",
  "auth_required": boolean,
  "handler": {
    "type": "string",
    "config": {}
  },
  "parameters": [],
  "responses": {}
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/routes/route_1705123456789" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "商品列表API（增强版）",
    "description": "获取商品列表，支持高级筛选和排序",
    "auth_required": true,
    "handler": {
      "type": "entity_crud",
      "config": {
        "entity": "product",
        "operation": "list",
        "default_limit": 20,
        "max_limit": 100,
        "allowed_filters": ["category", "status", "price_range", "brand"],
        "allowed_sorts": ["name", "price", "created_at", "popularity"],
        "include_relations": ["category_info", "brand_info"]
      }
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "API路由更新成功",
  "data": {
    "route": {
      "id": "route_1705123456789",
      "api_id": "product_list_api",
      "name": "商品列表API（增强版）",
      "endpoint": "/api/products",
      "method": "GET",
      "description": "获取商品列表，支持高级筛选和排序",
      "auth_required": true,
      "status": "active",
      "handler": {
        "type": "entity_crud",
        "config": {
          "entity": "product",
          "operation": "list",
          "include_relations": ["category_info", "brand_info"]
        }
      },
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 4. 删除已注册的API路由

**DELETE** `/api/routes/{route_id}`

#### 描述
删除指定的API路由注册。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| route_id | string | 是 | 路由唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| force | boolean | 否 | 是否强制删除，默认false |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/routes/route_1705123456789?force=false" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "API路由删除成功",
  "data": {
    "route_id": "route_1705123456789",
    "api_id": "product_list_api",
    "endpoint": "/api/products",
    "method": "GET",
    "deleted_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

### 5. 激活API路由

**POST** `/api/routes/{route_id}/activate`

#### 描述
激活指定的API路由，使其可以接收和处理请求。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| route_id | string | 是 | 路由唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/routes/route_1705123456789/activate" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "API路由激活成功",
  "data": {
    "route": {
      "id": "route_1705123456789",
      "api_id": "product_list_api",
      "name": "商品列表API",
      "endpoint": "/api/products",
      "method": "GET",
      "status": "active",
      "activated_at": "2024-01-20T12:30:00.000Z"
    }
  }
}
```

---

### 6. 停用API路由

**POST** `/api/routes/{route_id}/deactivate`

#### 描述
停用指定的API路由，使其不再接收请求。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| route_id | string | 是 | 路由唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/routes/route_1705123456789/deactivate" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "API路由停用成功",
  "data": {
    "route": {
      "id": "route_1705123456789",
      "api_id": "product_list_api",
      "name": "商品列表API",
      "endpoint": "/api/products",
      "method": "GET",
      "status": "inactive",
      "deactivated_at": "2024-01-20T12:30:00.000Z"
    }
  }
}
```

---

### 5. 获取API路由状态

**GET** `/api/routes/{route_id}/status`

#### 描述
获取指定API路由的详细状态信息，包括调用统计和性能指标。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| route_id | string | 是 | 路由唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/routes/route_1705123456789/status" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取API路由状态成功",
  "data": {
    "route": {
      "id": "route_1705123456789",
      "api_id": "product_list_api",
      "name": "商品列表API",
      "endpoint": "/api/products",
      "method": "GET",
      "status": "active"
    },
    "statistics": {
      "total_calls": 1247,
      "successful_calls": 1198,
      "failed_calls": 49,
      "success_rate": 96.07,
      "avg_response_time": 156,
      "min_response_time": 45,
      "max_response_time": 2340,
      "last_called": "2024-01-20T11:30:00.000Z",
      "calls_today": 89,
      "calls_this_hour": 12
    },
    "performance": {
      "health_status": "healthy",
      "error_rate": 3.93,
      "recent_errors": [
        {
          "timestamp": "2024-01-20T11:25:00.000Z",
          "error": "timeout",
          "message": "Request timeout after 30 seconds"
        }
      ],
      "response_time_trend": "stable",
      "load_level": "normal"
    }
  }
}
```

---

### 6. 检查所有API路由健康状态

**GET** `/api/routes/health`

#### 描述
检查所有已注册API路由的健康状态，提供系统整体运行情况概览。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/routes/health" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "API路由健康检查完成",
  "data": {
    "overall_status": "healthy",
    "total_routes": 25,
    "healthy_routes": 23,
    "warning_routes": 1,
    "unhealthy_routes": 1,
    "inactive_routes": 2,
    "system_metrics": {
      "total_requests": 156789,
      "avg_response_time": 189,
      "success_rate": 97.8,
      "error_rate": 2.2
    },
    "route_status": [
      {
        "id": "route_1705123456789",
        "api_id": "product_list_api",
        "endpoint": "/api/products",
        "method": "GET",
        "status": "active",
        "health": "healthy",
        "success_rate": 96.07,
        "avg_response_time": 156
      },
      {
        "id": "route_1705123456791",
        "api_id": "order_process_api",
        "endpoint": "/api/orders/process",
        "method": "POST",
        "status": "active",
        "health": "warning",
        "success_rate": 89.5,
        "avg_response_time": 2340,
        "issues": ["high_response_time", "increased_error_rate"]
      },
      {
        "id": "route_1705123456792",
        "api_id": "payment_api",
        "endpoint": "/api/payments",
        "method": "POST",
        "status": "active",
        "health": "unhealthy",
        "success_rate": 45.2,
        "avg_response_time": 5000,
        "issues": ["high_error_rate", "timeout_issues", "dependency_failure"]
      }
    ],
    "recommendations": [
      "检查支付API的外部依赖服务",
      "优化订单处理API的响应时间",
      "考虑为高负载API增加缓存机制"
    ]
  }
}
```

---

## 📝 路由状态说明

| 状态 | 描述 |
|------|------|
| active | 活跃状态，正常接收请求 |
| inactive | 非活跃状态，不接收请求 |
| maintenance | 维护状态，临时停用 |
| deprecated | 已弃用，计划移除 |

---

## 📝 健康状态说明

| 健康状态 | 描述 | 判断标准 |
|----------|------|----------|
| healthy | 健康 | 成功率>95%，响应时间<500ms |
| warning | 警告 | 成功率85-95%，响应时间500-2000ms |
| unhealthy | 不健康 | 成功率<85%，响应时间>2000ms |
| unknown | 未知 | 缺少足够的统计数据 |

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| route_not_found | 404 | 路由不存在 | 检查路由ID是否正确 |
| route_conflict | 409 | 路由冲突 | 使用不同的端点或方法 |
| invalid_handler | 400 | 无效的处理器配置 | 检查处理器类型和配置 |
| endpoint_invalid | 400 | 端点格式无效 | 检查端点路径格式 |
| method_not_supported | 400 | HTTP方法不支持 | 使用支持的HTTP方法 |
| route_in_use | 409 | 路由正在使用中 | 先停用路由再进行操作 |
