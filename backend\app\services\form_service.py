"""
表单配置服务层
实现表单的核心业务逻辑
"""
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_


def format_datetime(dt: datetime) -> str:
    """格式化datetime为ISO 8601格式，符合API文档要求"""
    if dt is None:
        return ""
    # 格式化为 "2024-01-20T10:00:00.000Z"
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

from app.core.database import get_db_session
from app.models.form import (
    FormDBModel, FormSectionDBModel, FormFieldDBModel, FormDataDBModel,
    FormStatus, LayoutType, DisplayType
)
from app.schemas.form import (
    FormCreateRequest, FormUpdateRequest, Form, FormListItem,
    FormLayout, FormSectionDetail, FormField, FormPermission,
    FormRenderRequest, FormSubmitRequest, FormDataResponse,
    PaginationInfo
)


class FormService:
    """表单服务类"""
    
    def _generate_id(self, prefix: str) -> str:
        """生成唯一ID"""
        import random
        timestamp = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)
        return f"{prefix}_{timestamp}_{random_suffix}"
    
    # 表单定义管理
    def create_form(self, request: FormCreateRequest) -> Dict[str, Any]:
        """创建表单配置"""
        try:
            with get_db_session() as db:
                # 生成表单ID
                form_id = self._generate_id("form")
                
                # 创建表单记录
                db_form = FormDBModel(
                    id=form_id,
                    name=request.name,
                    entity=request.entity,
                    description=request.description,
                    layout_type=LayoutType(request.layout.type),
                    layout_columns=request.layout.columns,
                    layout_spacing=request.layout.spacing,
                    layout_config={
                        "type": request.layout.type,
                        "columns": request.layout.columns,
                        "spacing": request.layout.spacing
                    },
                    permissions_config=[perm.dict() for perm in (request.permissions or [])],
                    status=FormStatus.ACTIVE
                )
                
                db.add(db_form)
                
                # 创建分组和字段
                total_fields = 0
                for section_index, section_data in enumerate(request.sections):
                    # 创建分组
                    section_id = self._generate_id("section")
                    db_section = FormSectionDBModel(
                        id=section_id,
                        form_id=form_id,
                        section_id=section_data.id,
                        title=section_data.title,
                        collapsible=section_data.collapsible,
                        order_index=section_index,
                        field_count=len(section_data.fields)
                    )
                    db.add(db_section)
                    
                    # 创建字段
                    for field_index, field_data in enumerate(section_data.fields):
                        field_id = self._generate_id("field")
                        db_field = FormFieldDBModel(
                            id=field_id,
                            form_id=form_id,
                            section_id=section_id,
                            field_id=field_data.id,
                            entity_field=field_data.entityField,
                            display_type=DisplayType(field_data.displayType),
                            label=field_data.label,
                            placeholder=field_data.placeholder,
                            required=field_data.required,
                            readonly=field_data.readonly,
                            hidden=field_data.hidden,
                            validation_rules=field_data.validation.rules if field_data.validation else [],
                            validation_messages=field_data.validation.messages if field_data.validation else {},
                            options_config=[opt.dict() for opt in (field_data.options or [])],
                            order_index=field_index,
                            grid_span=field_data.gridSpan
                        )
                        db.add(db_field)
                        total_fields += 1
                
                # 更新表单统计
                db_form.field_count = total_fields
                db_form.section_count = len(request.sections)
                
                db.commit()
                
                # 构建响应
                form = self._build_form_response(db_form, db, include_sections=True)
                
                return {
                    "success": True,
                    "data": {"form": form}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"创建表单配置失败: {str(e)}"
            }
    
    def get_forms_list(self, entity: Optional[str] = None, page: int = 1, 
                      limit: int = 20) -> Dict[str, Any]:
        """获取表单配置列表"""
        try:
            with get_db_session() as db:
                # 构建查询
                query = db.query(FormDBModel)
                
                # 实体筛选
                if entity:
                    query = query.filter(FormDBModel.entity == entity)
                
                # 分页查询
                total = query.count()
                offset = (page - 1) * limit
                db_forms = query.order_by(FormDBModel.created_at.desc()).offset(offset).limit(limit).all()
                
                # 构建响应
                forms = []
                for db_form in db_forms:
                    form = FormListItem(
                        id=db_form.id,
                        name=db_form.name,
                        entity=db_form.entity,
                        description=db_form.description,
                        status=db_form.status.value,
                        field_count=db_form.field_count,
                        section_count=db_form.section_count,
                        created_at=format_datetime(db_form.created_at),
                        updated_at=format_datetime(db_form.updated_at)
                    )
                    forms.append(form)
                
                return {
                    "success": True,
                    "data": {
                        "forms": [form.dict() for form in forms],
                        "total": total
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"获取表单配置列表失败: {str(e)}"
            }
    
    def get_form_detail(self, form_id: str) -> Dict[str, Any]:
        """获取表单配置详情"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()
                
                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }
                
                # 构建响应
                form = self._build_form_response(db_form, db, include_sections=True)
                
                return {
                    "success": True,
                    "data": {"form": form}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"获取表单配置详情失败: {str(e)}"
            }
    
    def update_form(self, form_id: str, request: FormUpdateRequest) -> Dict[str, Any]:
        """更新表单配置"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()
                
                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }
                
                # 更新字段
                if request.name is not None:
                    db_form.name = request.name
                if request.description is not None:
                    db_form.description = request.description
                if request.layout is not None:
                    db_form.layout_type = LayoutType(request.layout.type)
                    db_form.layout_columns = request.layout.columns
                    db_form.layout_spacing = request.layout.spacing
                    db_form.layout_config = {
                        "type": request.layout.type,
                        "columns": request.layout.columns,
                        "spacing": request.layout.spacing
                    }
                if request.status is not None:
                    db_form.status = FormStatus(request.status)
                if request.permissions is not None:
                    db_form.permissions_config = [perm.dict() for perm in request.permissions]
                
                db_form.updated_at = datetime.now()
                db.commit()
                
                # 构建响应
                form = self._build_form_response(db_form, db, include_sections=True)
                
                return {
                    "success": True,
                    "data": {"form": form}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新表单配置失败: {str(e)}"
            }
    
    def delete_form(self, form_id: str, force: bool = False) -> Dict[str, Any]:
        """删除表单配置"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()
                
                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }
                
                # 检查是否有关联数据
                data_count = db.query(FormDataDBModel).filter(FormDataDBModel.form_id == form_id).count()
                if data_count > 0 and not force:
                    return {
                        "success": False,
                        "error": "has_related_data",
                        "details": f"表单配置有 {data_count} 条关联数据，无法删除"
                    }
                
                form_name = db_form.name
                db.delete(db_form)
                db.commit()
                
                return {
                    "success": True,
                    "data": {"name": form_name}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除表单配置失败: {str(e)}"
            }
    
    def _build_form_response(self, db_form: FormDBModel, db: Session, 
                           include_sections: bool = False) -> Dict[str, Any]:
        """构建表单响应数据"""
        # 基本信息
        form_data = {
            "id": db_form.id,
            "name": db_form.name,
            "entity": db_form.entity,
            "description": db_form.description,
            "layout": {
                "type": db_form.layout_type.value,
                "columns": db_form.layout_columns,
                "spacing": db_form.layout_spacing
            },
            "status": db_form.status.value,
            "field_count": db_form.field_count,
            "section_count": db_form.section_count,
            "permissions": db_form.permissions_config or [],
            "created_at": format_datetime(db_form.created_at),
            "updated_at": format_datetime(db_form.updated_at)
        }
        
        # 包含分组详情
        if include_sections:
            sections = []
            db_sections = db.query(FormSectionDBModel).filter(
                FormSectionDBModel.form_id == db_form.id
            ).order_by(FormSectionDBModel.order_index).all()
            
            for db_section in db_sections:
                # 获取分组字段
                fields = []
                db_fields = db.query(FormFieldDBModel).filter(
                    FormFieldDBModel.section_id == db_section.id
                ).order_by(FormFieldDBModel.order_index).all()
                
                for db_field in db_fields:
                    field = {
                        "id": db_field.field_id,
                        "entityField": db_field.entity_field,
                        "displayType": db_field.display_type.value,
                        "label": db_field.label,
                        "placeholder": db_field.placeholder,
                        "required": db_field.required,
                        "readonly": db_field.readonly,
                        "hidden": db_field.hidden,
                        "validation": {
                            "rules": db_field.validation_rules or [],
                            "messages": db_field.validation_messages or {}
                        } if db_field.validation_rules or db_field.validation_messages else None,
                        "options": db_field.options_config or None,
                        "gridSpan": db_field.grid_span,
                        "orderIndex": db_field.order_index,
                        "created_at": format_datetime(db_field.created_at),
                        "updated_at": format_datetime(db_field.updated_at)
                    }
                    fields.append(field)
                
                section = {
                    "id": db_section.section_id,
                    "title": db_section.title,
                    "collapsible": db_section.collapsible,
                    "field_count": len(fields),
                    "fields": fields,
                    "orderIndex": db_section.order_index,
                    "created_at": format_datetime(db_section.created_at),
                    "updated_at": format_datetime(db_section.updated_at)
                }
                sections.append(section)
            
            form_data["sections"] = sections
        
        return form_data


    # 表单渲染管理
    def get_form_render_schema(self, form_id: str, request: FormRenderRequest) -> Dict[str, Any]:
        """获取表单渲染配置（amis schema）"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()

                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }

                # 检查权限
                user_permissions = self._check_form_permissions(db_form, request.user_role, request.mode)
                if not user_permissions["allowed"]:
                    return {
                        "success": False,
                        "error": "permission_denied",
                        "details": user_permissions["message"]
                    }

                # 生成amis schema
                schema = self._generate_amis_schema(db_form, db, request)

                return {
                    "success": True,
                    "data": {"schema": schema}
                }

        except Exception as e:
            return {
                "success": False,
                "error": "render_failed",
                "details": f"获取表单渲染配置失败: {str(e)}"
            }

    def submit_form_data(self, form_id: str, request: FormSubmitRequest,
                        submitted_by: str = "system") -> Dict[str, Any]:
        """提交表单数据"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()

                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }

                # 验证表单数据
                validation_result = self._validate_form_data(db_form, db, request.form_data)
                if not validation_result["valid"]:
                    return {
                        "success": False,
                        "error": "validation_failed",
                        "details": validation_result["errors"]
                    }

                # 生成数据ID
                data_id = self._generate_id("data")

                # 创建表单数据记录
                db_data = FormDataDBModel(
                    id=data_id,
                    form_id=form_id,
                    entity_id=request.entity_id,
                    form_data=request.form_data,
                    submitted_by=submitted_by,
                    status="submitted"
                )

                db.add(db_data)
                db.commit()

                # 构建响应
                response_data = {
                    "data_id": data_id,
                    "form_data": request.form_data,
                    "entity_id": request.entity_id,
                    "submitted_at": format_datetime(db_data.submitted_at)
                }

                return {
                    "success": True,
                    "data": response_data
                }

        except Exception as e:
            return {
                "success": False,
                "error": "submission_failed",
                "details": f"提交表单数据失败: {str(e)}"
            }

    def get_form_data_for_edit(self, form_id: str, entity_id: str) -> Dict[str, Any]:
        """获取表单数据用于编辑"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()

                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }

                # 查询表单数据
                db_data = db.query(FormDataDBModel).filter(
                    and_(
                        FormDataDBModel.form_id == form_id,
                        FormDataDBModel.entity_id == entity_id
                    )
                ).order_by(FormDataDBModel.submitted_at.desc()).first()

                if not db_data:
                    return {
                        "success": False,
                        "error": "data_not_found",
                        "details": "表单数据不存在"
                    }

                # 构建响应
                response_data = {
                    "form_data": db_data.form_data,
                    "entity_id": db_data.entity_id,
                    "submitted_at": format_datetime(db_data.submitted_at)
                }

                return {
                    "success": True,
                    "data": response_data
                }

        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"获取表单数据失败: {str(e)}"
            }

    def validate_form_config(self, form_id: str) -> Dict[str, Any]:
        """验证表单配置"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()

                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }

                # 验证表单配置
                validation_errors = []

                # 检查是否有字段
                if db_form.field_count == 0:
                    validation_errors.append("表单没有配置任何字段")

                # 检查字段配置
                db_fields = db.query(FormFieldDBModel).filter(FormFieldDBModel.form_id == form_id).all()
                for db_field in db_fields:
                    # 检查必填字段标签
                    if not db_field.label:
                        validation_errors.append(f"字段 {db_field.field_id} 缺少标签")

                    # 检查选择类型字段的选项配置
                    if db_field.display_type in [DisplayType.SELECT, DisplayType.RADIO, DisplayType.CHECKBOX]:
                        if not db_field.options_config:
                            validation_errors.append(f"字段 {db_field.field_id} 缺少选项配置")

                # 构建响应
                is_valid = len(validation_errors) == 0

                return {
                    "success": True,
                    "data": {
                        "valid": is_valid,
                        "errors": validation_errors,
                        "field_count": db_form.field_count,
                        "section_count": db_form.section_count
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "validation_failed",
                "details": f"验证表单配置失败: {str(e)}"
            }


    def _check_form_permissions(self, db_form: FormDBModel, user_role: str, mode: str) -> Dict[str, Any]:
        """检查表单权限"""
        permissions_config = db_form.permissions_config or []

        # 如果没有权限配置，默认允许
        if not permissions_config:
            return {"allowed": True, "message": ""}

        # 查找用户角色权限
        user_permissions = None
        for perm in permissions_config:
            if perm.get("role") == user_role:
                user_permissions = perm.get("actions", [])
                break

        # 如果没有找到角色权限，拒绝访问
        if user_permissions is None:
            return {"allowed": False, "message": f"角色 {user_role} 没有访问权限"}

        # 检查操作权限
        required_action = {
            "create": "create",
            "edit": "update",
            "view": "read"
        }.get(mode, "read")

        if required_action not in user_permissions:
            return {"allowed": False, "message": f"角色 {user_role} 没有 {required_action} 权限"}

        return {"allowed": True, "message": ""}

    def _generate_amis_schema(self, db_form: FormDBModel, db: Session, request: FormRenderRequest) -> Dict[str, Any]:
        """生成amis schema"""
        # 基础schema结构
        schema = {
            "type": "form",
            "title": db_form.name,
            "mode": "horizontal",
            "api": {
                "method": "post",
                "url": f"/api/forms/{db_form.id}/submit"
            },
            "body": []
        }

        # 获取分组和字段
        db_sections = db.query(FormSectionDBModel).filter(
            FormSectionDBModel.form_id == db_form.id
        ).order_by(FormSectionDBModel.order_index).all()

        for db_section in db_sections:
            # 创建分组
            section_schema = {
                "type": "fieldset",
                "title": db_section.title,
                "collapsible": db_section.collapsible,
                "body": []
            }

            # 获取分组字段
            db_fields = db.query(FormFieldDBModel).filter(
                FormFieldDBModel.section_id == db_section.id
            ).order_by(FormFieldDBModel.order_index).all()

            for db_field in db_fields:
                field_schema = self._generate_field_schema(db_field, request.mode)
                section_schema["body"].append(field_schema)

            schema["body"].append(section_schema)

        return schema

    def _generate_field_schema(self, db_field: FormFieldDBModel, mode: str) -> Dict[str, Any]:
        """生成字段schema"""
        # 基础字段配置
        field_schema = {
            "type": self._get_amis_field_type(db_field.display_type),
            "name": db_field.entity_field,
            "label": db_field.label,
            "required": db_field.required,
            "disabled": db_field.readonly or mode == "view",
            "hidden": db_field.hidden
        }

        # 添加占位符
        if db_field.placeholder:
            field_schema["placeholder"] = db_field.placeholder

        # 添加验证规则
        if db_field.validation_rules:
            field_schema["validations"] = self._convert_validation_rules(db_field.validation_rules)

        # 添加选项配置
        if db_field.options_config and db_field.display_type in [DisplayType.SELECT, DisplayType.RADIO, DisplayType.CHECKBOX]:
            field_schema["options"] = db_field.options_config

        return field_schema

    def _get_amis_field_type(self, display_type: DisplayType) -> str:
        """获取amis字段类型"""
        type_mapping = {
            DisplayType.INPUT: "input-text",
            DisplayType.TEXTAREA: "textarea",
            DisplayType.SELECT: "select",
            DisplayType.RADIO: "radios",
            DisplayType.CHECKBOX: "checkboxes",
            DisplayType.SWITCH: "switch",
            DisplayType.DATE_PICKER: "input-date",
            DisplayType.UPLOAD: "input-file",
            DisplayType.RICH_TEXT: "input-rich-text"
        }
        return type_mapping.get(display_type, "input-text")

    def _convert_validation_rules(self, rules: List[str]) -> Dict[str, Any]:
        """转换验证规则为amis格式"""
        validations = {}

        for rule in rules:
            if rule == "required":
                validations["required"] = True
            elif rule.startswith("max:"):
                validations["maxLength"] = int(rule.split(":")[1])
            elif rule.startswith("min:"):
                validations["minLength"] = int(rule.split(":")[1])
            elif rule == "numeric":
                validations["isNumeric"] = True
            elif rule == "email":
                validations["isEmail"] = True

        return validations

    def _validate_form_data(self, db_form: FormDBModel, db: Session, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证表单数据"""
        errors = []

        # 获取所有字段
        db_fields = db.query(FormFieldDBModel).filter(FormFieldDBModel.form_id == db_form.id).all()

        for db_field in db_fields:
            field_value = form_data.get(db_field.entity_field)

            # 检查必填字段
            if db_field.required and (field_value is None or field_value == ""):
                errors.append(f"字段 {db_field.label} 不能为空")
                continue

            # 如果字段为空且非必填，跳过其他验证
            if field_value is None or field_value == "":
                continue

            # 验证规则检查
            if db_field.validation_rules:
                field_errors = self._validate_field_value(db_field, field_value)
                errors.extend(field_errors)

        return {
            "valid": len(errors) == 0,
            "errors": errors
        }

    def _validate_field_value(self, db_field: FormFieldDBModel, value: Any) -> List[str]:
        """验证字段值"""
        errors = []

        for rule in db_field.validation_rules:
            if rule.startswith("max:"):
                max_length = int(rule.split(":")[1])
                if isinstance(value, str) and len(value) > max_length:
                    message = db_field.validation_messages.get("max", f"{db_field.label} 不能超过 {max_length} 个字符")
                    errors.append(message)

            elif rule.startswith("min:"):
                min_length = int(rule.split(":")[1])
                if isinstance(value, str) and len(value) < min_length:
                    message = db_field.validation_messages.get("min", f"{db_field.label} 不能少于 {min_length} 个字符")
                    errors.append(message)

            elif rule == "numeric":
                try:
                    float(value)
                except (ValueError, TypeError):
                    message = db_field.validation_messages.get("numeric", f"{db_field.label} 必须是数字")
                    errors.append(message)

            elif rule == "email":
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, str(value)):
                    message = db_field.validation_messages.get("email", f"{db_field.label} 格式不正确")
                    errors.append(message)

        return errors


# 全局表单服务实例
form_service = FormService()
