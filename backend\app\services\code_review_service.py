"""
代码审查服务
提供AI生成代码的安全检查、质量评估、审批流程等功能
"""
import os
import json
import logging
import shutil
from typing import Dict, Any, Optional, List
from datetime import datetime
import re

from app.config import settings
from app.core.ai.bailian_client import bailian_client, BailianAPIError

logger = logging.getLogger(__name__)


class CodeReviewService:
    """代码审查服务类"""
    
    def __init__(self):
        self.client = bailian_client
        self.generated_dir = settings.GENERATED_CODE_DIR
        self.review_dir = settings.CODE_REVIEW_DIR
        self.approved_dir = settings.APPROVED_CODE_DIR
        
        # 确保目录存在
        os.makedirs(self.review_dir, exist_ok=True)
        os.makedirs(self.approved_dir, exist_ok=True)
    
    async def review_generated_code(self, generation_id: str) -> Dict[str, Any]:
        """
        审查生成的代码
        
        Args:
            generation_id: 代码生成ID
            
        Returns:
            审查结果
        """
        try:
            # 检查生成的代码是否存在
            generation_dir = os.path.join(self.generated_dir, generation_id)
            if not os.path.exists(generation_dir):
                return {
                    "success": False,
                    "error": "生成的代码不存在"
                }
            
            # 创建审查目录
            review_id = f"review_{generation_id}_{int(datetime.now().timestamp())}"
            review_path = os.path.join(self.review_dir, review_id)
            os.makedirs(review_path, exist_ok=True)
            
            # 复制代码到审查目录
            shutil.copytree(generation_dir, os.path.join(review_path, "code"), dirs_exist_ok=True)
            
            # 执行各项检查
            review_results = {
                "review_id": review_id,
                "generation_id": generation_id,
                "started_at": datetime.now().isoformat(),
                "checks": {}
            }
            
            # 1. 安全检查
            security_result = await self._security_check(generation_dir)
            review_results["checks"]["security"] = security_result
            
            # 2. 代码质量检查
            quality_result = await self._quality_check(generation_dir)
            review_results["checks"]["quality"] = quality_result
            
            # 3. 合规性检查
            compliance_result = await self._compliance_check(generation_dir)
            review_results["checks"]["compliance"] = compliance_result
            
            # 4. 性能检查
            performance_result = await self._performance_check(generation_dir)
            review_results["checks"]["performance"] = performance_result
            
            # 计算总体评分
            overall_score = self._calculate_overall_score(review_results["checks"])
            review_results["overall_score"] = overall_score
            review_results["recommendation"] = self._get_recommendation(overall_score)
            
            review_results["completed_at"] = datetime.now().isoformat()
            review_results["status"] = "completed"
            
            # 保存审查结果
            result_file = os.path.join(review_path, "review_result.json")
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(review_results, f, ensure_ascii=False, indent=2)
            
            return {
                "success": True,
                "data": review_results
            }
            
        except Exception as e:
            logger.error(f"代码审查失败: {str(e)}")
            return {
                "success": False,
                "error": f"代码审查失败: {str(e)}"
            }
    
    async def approve_code(self, review_id: str, approver: str, comments: Optional[str] = None) -> Dict[str, Any]:
        """
        批准代码
        
        Args:
            review_id: 审查ID
            approver: 批准人
            comments: 批准意见
            
        Returns:
            批准结果
        """
        try:
            # 检查审查结果是否存在
            review_path = os.path.join(self.review_dir, review_id)
            if not os.path.exists(review_path):
                return {
                    "success": False,
                    "error": "审查结果不存在"
                }
            
            # 读取审查结果
            result_file = os.path.join(review_path, "review_result.json")
            with open(result_file, 'r', encoding='utf-8') as f:
                review_result = json.load(f)
            
            # 检查是否可以批准
            if review_result.get("overall_score", 0) < 60:
                return {
                    "success": False,
                    "error": "代码质量评分过低，不建议批准"
                }
            
            # 移动代码到批准目录
            generation_id = review_result["generation_id"]
            approved_path = os.path.join(self.approved_dir, generation_id)
            code_path = os.path.join(review_path, "code")
            
            if os.path.exists(code_path):
                shutil.copytree(code_path, approved_path, dirs_exist_ok=True)
            
            # 更新审查结果
            review_result["approval"] = {
                "approved": True,
                "approver": approver,
                "approved_at": datetime.now().isoformat(),
                "comments": comments
            }
            
            # 保存更新的审查结果
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(review_result, f, ensure_ascii=False, indent=2)
            
            return {
                "success": True,
                "data": {
                    "review_id": review_id,
                    "generation_id": generation_id,
                    "approved_path": approved_path,
                    "approval": review_result["approval"]
                }
            }
            
        except Exception as e:
            logger.error(f"代码批准失败: {str(e)}")
            return {
                "success": False,
                "error": f"代码批准失败: {str(e)}"
            }
    
    async def reject_code(self, review_id: str, reviewer: str, reason: str) -> Dict[str, Any]:
        """
        拒绝代码
        
        Args:
            review_id: 审查ID
            reviewer: 审查人
            reason: 拒绝原因
            
        Returns:
            拒绝结果
        """
        try:
            # 检查审查结果是否存在
            review_path = os.path.join(self.review_dir, review_id)
            if not os.path.exists(review_path):
                return {
                    "success": False,
                    "error": "审查结果不存在"
                }
            
            # 读取审查结果
            result_file = os.path.join(review_path, "review_result.json")
            with open(result_file, 'r', encoding='utf-8') as f:
                review_result = json.load(f)
            
            # 更新审查结果
            review_result["approval"] = {
                "approved": False,
                "reviewer": reviewer,
                "rejected_at": datetime.now().isoformat(),
                "reason": reason
            }
            
            # 保存更新的审查结果
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(review_result, f, ensure_ascii=False, indent=2)
            
            return {
                "success": True,
                "data": {
                    "review_id": review_id,
                    "generation_id": review_result["generation_id"],
                    "approval": review_result["approval"]
                }
            }
            
        except Exception as e:
            logger.error(f"代码拒绝失败: {str(e)}")
            return {
                "success": False,
                "error": f"代码拒绝失败: {str(e)}"
            }
    
    async def get_review_status(self, review_id: Optional[str] = None) -> Dict[str, Any]:
        """获取审查状态"""
        try:
            if review_id:
                # 获取特定审查状态
                review_path = os.path.join(self.review_dir, review_id)
                result_file = os.path.join(review_path, "review_result.json")
                
                if os.path.exists(result_file):
                    with open(result_file, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                    
                    return {
                        "success": True,
                        "current_review": result
                    }
                else:
                    return {
                        "success": False,
                        "error": "审查结果不存在"
                    }
            else:
                # 获取所有审查状态
                reviews = []
                if os.path.exists(self.review_dir):
                    for item in os.listdir(self.review_dir):
                        item_path = os.path.join(self.review_dir, item)
                        if os.path.isdir(item_path):
                            result_file = os.path.join(item_path, "review_result.json")
                            if os.path.exists(result_file):
                                with open(result_file, 'r', encoding='utf-8') as f:
                                    result = json.load(f)
                                reviews.append(result)
                
                return {
                    "success": True,
                    "recent_reviews": sorted(reviews, key=lambda x: x.get("started_at", ""), reverse=True)[:10]
                }
                
        except Exception as e:
            logger.error(f"获取审查状态失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取审查状态失败: {str(e)}"
            }
    
    async def _security_check(self, code_dir: str) -> Dict[str, Any]:
        """安全检查"""
        try:
            security_issues = []
            
            # 扫描所有Python文件
            for root, dirs, files in os.walk(code_dir):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 检查常见安全问题
                        issues = self._check_security_patterns(content, file_path)
                        security_issues.extend(issues)
            
            score = max(0, 100 - len(security_issues) * 10)
            
            return {
                "score": score,
                "issues": security_issues,
                "status": "pass" if score >= 80 else "warning" if score >= 60 else "fail"
            }
            
        except Exception as e:
            logger.error(f"安全检查失败: {str(e)}")
            return {
                "score": 0,
                "issues": [f"安全检查失败: {str(e)}"],
                "status": "error"
            }
    
    async def _quality_check(self, code_dir: str) -> Dict[str, Any]:
        """代码质量检查"""
        try:
            quality_issues = []
            
            # 扫描所有Python文件
            for root, dirs, files in os.walk(code_dir):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 检查代码质量问题
                        issues = self._check_quality_patterns(content, file_path)
                        quality_issues.extend(issues)
            
            score = max(0, 100 - len(quality_issues) * 5)
            
            return {
                "score": score,
                "issues": quality_issues,
                "status": "pass" if score >= 80 else "warning" if score >= 60 else "fail"
            }
            
        except Exception as e:
            logger.error(f"质量检查失败: {str(e)}")
            return {
                "score": 0,
                "issues": [f"质量检查失败: {str(e)}"],
                "status": "error"
            }
    
    async def _compliance_check(self, code_dir: str) -> Dict[str, Any]:
        """合规性检查"""
        try:
            compliance_issues = []
            
            # 检查必要的文件是否存在
            required_files = ['__init__.py']
            for root, dirs, files in os.walk(code_dir):
                if any(f.endswith('.py') for f in files):
                    # 如果目录包含Python文件，检查是否有__init__.py
                    if '__init__.py' not in files:
                        compliance_issues.append(f"缺少__init__.py文件: {root}")
            
            score = max(0, 100 - len(compliance_issues) * 15)
            
            return {
                "score": score,
                "issues": compliance_issues,
                "status": "pass" if score >= 80 else "warning" if score >= 60 else "fail"
            }
            
        except Exception as e:
            logger.error(f"合规性检查失败: {str(e)}")
            return {
                "score": 0,
                "issues": [f"合规性检查失败: {str(e)}"],
                "status": "error"
            }
    
    async def _performance_check(self, code_dir: str) -> Dict[str, Any]:
        """性能检查"""
        try:
            performance_issues = []
            
            # 扫描所有Python文件
            for root, dirs, files in os.walk(code_dir):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 检查性能问题
                        issues = self._check_performance_patterns(content, file_path)
                        performance_issues.extend(issues)
            
            score = max(0, 100 - len(performance_issues) * 8)
            
            return {
                "score": score,
                "issues": performance_issues,
                "status": "pass" if score >= 80 else "warning" if score >= 60 else "fail"
            }
            
        except Exception as e:
            logger.error(f"性能检查失败: {str(e)}")
            return {
                "score": 0,
                "issues": [f"性能检查失败: {str(e)}"],
                "status": "error"
            }
    
    def _check_security_patterns(self, content: str, file_path: str) -> List[str]:
        """检查安全模式"""
        issues = []
        
        # 检查SQL注入风险
        if re.search(r'execute\s*\(\s*["\'].*%.*["\']', content):
            issues.append(f"可能的SQL注入风险: {file_path}")
        
        # 检查硬编码密码
        if re.search(r'password\s*=\s*["\'][^"\']+["\']', content, re.IGNORECASE):
            issues.append(f"硬编码密码: {file_path}")
        
        # 检查eval使用
        if 'eval(' in content:
            issues.append(f"使用了eval函数: {file_path}")
        
        return issues
    
    def _check_quality_patterns(self, content: str, file_path: str) -> List[str]:
        """检查质量模式"""
        issues = []
        
        # 检查函数长度
        functions = re.findall(r'def\s+\w+.*?(?=\ndef|\nclass|\Z)', content, re.DOTALL)
        for func in functions:
            lines = func.split('\n')
            if len(lines) > 50:
                issues.append(f"函数过长 (>{len(lines)}行): {file_path}")
        
        # 检查注释
        lines = content.split('\n')
        code_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
        comment_lines = [line for line in lines if line.strip().startswith('#')]
        
        if code_lines and len(comment_lines) / len(code_lines) < 0.1:
            issues.append(f"注释不足: {file_path}")
        
        return issues
    
    def _check_performance_patterns(self, content: str, file_path: str) -> List[str]:
        """检查性能模式"""
        issues = []
        
        # 检查循环中的数据库查询
        if re.search(r'for\s+.*?:\s*.*?\.query\(', content, re.DOTALL):
            issues.append(f"循环中的数据库查询: {file_path}")
        
        # 检查大量字符串拼接
        if content.count('+=') > 10:
            issues.append(f"大量字符串拼接操作: {file_path}")
        
        return issues
    
    def _calculate_overall_score(self, checks: Dict[str, Any]) -> int:
        """计算总体评分"""
        scores = []
        weights = {
            "security": 0.4,
            "quality": 0.3,
            "compliance": 0.2,
            "performance": 0.1
        }
        
        for check_name, check_result in checks.items():
            if check_name in weights and isinstance(check_result, dict):
                score = check_result.get("score", 0)
                weight = weights[check_name]
                scores.append(score * weight)
        
        return int(sum(scores)) if scores else 0
    
    def _get_recommendation(self, score: int) -> str:
        """获取推荐意见"""
        if score >= 90:
            return "优秀，建议批准"
        elif score >= 80:
            return "良好，可以批准"
        elif score >= 70:
            return "一般，建议修改后批准"
        elif score >= 60:
            return "较差，需要修改"
        else:
            return "很差，不建议使用"


# 全局代码审查服务实例
code_review_service = CodeReviewService()
