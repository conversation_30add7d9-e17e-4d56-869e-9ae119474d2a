/**
 * 表单预览组件
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Spin,
  message,
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Alert
} from 'antd';
import {
  EyeOutlined,
  CodeOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { FormAPI } from '../../../services/developer/formAPI';
import type { FormConfig, FormRenderConfig } from '../../../types/developer/form';

const { Text } = Typography;
const { TabPane } = Tabs;

interface FormPreviewProps {
  form: FormConfig;
  visible: boolean;
  onClose: () => void;
}

const FormPreview: React.FC<FormPreviewProps> = ({ form, visible, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [renderConfig, setRenderConfig] = useState<FormRenderConfig | null>(null);
  const [activeTab, setActiveTab] = useState('preview');
  const [previewMode, setPreviewMode] = useState<'create' | 'edit' | 'view'>('create');

  // 加载渲染配置
  useEffect(() => {
    if (visible && form) {
      loadRenderConfig();
    }
  }, [visible, form, previewMode]);

  const loadRenderConfig = async () => {
    setLoading(true);
    try {
      const config = await FormAPI.getFormRenderConfig(form.id, {
        mode: previewMode
      });
      setRenderConfig(config);
    } catch (error) {
      console.error('加载渲染配置失败:', error);
      message.error('加载渲染配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出amis schema
  const handleExportSchema = () => {
    if (!renderConfig) return;

    const dataStr = JSON.stringify(renderConfig.schema, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${form.name}_schema.json`;
    link.click();
    URL.revokeObjectURL(url);
    message.success('Schema导出成功');
  };

  // 渲染表单预览
  const renderFormPreview = () => {
    if (!renderConfig) return null;

    // 简化的表单渲染，避免amis依赖问题
    return (
      <div className="form-preview-content">
        <Alert
          message="表单预览"
          description="这里将显示根据配置生成的amis表单"
          type="info"
          showIcon
        />
        <div style={{ marginTop: 16 }}>
          <Text strong>表单Schema预览:</Text>
          <pre style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '4px',
            marginTop: '8px',
            fontSize: '12px'
          }}>
            {JSON.stringify(renderConfig.schema, null, 2)}
          </pre>
        </div>
      </div>
    );
  };

  // 渲染Schema代码
  const renderSchemaCode = () => {
    if (!renderConfig) return null;

    return (
      <pre style={{
        background: '#f5f5f5',
        padding: '16px',
        borderRadius: '4px',
        overflow: 'auto',
        maxHeight: '600px'
      }}>
        <code>
          {JSON.stringify(renderConfig.schema, null, 2)}
        </code>
      </pre>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <EyeOutlined />
          <span>表单预览 - {form.name}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button
          key="export"
          icon={<DownloadOutlined />}
          onClick={handleExportSchema}
          disabled={!renderConfig}
        >
          导出Schema
        </Button>
      ]}
    >
      <div className="form-preview">
        <div className="preview-header">
          <Space>
            <Text strong>预览模式:</Text>
            <Button.Group>
              <Button
                type={previewMode === 'create' ? 'primary' : 'default'}
                onClick={() => setPreviewMode('create')}
              >
                创建模式
              </Button>
              <Button
                type={previewMode === 'edit' ? 'primary' : 'default'}
                onClick={() => setPreviewMode('edit')}
              >
                编辑模式
              </Button>
              <Button
                type={previewMode === 'view' ? 'primary' : 'default'}
                onClick={() => setPreviewMode('view')}
              >
                查看模式
              </Button>
            </Button.Group>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <EyeOutlined />
                <span>表单预览</span>
              </Space>
            }
            key="preview"
          >
            <Card>
              <Spin spinning={loading}>
                <div className="form-preview-container">
                  {renderFormPreview()}
                </div>
              </Spin>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <CodeOutlined />
                <span>Schema代码</span>
              </Space>
            }
            key="schema"
          >
            <Card title="Amis Schema">
              <Spin spinning={loading}>
                {renderSchemaCode()}
              </Spin>
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </Modal>
  );
};

export default FormPreview;