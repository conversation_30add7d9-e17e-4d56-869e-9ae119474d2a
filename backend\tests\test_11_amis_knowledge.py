#!/usr/bin/env python3
"""
第十一部分：AMIS知识库和校验器单元测试
测试amis知识库、组件模板、schema校验器等功能
"""
import unittest
import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.ai.amis_knowledge import amis_knowledge, AmisComponentType, AmisFormItemType
from app.core.ai.amis_validator import amis_validator, ValidationLevel


class TestAmisKnowledge(unittest.TestCase):
    """AMIS知识库测试类"""
    
    def test_component_types_enum(self):
        """测试组件类型枚举"""
        print("\n=== 测试组件类型枚举 ===")
        
        # 测试基本组件类型
        self.assertEqual(AmisComponentType.PAGE.value, "page")
        self.assertEqual(AmisComponentType.FORM.value, "form")
        self.assertEqual(AmisComponentType.CRUD.value, "crud")
        
        # 测试组件类型数量（应该有大量组件）
        component_count = len(list(AmisComponentType))
        print(f"支持的组件类型数量: {component_count}")
        self.assertGreater(component_count, 50, "应该支持50+种组件类型")
        
        # 测试新增的组件类型
        self.assertEqual(AmisComponentType.INPUT_TEXT.value, "input-text")
        self.assertEqual(AmisComponentType.TABLE2.value, "table2")
        self.assertEqual(AmisComponentType.CHART.value, "chart")
        
        print("✅ 组件类型枚举测试通过")
    
    def test_form_item_types_enum(self):
        """测试表单项类型枚举"""
        print("\n=== 测试表单项类型枚举 ===")
        
        # 测试基础表单项
        self.assertEqual(AmisFormItemType.INPUT_TEXT.value, "input-text")
        self.assertEqual(AmisFormItemType.SELECT.value, "select")
        self.assertEqual(AmisFormItemType.TEXTAREA.value, "textarea")
        
        # 测试高级表单项
        self.assertEqual(AmisFormItemType.INPUT_DATE_RANGE.value, "input-date-range")
        self.assertEqual(AmisFormItemType.TRANSFER.value, "transfer")
        self.assertEqual(AmisFormItemType.JSON_SCHEMA.value, "json-schema")
        
        # 测试表单项类型数量
        form_item_count = len(list(AmisFormItemType))
        print(f"支持的表单项类型数量: {form_item_count}")
        self.assertGreater(form_item_count, 30, "应该支持30+种表单项类型")
        
        print("✅ 表单项类型枚举测试通过")
    
    def test_component_templates(self):
        """测试组件模板"""
        print("\n=== 测试组件模板 ===")
        
        # 测试页面模板
        page_template = amis_knowledge.get_component_template(AmisComponentType.PAGE, "basic")
        self.assertIsInstance(page_template, dict)
        self.assertEqual(page_template.get("type"), "page")
        self.assertIn("title", page_template)
        self.assertIn("body", page_template)
        print(f"✅ 页面模板: {page_template.get('type')}")
        
        # 测试表单模板
        form_template = amis_knowledge.get_component_template(AmisComponentType.FORM, "with_validation")
        self.assertIsInstance(form_template, dict)
        self.assertEqual(form_template.get("type"), "form")
        self.assertEqual(form_template.get("mode"), "horizontal")
        print(f"✅ 表单模板: {form_template.get('mode')} 模式")
        
        # 测试CRUD模板
        crud_template = amis_knowledge.get_component_template(AmisComponentType.CRUD, "with_filter")
        self.assertIsInstance(crud_template, dict)
        self.assertEqual(crud_template.get("type"), "crud")
        self.assertIn("filter", crud_template)
        print(f"✅ CRUD模板: 包含筛选功能")
        
        # 测试不存在的模板
        empty_template = amis_knowledge.get_component_template(AmisComponentType.PAGE, "nonexistent")
        self.assertEqual(empty_template, {})
        
        print("✅ 组件模板测试通过")
    
    def test_form_item_config(self):
        """测试表单项配置生成"""
        print("\n=== 测试表单项配置生成 ===")
        
        # 测试文本输入
        text_config = amis_knowledge.get_form_item_config("string", "username", "用户名", required=True)
        self.assertEqual(text_config["type"], "input-text")
        self.assertEqual(text_config["name"], "username")
        self.assertEqual(text_config["label"], "用户名")
        self.assertTrue(text_config["required"])
        print(f"✅ 文本输入: {text_config['type']}")
        
        # 测试选择框
        select_config = amis_knowledge.get_form_item_config("select", "status", "状态", 
                                                           options=[{"label": "启用", "value": "active"}])
        self.assertEqual(select_config["type"], "select")
        self.assertIn("options", select_config)
        print(f"✅ 选择框: {select_config['type']}")
        
        # 测试日期选择
        date_config = amis_knowledge.get_form_item_config("date", "birthday", "生日")
        self.assertEqual(date_config["type"], "input-date")
        self.assertEqual(date_config["format"], "YYYY-MM-DD")
        print(f"✅ 日期选择: {date_config['type']}")
        
        # 测试数字输入
        number_config = amis_knowledge.get_form_item_config("number", "age", "年龄")
        self.assertEqual(number_config["type"], "input-number")
        print(f"✅ 数字输入: {number_config['type']}")
        
        # 测试开关
        switch_config = amis_knowledge.get_form_item_config("boolean", "active", "是否激活")
        self.assertEqual(switch_config["type"], "switch")
        self.assertTrue(switch_config["trueValue"])
        self.assertFalse(switch_config["falseValue"])
        print(f"✅ 开关: {switch_config['type']}")
        
        print("✅ 表单项配置生成测试通过")
    
    def test_crud_column_config(self):
        """测试CRUD列配置生成"""
        print("\n=== 测试CRUD列配置生成 ===")
        
        # 测试文本列
        text_column = amis_knowledge.get_crud_column_config("username", "用户名", "text", sortable=True)
        self.assertEqual(text_column["name"], "username")
        self.assertEqual(text_column["label"], "用户名")
        self.assertTrue(text_column["sortable"])
        print(f"✅ 文本列: {text_column['name']}")
        
        # 测试日期时间列
        datetime_column = amis_knowledge.get_crud_column_config("created_at", "创建时间", "datetime")
        self.assertEqual(datetime_column["type"], "datetime")
        self.assertEqual(datetime_column["format"], "YYYY-MM-DD HH:mm:ss")
        print(f"✅ 日期时间列: {datetime_column['type']}")
        
        # 测试状态列
        status_column = amis_knowledge.get_crud_column_config("active", "状态", "boolean")
        self.assertEqual(status_column["type"], "status")
        self.assertIn("map", status_column)
        print(f"✅ 状态列: {status_column['type']}")
        
        # 测试图片列
        image_column = amis_knowledge.get_crud_column_config("avatar", "头像", "image")
        self.assertEqual(image_column["type"], "image")
        self.assertTrue(image_column["enlargeAble"])
        print(f"✅ 图片列: {image_column['type']}")
        
        print("✅ CRUD列配置生成测试通过")
    
    def test_best_practices(self):
        """测试最佳实践"""
        print("\n=== 测试最佳实践 ===")
        
        # 测试表单设计最佳实践
        form_practices = amis_knowledge.get_best_practices("form_design")
        self.assertIsInstance(form_practices, list)
        self.assertGreater(len(form_practices), 0)
        print(f"✅ 表单设计最佳实践: {len(form_practices)} 条")
        
        # 测试CRUD设计最佳实践
        crud_practices = amis_knowledge.get_best_practices("crud_design")
        self.assertIsInstance(crud_practices, list)
        self.assertGreater(len(crud_practices), 0)
        print(f"✅ CRUD设计最佳实践: {len(crud_practices)} 条")
        
        # 测试API集成最佳实践
        api_practices = amis_knowledge.get_best_practices("api_integration")
        self.assertIsInstance(api_practices, list)
        self.assertGreater(len(api_practices), 0)
        print(f"✅ API集成最佳实践: {len(api_practices)} 条")
        
        # 测试不存在的分类
        empty_practices = amis_knowledge.get_best_practices("nonexistent")
        self.assertEqual(empty_practices, [])
        
        print("✅ 最佳实践测试通过")
    
    def test_api_config_generation(self):
        """测试API配置生成"""
        print("\n=== 测试API配置生成 ===")
        
        # 测试GET请求
        get_config = amis_knowledge.generate_api_config("GET", "/api/users")
        self.assertEqual(get_config["method"], "GET")
        self.assertEqual(get_config["url"], "/api/users")
        print(f"✅ GET请求配置: {get_config['method']} {get_config['url']}")
        
        # 测试POST请求
        post_config = amis_knowledge.generate_api_config("POST", "/api/users", 
                                                        data={"name": "${name}", "email": "${email}"})
        self.assertEqual(post_config["method"], "POST")
        self.assertIn("data", post_config)
        print(f"✅ POST请求配置: {post_config['method']} {post_config['url']}")
        
        # 测试带头部的请求
        header_config = amis_knowledge.generate_api_config("PUT", "/api/users/${id}", 
                                                          headers={"Authorization": "Bearer ${token}"})
        self.assertIn("headers", header_config)
        print(f"✅ 带头部请求配置: {header_config['method']}")
        
        print("✅ API配置生成测试通过")


class TestAmisValidator(unittest.TestCase):
    """AMIS校验器测试类"""
    
    def test_valid_schema_validation(self):
        """测试有效schema校验"""
        print("\n=== 测试有效schema校验 ===")
        
        valid_schema = {
            "type": "page",
            "title": "用户管理",
            "body": [
                {
                    "type": "crud",
                    "api": "/api/users",
                    "columns": [
                        {"name": "id", "label": "ID", "type": "text"},
                        {"name": "username", "label": "用户名", "type": "text"},
                        {"name": "email", "label": "邮箱", "type": "text"}
                    ]
                }
            ]
        }
        
        result = amis_validator.validate(valid_schema)
        
        self.assertTrue(result.is_valid, "有效schema应该通过校验")
        self.assertEqual(len(result.errors), 0, "有效schema不应该有错误")
        print(f"✅ 有效schema校验通过，警告数: {len(result.warnings)}")
    
    def test_invalid_schema_validation(self):
        """测试无效schema校验"""
        print("\n=== 测试无效schema校验 ===")
        
        # 测试缺少type字段
        invalid_schema1 = {
            "title": "页面标题"
            # 缺少type字段
        }
        
        result1 = amis_validator.validate(invalid_schema1)
        self.assertFalse(result1.is_valid, "缺少type字段的schema应该校验失败")
        self.assertGreater(len(result1.errors), 0, "应该有错误信息")
        print(f"✅ 缺少type字段校验失败，错误数: {len(result1.errors)}")
        
        # 测试CRUD缺少API
        invalid_schema2 = {
            "type": "crud",
            # 缺少api字段
            "columns": []
        }
        
        result2 = amis_validator.validate(invalid_schema2)
        self.assertFalse(result2.is_valid, "CRUD缺少API应该校验失败")
        print(f"✅ CRUD缺少API校验失败，错误数: {len(result2.errors)}")
    
    def test_form_validation(self):
        """测试表单校验"""
        print("\n=== 测试表单校验 ===")
        
        # 测试完整表单
        form_schema = {
            "type": "form",
            "api": "post:/api/users",
            "body": [
                {
                    "type": "input-text",
                    "name": "username",
                    "label": "用户名",
                    "required": True
                },
                {
                    "type": "select",
                    "name": "status",
                    "label": "状态",
                    "options": [
                        {"label": "启用", "value": "active"},
                        {"label": "禁用", "value": "inactive"}
                    ]
                }
            ]
        }
        
        result = amis_validator.validate(form_schema)
        self.assertTrue(result.is_valid, "完整表单应该通过校验")
        print(f"✅ 完整表单校验通过")
        
        # 测试表单项缺少name
        invalid_form = {
            "type": "form",
            "api": "post:/api/users",
            "body": [
                {
                    "type": "input-text",
                    "label": "用户名"
                    # 缺少name字段
                }
            ]
        }
        
        result = amis_validator.validate(invalid_form)
        self.assertFalse(result.is_valid, "表单项缺少name应该校验失败")
        print(f"✅ 表单项缺少name校验失败，错误数: {len(result.errors)}")
    
    def test_table_validation(self):
        """测试表格校验"""
        print("\n=== 测试表格校验 ===")
        
        # 测试完整表格
        table_schema = {
            "type": "table",
            "data": [],
            "columns": [
                {"name": "id", "label": "ID"},
                {"name": "name", "label": "名称"}
            ]
        }
        
        result = amis_validator.validate(table_schema)
        self.assertTrue(result.is_valid, "完整表格应该通过校验")
        print(f"✅ 完整表格校验通过")
        
        # 测试表格缺少列
        invalid_table = {
            "type": "table",
            "data": []
            # 缺少columns字段
        }
        
        result = amis_validator.validate(invalid_table)
        self.assertFalse(result.is_valid, "表格缺少列应该校验失败")
        print(f"✅ 表格缺少列校验失败，错误数: {len(result.errors)}")
    
    def test_validation_result_structure(self):
        """测试校验结果结构"""
        print("\n=== 测试校验结果结构 ===")
        
        schema = {
            "type": "page",
            "body": [
                {
                    "type": "form",
                    "body": [
                        {
                            "type": "select",
                            "name": "status"
                            # 缺少options，应该产生警告
                        }
                    ]
                }
            ]
        }
        
        result = amis_validator.validate(schema)
        result_dict = result.to_dict()
        
        # 检查结果结构
        self.assertIn("is_valid", result_dict)
        self.assertIn("errors", result_dict)
        self.assertIn("warnings", result_dict)
        self.assertIn("infos", result_dict)
        self.assertIn("summary", result_dict)
        
        # 检查摘要信息
        summary = result_dict["summary"]
        self.assertIn("error_count", summary)
        self.assertIn("warning_count", summary)
        self.assertIn("info_count", summary)
        
        print(f"✅ 校验结果结构完整")
        print(f"  错误数: {summary['error_count']}")
        print(f"  警告数: {summary['warning_count']}")
        print(f"  建议数: {summary['info_count']}")


def run_tests():
    """运行所有测试"""
    print("开始AMIS知识库和校验器测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestAmisKnowledge))
    suite.addTests(loader.loadTestsFromTestCase(TestAmisValidator))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("🎉 所有测试通过！AMIS知识库和校验器功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
