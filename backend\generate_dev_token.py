#!/usr/bin/env python3
"""
生成开发者token的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.auth.jwt_handler import jwt_handler

def generate_developer_token():
    """生成开发者token"""
    
    # 开发者数据
    developer_data = {
        "user_id": "dev_001",
        "username": "developer",
        "user_type": "developer",
        "permissions": ["all"]
    }
    
    # 生成token
    token_info = jwt_handler.create_access_token(developer_data)
    
    print("🔑 开发者Token生成成功!")
    print("=" * 50)
    print(f"Token: {token_info['token']}")
    print(f"类型: {token_info['token_type']}")
    print(f"有效期: {token_info['expires_in']} 秒")
    print(f"签发时间: {token_info['issued_at']}")
    print(f"过期时间: {token_info['expires_at']}")
    print("=" * 50)
    
    return token_info['token']

if __name__ == "__main__":
    token = generate_developer_token()
    
    # 保存到文件
    with open("dev_token.txt", "w") as f:
        f.write(token)
    
    print("✅ Token已保存到 dev_token.txt 文件")
