"""
阿里云百炼API客户端
支持qwen-coder-plus模型调用，包含错误处理和重试机制
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
import aiohttp
from datetime import datetime

from app.config import settings
from app.core.ai.amis_knowledge import amis_knowledge
from app.core.ai.prompt_templates import prompt_manager, PromptType

logger = logging.getLogger(__name__)


class BailianAPIError(Exception):
    """百炼API异常"""
    def __init__(self, message: str, error_code: Optional[str] = None, status_code: Optional[int] = None):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)


class BailianClient:
    """阿里云百炼API客户端 - 使用DashScope兼容OpenAI格式"""

    def __init__(self):
        self.api_key = settings.DASHSCOPE_API_KEY
        self.base_url = settings.DASHSCOPE_BASE_URL
        self.model = settings.DASHSCOPE_MODEL
        self.model_name = settings.DASHSCOPE_MODEL  # 添加model_name别名
        self.timeout = settings.DASHSCOPE_TIMEOUT
        self.max_retries = settings.DASHSCOPE_MAX_RETRIES

        # 检查API密钥是否可用
        self.is_available = bool(self.api_key and self.api_key.strip())

        # 请求头 - 使用OpenAI兼容格式
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    async def _make_request(self, endpoint: str, data: Dict[str, Any], retry_count: int = 0) -> Dict[str, Any]:
        """发送HTTP请求 - 使用OpenAI兼容格式"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, headers=self.headers, json=data) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        try:
                            return json.loads(response_text)
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析失败: {e}, 响应内容: {response_text}")
                            raise BailianAPIError(f"响应格式错误: {str(e)}")
                    else:
                        logger.error(f"API请求失败: {response.status}, 响应: {response_text}")
                        raise BailianAPIError(
                            f"API请求失败: {response_text}",
                            status_code=response.status
                        )
                        
        except asyncio.TimeoutError:
            if retry_count < self.max_retries:
                logger.warning(f"请求超时，正在重试 ({retry_count + 1}/{self.max_retries})")
                await asyncio.sleep(2 ** retry_count)  # 指数退避
                return await self._make_request(endpoint, data, retry_count + 1)
            else:
                raise BailianAPIError("请求超时，已达到最大重试次数")
                
        except aiohttp.ClientError as e:
            if retry_count < self.max_retries:
                logger.warning(f"网络错误，正在重试 ({retry_count + 1}/{self.max_retries}): {str(e)}")
                await asyncio.sleep(2 ** retry_count)
                return await self._make_request(endpoint, data, retry_count + 1)
            else:
                raise BailianAPIError(f"网络错误: {str(e)}")
    
    async def generate_code(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """生成代码 - 使用OpenAI兼容格式"""
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的FastAPI后端开发工程师，擅长生成高质量的Python代码。请根据用户需求生成完整、可运行的代码。不要输出代码之外的内容。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]

        # 如果有上下文信息，添加到系统消息中
        if context:
            context_info = f"\n\n上下文信息：\n{json.dumps(context, ensure_ascii=False, indent=2)}"
            messages[0]["content"] += context_info

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.1,  # 降低随机性，提高代码质量
            "max_tokens": 4000,
            "top_p": 0.8
        }

        try:
            response = await self._make_request("chat/completions", data)

            if "choices" in response and len(response["choices"]) > 0:
                generated_content = response["choices"][0]["message"]["content"]

                return {
                    "success": True,
                    "content": generated_content,
                    "usage": response.get("usage", {}),
                    "model": self.model,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise BailianAPIError("API响应格式异常：缺少choices字段")

        except BailianAPIError:
            raise
        except Exception as e:
            logger.error(f"代码生成失败: {str(e)}")
            raise BailianAPIError(f"代码生成失败: {str(e)}")
    
    async def analyze_intent(self, command: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """分析用户意图"""
        system_prompt = """你是一个智能意图分析助手，专门分析用户的自然语言命令并识别其意图。
请分析用户命令，返回JSON格式的结果，包含以下字段：
- intent_type: 意图类型 (data_query, data_create, data_update, data_delete, workflow_execute, report_generate等)
- entity: 涉及的业务实体
- action: 具体操作
- parameters: 提取的参数
- confidence: 置信度 (0-1)
"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请分析这个命令的意图：{command}"}
        ]
        
        if context:
            context_info = f"\n\n当前上下文：\n{json.dumps(context, ensure_ascii=False, indent=2)}"
            messages[1]["content"] += context_info
        
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        try:
            response = await self._make_request("chat/completions", data)
            
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                
                # 尝试解析JSON响应
                try:
                    intent_data = json.loads(content)
                    return {
                        "success": True,
                        "intent": intent_data,
                        "raw_response": content,
                        "timestamp": datetime.now().isoformat()
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，返回原始内容
                    return {
                        "success": True,
                        "intent": {
                            "intent_type": "unknown",
                            "raw_analysis": content,
                            "confidence": 0.5
                        },
                        "raw_response": content,
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                raise BailianAPIError("API响应格式异常：缺少choices字段")
                
        except BailianAPIError:
            raise
        except Exception as e:
            logger.error(f"意图分析失败: {str(e)}")
            raise BailianAPIError(f"意图分析失败: {str(e)}")
    
    async def generate_amis_schema(self, intent: Dict[str, Any], scenario_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """生成amis schema - 使用专业的amis知识库"""
        try:
            # 分析意图类型，选择合适的生成策略
            intent_type = intent.get("intent_type", "unknown")
            entity = intent.get("entity", "")

            # 构建API端点信息
            api_endpoints = self._build_api_endpoints(entity, intent_type)

            # 构建实体信息
            entities = self._build_entity_info(entity, scenario_config)

            # 使用专业的amis提示词模板
            messages = prompt_manager.get_messages(
                PromptType.AMIS_SCHEMA_GENERATION,
                intent=json.dumps(intent, ensure_ascii=False, indent=2),
                entities=json.dumps(entities, ensure_ascii=False, indent=2),
                scenario_config=json.dumps(scenario_config or {}, ensure_ascii=False, indent=2),
                api_endpoints=json.dumps(api_endpoints, ensure_ascii=False, indent=2)
            )

            data = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.2,
                "max_tokens": 3000
            }

            response = await self._make_request("chat/completions", data)

            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]

                # 尝试提取JSON schema
                try:
                    # 查找JSON内容
                    start_idx = content.find('{')
                    end_idx = content.rfind('}') + 1

                    if start_idx != -1 and end_idx > start_idx:
                        json_content = content[start_idx:end_idx]
                        schema = json.loads(json_content)

                        # 使用amis知识库优化schema
                        optimized_schema = self._optimize_amis_schema(schema, intent_type)

                        return {
                            "success": True,
                            "schema": optimized_schema,
                            "raw_response": content,
                            "timestamp": datetime.now().isoformat()
                        }
                    else:
                        raise ValueError("未找到有效的JSON内容")

                except (json.JSONDecodeError, ValueError) as e:
                    logger.warning(f"Schema解析失败: {e}, 返回原始内容")
                    return {
                        "success": False,
                        "error": f"Schema解析失败: {str(e)}",
                        "raw_response": content,
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                raise BailianAPIError("API响应格式异常：缺少choices字段")

        except BailianAPIError:
            raise
        except Exception as e:
            logger.error(f"Schema生成失败: {str(e)}")
            raise BailianAPIError(f"Schema生成失败: {str(e)}")


    def _build_api_endpoints(self, entity: str, intent_type: str) -> Dict[str, Any]:
        """构建API端点信息"""
        base_path = f"/api/{entity.lower()}" if entity else "/api/data"

        endpoints = {
            "list": f"GET {base_path}",
            "create": f"POST {base_path}",
            "update": f"PUT {base_path}/{{id}}",
            "delete": f"DELETE {base_path}/{{id}}",
            "detail": f"GET {base_path}/{{id}}"
        }

        # 根据意图类型返回相关的端点
        if intent_type == "data_query":
            return {"list": endpoints["list"], "detail": endpoints["detail"]}
        elif intent_type == "data_create":
            return {"create": endpoints["create"]}
        elif intent_type == "data_update":
            return {"update": endpoints["update"], "detail": endpoints["detail"]}
        elif intent_type == "data_delete":
            return {"delete": endpoints["delete"], "list": endpoints["list"]}
        else:
            return endpoints

    def _build_entity_info(self, entity: str, scenario_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """构建实体信息"""
        if not entity:
            return {}

        # 基础实体信息
        entity_info = {
            "name": entity,
            "label": entity,
            "fields": []
        }

        # 从场景配置中获取字段信息
        if scenario_config and "entities" in scenario_config:
            for ent in scenario_config["entities"]:
                if ent.get("name") == entity:
                    entity_info.update(ent)
                    break

        # 如果没有字段信息，提供默认字段
        if not entity_info["fields"]:
            entity_info["fields"] = [
                {"name": "id", "label": "ID", "type": "number", "primary": True},
                {"name": "name", "label": "名称", "type": "string", "required": True},
                {"name": "created_at", "label": "创建时间", "type": "datetime"},
                {"name": "updated_at", "label": "更新时间", "type": "datetime"}
            ]

        return entity_info

    def _optimize_amis_schema(self, schema: Dict[str, Any], intent_type: str) -> Dict[str, Any]:
        """使用amis知识库优化schema"""
        try:
            # 确保schema有正确的基础结构
            if "type" not in schema:
                schema["type"] = "page"

            # 根据意图类型优化schema
            if intent_type == "data_query":
                schema = self._optimize_query_schema(schema)
            elif intent_type == "data_create":
                schema = self._optimize_create_schema(schema)
            elif intent_type == "data_update":
                schema = self._optimize_update_schema(schema)

            return schema

        except Exception as e:
            logger.warning(f"Schema优化失败: {e}, 返回原始schema")
            return schema

    def _optimize_query_schema(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """优化查询类schema"""
        # 确保有CRUD组件
        if "body" in schema and isinstance(schema["body"], list):
            for component in schema["body"]:
                if component.get("type") == "crud":
                    # 添加默认的分页配置
                    if "footerToolbar" not in component:
                        component["footerToolbar"] = ["pagination"]

                    # 确保有列配置
                    if "columns" not in component:
                        component["columns"] = []

        return schema

    def _optimize_create_schema(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """优化创建类schema"""
        # 确保有表单组件
        if "body" in schema and isinstance(schema["body"], list):
            for component in schema["body"]:
                if component.get("type") == "form":
                    # 添加默认的提交按钮
                    if "actions" not in component:
                        component["actions"] = [
                            {"type": "submit", "label": "提交", "level": "primary"},
                            {"type": "reset", "label": "重置"}
                        ]

        return schema

    def _optimize_update_schema(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """优化更新类schema"""
        # 类似创建schema的优化
        return self._optimize_create_schema(schema)

    async def chat_completion(self, messages: List[Dict[str, str]],
                             temperature: float = 0.7,
                             max_tokens: int = 1000,
                             **kwargs) -> Dict[str, Any]:
        """通用聊天完成接口 - 使用OpenAI兼容格式"""
        try:
            data = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                **kwargs
            }

            response = await self._make_request("chat/completions", data)

            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]

                return {
                    "success": True,
                    "content": content,
                    "usage": response.get("usage", {}),
                    "model": self.model,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise BailianAPIError("API响应格式异常：缺少choices字段")

        except BailianAPIError:
            raise
        except Exception as e:
            logger.error(f"聊天完成失败: {str(e)}")
            return {
                "success": False,
                "error": f"聊天完成失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    async def health_check(self) -> Dict[str, Any]:
        """健康检查 - 使用OpenAI兼容格式"""
        try:
            test_data = {
                "model": self.model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }

            start_time = datetime.now()
            response = await self._make_request("chat/completions", test_data)
            end_time = datetime.now()

            response_time = (end_time - start_time).total_seconds()

            return {
                "status": "healthy",
                "response_time": response_time,
                "model": self.model,
                "api_key_valid": True,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "api_key_valid": False,
                "timestamp": datetime.now().isoformat()
            }

    def get_health_status(self) -> Dict[str, Any]:
        """获取客户端健康状态"""
        return {
            "status": "healthy" if self.is_available else "unavailable",
            "api_key_configured": self.api_key is not None,
            "model": self.model_name,
            "base_url": self.base_url,
            "timestamp": datetime.now().isoformat()
        }


# 全局客户端实例
bailian_client = BailianClient()
