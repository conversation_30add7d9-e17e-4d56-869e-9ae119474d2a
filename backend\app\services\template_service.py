"""
模板管理服务
处理模板查询、筛选和详情获取的业务逻辑
"""
from typing import Optional, Dict, Any, List
from app.schemas.template import (
    TemplateListItem, TemplateDetail, TemplatePreview, CategoryStats,
    TemplateCategory, TemplateDifficulty, TemplateConfig, ScenarioConfig,
    Entity, EntityField, FieldValidation, Workflow, WorkflowStep,
    Form, FormSection, API, Role, SetupGuide
)


class TemplateService:
    """模板管理服务类"""
    
    def __init__(self):
        # 预设模板数据
        self.templates = {
            "ecommerce_basic": {
                "key": "ecommerce_basic",
                "name": "基础电商系统",
                "description": "适合中小型电商企业的基础管理系统",
                "category": TemplateCategory.BUSINESS,
                "difficulty": TemplateDifficulty.BEGINNER,
                "estimated_setup_time": "30分钟",
                "version": "1.2.0",
                "author": "AILF Team",
                "license": "MIT",
                "features": ["商品管理", "订单处理", "用户管理", "库存管理"],
                "tags": ["电商", "零售", "管理系统"],
                "created_at": "2024-01-15T00:00:00.000Z",
                "updated_at": "2024-01-18T00:00:00.000Z",
                "preview": {
                    "entities": 4,
                    "workflows": 3,
                    "forms": 6,
                    "apis": 12
                }
            },
            "hospital_management": {
                "key": "hospital_management",
                "name": "医院管理系统",
                "description": "完整的医院信息管理系统模板",
                "category": TemplateCategory.HEALTHCARE,
                "difficulty": TemplateDifficulty.INTERMEDIATE,
                "estimated_setup_time": "45分钟",
                "version": "1.1.0",
                "author": "AILF Team",
                "license": "MIT",
                "features": ["患者管理", "医生排班", "预约挂号", "病历管理", "药品管理"],
                "tags": ["医疗", "管理系统", "预约"],
                "created_at": "2024-01-15T00:00:00.000Z",
                "updated_at": "2024-01-19T00:00:00.000Z",
                "preview": {
                    "entities": 6,
                    "workflows": 5,
                    "forms": 8,
                    "apis": 18
                }
            },
            "restaurant_pos": {
                "key": "restaurant_pos",
                "name": "餐厅点餐系统",
                "description": "餐厅点餐和管理一体化系统",
                "category": TemplateCategory.HOSPITALITY,
                "difficulty": TemplateDifficulty.BEGINNER,
                "estimated_setup_time": "25分钟",
                "version": "1.0.0",
                "author": "AILF Team",
                "license": "MIT",
                "features": ["菜品管理", "订单管理", "桌台管理", "收银结算"],
                "tags": ["餐饮", "点餐", "收银"],
                "created_at": "2024-01-15T00:00:00.000Z",
                "updated_at": "2024-01-17T00:00:00.000Z",
                "preview": {
                    "entities": 5,
                    "workflows": 4,
                    "forms": 7,
                    "apis": 15
                }
            },
            "education_lms": {
                "key": "education_lms",
                "name": "在线学习管理系统",
                "description": "完整的在线教育平台模板",
                "category": TemplateCategory.EDUCATION,
                "difficulty": TemplateDifficulty.INTERMEDIATE,
                "estimated_setup_time": "50分钟",
                "version": "1.3.0",
                "author": "AILF Team",
                "license": "MIT",
                "features": ["课程管理", "学员管理", "考试系统", "成绩统计"],
                "tags": ["教育", "在线学习", "考试"],
                "created_at": "2024-01-16T00:00:00.000Z",
                "updated_at": "2024-01-20T00:00:00.000Z",
                "preview": {
                    "entities": 7,
                    "workflows": 6,
                    "forms": 10,
                    "apis": 22
                }
            },
            "logistics_wms": {
                "key": "logistics_wms",
                "name": "仓储管理系统",
                "description": "专业的仓储物流管理系统",
                "category": TemplateCategory.LOGISTICS,
                "difficulty": TemplateDifficulty.ADVANCED,
                "estimated_setup_time": "60分钟",
                "version": "2.0.0",
                "author": "AILF Team",
                "license": "MIT",
                "features": ["库存管理", "入出库管理", "货位管理", "配送管理"],
                "tags": ["物流", "仓储", "配送"],
                "created_at": "2024-01-14T00:00:00.000Z",
                "updated_at": "2024-01-21T00:00:00.000Z",
                "preview": {
                    "entities": 8,
                    "workflows": 7,
                    "forms": 12,
                    "apis": 25
                }
            }
        }
        
        # 分类映射
        self.category_names = {
            TemplateCategory.BUSINESS: "商业管理",
            TemplateCategory.HEALTHCARE: "医疗健康",
            TemplateCategory.EDUCATION: "教育培训",
            TemplateCategory.HOSPITALITY: "服务行业",
            TemplateCategory.FINANCE: "金融服务",
            TemplateCategory.LOGISTICS: "物流运输",
            TemplateCategory.GOVERNMENT: "政府机构",
            TemplateCategory.CUSTOM: "自定义"
        }
    
    def get_templates_list(self, category: Optional[str] = None, include_preview: bool = False) -> Dict[str, Any]:
        """
        获取模板列表
        
        Args:
            category: 分类筛选
            include_preview: 是否包含预览信息
            
        Returns:
            模板列表数据
        """
        try:
            # 筛选模板
            filtered_templates = []
            for template_data in self.templates.values():
                # 分类筛选
                if category and template_data["category"].value != category:
                    continue
                
                # 构建模板列表项
                template_item = TemplateListItem(
                    key=template_data["key"],
                    name=template_data["name"],
                    description=template_data["description"],
                    category=template_data["category"],
                    difficulty=template_data["difficulty"],
                    estimated_setup_time=template_data["estimated_setup_time"],
                    features=template_data["features"],
                    tags=template_data["tags"],
                    created_at=template_data["created_at"],
                    updated_at=template_data["updated_at"]
                )
                
                # 添加预览信息
                if include_preview and "preview" in template_data:
                    template_item.preview = TemplatePreview(**template_data["preview"])
                
                filtered_templates.append(template_item)
            
            # 统计分类
            categories = self._get_category_stats()
            
            return {
                "success": True,
                "data": {
                    "templates": filtered_templates,
                    "total": len(filtered_templates),
                    "categories": categories
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询模板列表失败: {str(e)}"
            }
    
    def get_template_detail(self, template_key: str) -> Dict[str, Any]:
        """
        获取模板详情
        
        Args:
            template_key: 模板标识符
            
        Returns:
            模板详情数据
        """
        if template_key not in self.templates:
            return {
                "success": False,
                "error": "template_not_found",
                "details": "指定的模板标识符不存在"
            }
        
        try:
            template_data = self.templates[template_key]
            
            # 构建详细配置（这里使用示例数据，实际应该从数据库获取）
            config = self._build_template_config(template_key)
            setup_guide = self._build_setup_guide(template_key)
            
            # 构建模板详情
            template_detail = TemplateDetail(
                key=template_data["key"],
                name=template_data["name"],
                description=template_data["description"],
                category=template_data["category"],
                difficulty=template_data["difficulty"],
                estimated_setup_time=template_data["estimated_setup_time"],
                version=template_data["version"],
                author=template_data["author"],
                license=template_data["license"],
                features=template_data["features"],
                tags=template_data["tags"],
                created_at=template_data["created_at"],
                updated_at=template_data["updated_at"],
                config=config,
                setup_guide=setup_guide
            )
            
            return {
                "success": True,
                "data": {"template": template_detail}
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询模板详情失败: {str(e)}"
            }
    
    def _get_category_stats(self) -> List[CategoryStats]:
        """获取分类统计"""
        category_counts = {}
        
        for template_data in self.templates.values():
            category = template_data["category"]
            category_counts[category] = category_counts.get(category, 0) + 1
        
        categories = []
        for category, count in category_counts.items():
            categories.append(CategoryStats(
                key=category.value,
                name=self.category_names[category],
                count=count
            ))
        
        return categories

    def _build_template_config(self, template_key: str) -> TemplateConfig:
        """构建模板配置（示例数据）"""
        if template_key == "ecommerce_basic":
            return TemplateConfig(
                scenario=ScenarioConfig(
                    type="ecommerce",
                    business_domain="电子商务",
                    target_users=["管理员", "销售人员", "客服"],
                    key_features=["商品管理", "订单处理", "用户管理", "库存管理"]
                ),
                entities=[
                    Entity(
                        name="product",
                        displayName="商品",
                        description="商品信息管理",
                        fields=[
                            EntityField(
                                name="name",
                                displayName="商品名称",
                                type="text",
                                required=True,
                                validation=FieldValidation(maxLength=100)
                            ),
                            EntityField(
                                name="price",
                                displayName="价格",
                                type="decimal",
                                required=True,
                                validation=FieldValidation(min=0, precision=2)
                            ),
                            EntityField(
                                name="stock",
                                displayName="库存",
                                type="number",
                                required=True,
                                defaultValue=0
                            ),
                            EntityField(
                                name="category",
                                displayName="分类",
                                type="select",
                                options=["电子产品", "服装", "食品", "图书"]
                            )
                        ]
                    ),
                    Entity(
                        name="order",
                        displayName="订单",
                        description="订单信息管理",
                        fields=[
                            EntityField(
                                name="order_no",
                                displayName="订单号",
                                type="text",
                                required=True,
                                unique=True
                            ),
                            EntityField(
                                name="customer_name",
                                displayName="客户姓名",
                                type="text",
                                required=True
                            ),
                            EntityField(
                                name="total_amount",
                                displayName="总金额",
                                type="decimal",
                                required=True
                            ),
                            EntityField(
                                name="status",
                                displayName="订单状态",
                                type="select",
                                options=["待付款", "已付款", "已发货", "已完成", "已取消"],
                                defaultValue="待付款"
                            )
                        ]
                    )
                ],
                workflows=[
                    Workflow(
                        name="订单处理流程",
                        description="从下单到完成的完整流程",
                        steps=[
                            WorkflowStep(name="创建订单", type="form", entity="order"),
                            WorkflowStep(name="支付确认", type="condition", condition="payment_status === 'success'"),
                            WorkflowStep(name="库存扣减", type="api_call", endpoint="/api/inventory/deduct"),
                            WorkflowStep(name="发货通知", type="notification", recipients=["customer", "warehouse"])
                        ]
                    )
                ],
                forms=[
                    Form(
                        name="商品录入表单",
                        entity="product",
                        layout="vertical",
                        sections=[
                            FormSection(title="基本信息", fields=["name", "category", "price", "stock"])
                        ]
                    ),
                    Form(
                        name="订单创建表单",
                        entity="order",
                        layout="horizontal",
                        sections=[
                            FormSection(title="订单信息", fields=["order_no", "customer_name", "total_amount"])
                        ]
                    )
                ],
                apis=[
                    API(path="/api/products", method="GET", description="获取商品列表"),
                    API(path="/api/products", method="POST", description="创建商品"),
                    API(path="/api/orders", method="GET", description="获取订单列表"),
                    API(path="/api/orders", method="POST", description="创建订单")
                ],
                roles=[
                    Role(name="管理员", code="admin", level=10, permissions=["*"]),
                    Role(name="销售人员", code="sales", level=5, permissions=["products:read", "orders:*"])
                ]
            )
        else:
            # 其他模板的简化配置
            return TemplateConfig(
                scenario=ScenarioConfig(
                    type="custom",
                    business_domain="通用业务",
                    target_users=["管理员"],
                    key_features=["基础管理"]
                ),
                entities=[],
                workflows=[],
                forms=[],
                apis=[],
                roles=[]
            )

    def _build_setup_guide(self, template_key: str) -> SetupGuide:
        """构建设置指南"""
        if template_key == "ecommerce_basic":
            return SetupGuide(
                steps=[
                    "1. 选择此模板创建场景",
                    "2. 根据需要调整商品分类",
                    "3. 配置支付接口",
                    "4. 设置用户角色权限",
                    "5. 生成并部署系统"
                ],
                prerequisites=[
                    "了解基本的电商业务流程",
                    "准备商品分类数据"
                ],
                tips=[
                    "可以根据实际需求添加更多商品字段",
                    "建议先配置少量测试数据进行验证"
                ]
            )
        else:
            return SetupGuide(
                steps=["1. 选择此模板创建场景", "2. 根据需要调整配置", "3. 生成并部署系统"],
                prerequisites=["了解基本业务流程"],
                tips=["建议先进行测试验证"]
            )


# 全局模板服务实例
template_service = TemplateService()
