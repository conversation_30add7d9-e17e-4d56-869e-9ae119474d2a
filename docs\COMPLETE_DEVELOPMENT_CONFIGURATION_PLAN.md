# AILF 完整开发配置规划文档

## 📋 概述

基于现有API文档，扩展AILF系统为通用业务场景配置平台，支持任意业务场景的零代码配置。

## 🏗️ 系统架构升级

### 核心设计理念升级
- **通用业务建模**：支持任意业务场景的实体和关系建模
- **可视化工作流**：支持复杂业务流程的可视化配置
- **动态表单生成**：根据业务实体自动生成表单界面
- **八步骤配置**：场景配置 → 实体建模 → 工作流设计 → 表单配置 → API配置 → 角色配置 → 权限设置 → 自动生成

### 技术栈扩展
- **前端新增**：React Flow (工作流设计)、Formily (动态表单)、AntV G6 (关系图)
- **后端新增**：工作流引擎、表单引擎、实体关系管理器
- **数据存储升级**：SQLite + JSON字段存储复杂数据结构

## 🎯 新增模块设计

### 1. 业务实体建模模块

#### 实体管理 API

##### POST /api/entities
创建业务实体定义。

**请求体：**
```json
{
  "name": "product",
  "displayName": "商品",
  "description": "电商系统商品实体",
  "icon": "shopping-cart",
  "fields": [
    {
      "name": "name",
      "displayName": "商品名称",
      "type": "text",
      "required": true,
      "unique": false,
      "validation": {
        "minLength": 1,
        "maxLength": 100
      }
    },
    {
      "name": "price",
      "displayName": "价格",
      "type": "decimal",
      "required": true,
      "validation": {
        "min": 0,
        "precision": 2
      }
    },
    {
      "name": "image",
      "displayName": "商品图片",
      "type": "image",
      "required": false,
      "validation": {
        "maxSize": "5MB",
        "formats": ["jpg", "png", "webp"]
      }
    },
    {
      "name": "category",
      "displayName": "商品分类",
      "type": "select",
      "required": true,
      "options": ["电子产品", "服装", "食品", "图书"]
    },
    {
      "name": "stock",
      "displayName": "库存数量",
      "type": "number",
      "required": true,
      "defaultValue": 0
    }
  ],
  "relationships": [
    {
      "name": "orders",
      "targetEntity": "order",
      "type": "one-to-many",
      "foreignKey": "product_id"
    }
  ]
}
```

**响应：**
```json
{
  "code": 201,
  "message": "实体创建成功",
  "data": {
    "entity": {
      "id": "entity_1705123456789",
      "name": "product",
      "displayName": "商品",
      "description": "电商系统商品实体",
      "fields": [...],
      "relationships": [...],
      "created_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

##### GET /api/entities
获取所有业务实体列表。

##### GET /api/entities/{entity_id}
获取特定实体详情。

##### PUT /api/entities/{entity_id}
更新实体定义。

##### DELETE /api/entities/{entity_id}
删除实体定义。

#### 实体数据管理 API

##### GET /api/entities/{entity_id}/data
获取实体数据列表。

**查询参数：**
- `page`: 页码
- `limit`: 每页数量
- `filter`: 过滤条件
- `sort`: 排序字段

##### POST /api/entities/{entity_id}/data
创建实体数据记录。

##### PUT /api/entities/{entity_id}/data/{record_id}
更新实体数据记录。

##### DELETE /api/entities/{entity_id}/data/{record_id}
删除实体数据记录。

### 2. 工作流设计模块

#### 工作流管理 API

##### POST /api/workflows
创建工作流定义。

**请求体：**
```json
{
  "name": "订单处理流程",
  "description": "电商订单从创建到完成的完整流程",
  "triggerType": "manual",
  "steps": [
    {
      "id": "step_start",
      "name": "开始",
      "type": "start",
      "position": {"x": 100, "y": 100},
      "nextSteps": ["step_create_order"]
    },
    {
      "id": "step_create_order",
      "name": "创建订单",
      "type": "form",
      "position": {"x": 300, "y": 100},
      "config": {
        "formConfig": {
          "entity": "order",
          "fields": ["customer_name", "products", "total_amount"],
          "layout": "vertical",
          "validation": [
            {
              "field": "total_amount",
              "rule": "required|min:0.01"
            }
          ]
        }
      },
      "nextSteps": ["step_payment"]
    },
    {
      "id": "step_payment",
      "name": "支付处理",
      "type": "api_call",
      "position": {"x": 500, "y": 100},
      "config": {
        "apiConfig": {
          "endpoint": "/api/payment/process",
          "method": "POST",
          "params": {
            "order_id": "{{step_create_order.order_id}}",
            "amount": "{{step_create_order.total_amount}}"
          }
        }
      },
      "nextSteps": ["step_condition_payment"]
    },
    {
      "id": "step_condition_payment",
      "name": "支付结果判断",
      "type": "condition",
      "position": {"x": 700, "y": 100},
      "config": {
        "conditionConfig": {
          "expression": "payment.status === 'success'",
          "branches": [
            {
              "condition": "true",
              "nextStep": "step_fulfill_order"
            },
            {
              "condition": "false",
              "nextStep": "step_payment_failed"
            }
          ]
        }
      }
    },
    {
      "id": "step_fulfill_order",
      "name": "订单履行",
      "type": "notification",
      "position": {"x": 900, "y": 50},
      "config": {
        "notificationConfig": {
          "recipients": ["warehouse_staff"],
          "template": "order_fulfillment",
          "data": {
            "order_id": "{{step_create_order.order_id}}"
          }
        }
      },
      "nextSteps": ["step_end"]
    },
    {
      "id": "step_payment_failed",
      "name": "支付失败处理",
      "type": "notification",
      "position": {"x": 900, "y": 150},
      "config": {
        "notificationConfig": {
          "recipients": ["customer"],
          "template": "payment_failed",
          "data": {
            "order_id": "{{step_create_order.order_id}}"
          }
        }
      },
      "nextSteps": ["step_end"]
    },
    {
      "id": "step_end",
      "name": "结束",
      "type": "end",
      "position": {"x": 1100, "y": 100}
    }
  ],
  "conditions": [
    {
      "id": "condition_vip_customer",
      "expression": "customer.type === 'vip'",
      "action": "show",
      "targetSteps": ["step_vip_discount"]
    }
  ]
}
```

##### GET /api/workflows
获取所有工作流列表。

##### GET /api/workflows/{workflow_id}
获取特定工作流详情。

##### PUT /api/workflows/{workflow_id}
更新工作流定义。

##### DELETE /api/workflows/{workflow_id}
删除工作流定义。

#### 工作流执行 API

##### POST /api/workflows/{workflow_id}/execute
执行工作流。

**请求体：**
```json
{
  "input_data": {
    "customer_id": "customer_123",
    "products": [
      {"id": "product_1", "quantity": 2},
      {"id": "product_2", "quantity": 1}
    ]
  },
  "context": {
    "user_id": "user_456",
    "session_id": "session_789"
  }
}
```

##### GET /api/workflows/instances
获取工作流实例列表。

##### GET /api/workflows/instances/{instance_id}
获取工作流实例详情。

##### POST /api/workflows/instances/{instance_id}/continue
继续执行工作流实例。

### 3. 动态表单配置模块

#### 表单管理 API

##### POST /api/forms
创建动态表单配置。

**请求体：**
```json
{
  "name": "商品录入表单",
  "entity": "product",
  "layout": {
    "type": "grid",
    "columns": 2,
    "spacing": 16
  },
  "sections": [
    {
      "id": "section_basic",
      "title": "基本信息",
      "collapsible": false,
      "fields": [
        {
          "id": "field_name",
          "entityField": "name",
          "displayType": "input",
          "label": "商品名称",
          "placeholder": "请输入商品名称",
          "required": true,
          "validation": {
            "rules": ["required", "max:100"],
            "messages": {
              "required": "商品名称不能为空",
              "max": "商品名称不能超过100个字符"
            }
          }
        },
        {
          "id": "field_category",
          "entityField": "category",
          "displayType": "select",
          "label": "商品分类",
          "required": true,
          "dataSource": {
            "type": "static",
            "options": [
              {"value": "electronics", "label": "电子产品"},
              {"value": "clothing", "label": "服装"},
              {"value": "food", "label": "食品"}
            ]
          }
        }
      ]
    },
    {
      "id": "section_pricing",
      "title": "价格信息",
      "collapsible": true,
      "fields": [
        {
          "id": "field_price",
          "entityField": "price",
          "displayType": "input",
          "label": "商品价格",
          "placeholder": "0.00",
          "required": true,
          "validation": {
            "rules": ["required", "numeric", "min:0.01"],
            "messages": {
              "required": "价格不能为空",
              "numeric": "价格必须是数字",
              "min": "价格必须大于0"
            }
          }
        }
      ]
    }
  ],
  "permissions": [
    {
      "role": "admin",
      "actions": ["create", "read", "update", "delete"]
    },
    {
      "role": "staff",
      "actions": ["create", "read", "update"]
    }
  ]
}
```

##### GET /api/forms
获取所有表单配置。

##### GET /api/forms/{form_id}
获取特定表单配置。

##### PUT /api/forms/{form_id}
更新表单配置。

##### DELETE /api/forms/{form_id}
删除表单配置。

#### 表单渲染 API

##### GET /api/forms/{form_id}/render
获取表单渲染配置。

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "schema": {
      "type": "form",
      "title": "商品录入表单",
      "body": [
        {
          "type": "fieldset",
          "title": "基本信息",
          "body": [
            {
              "type": "input-text",
              "name": "name",
              "label": "商品名称",
              "placeholder": "请输入商品名称",
              "required": true,
              "validations": {
                "isLength": {
                  "args": [1, 100],
                  "message": "商品名称长度必须在1-100个字符之间"
                }
              }
            },
            {
              "type": "select",
              "name": "category",
              "label": "商品分类",
              "required": true,
              "options": [
                {"label": "电子产品", "value": "electronics"},
                {"label": "服装", "value": "clothing"},
                {"label": "食品", "value": "food"}
              ]
            }
          ]
        }
      ]
    }
  }
}
```

## 🔄 现有API模块扩展

### 场景管理模块扩展

#### POST /api/scenario (扩展)
扩展场景配置，支持实体、工作流和表单配置。

**请求体扩展：**
```json
{
  "id": "scenario_1705123456789",
  "name": "电商系统",
  "type": "ecommerce",
  "description": "电子商务管理系统",
  "entities": [
    {
      "id": "entity_product",
      "name": "product",
      "displayName": "商品",
      "fields": [...]
    }
  ],
  "workflows": [
    {
      "id": "workflow_order_process",
      "name": "订单处理流程",
      "steps": [...]
    }
  ],
  "forms": [
    {
      "id": "form_product_create",
      "name": "商品创建表单",
      "entity": "product"
    }
  ],
  "apis": [...],
  "roles": [...],
  "permissions": [...]
}
```

### 代码生成模块扩展

#### POST /api/generate-entities
根据实体配置生成数据库表和模型代码。

#### POST /api/generate-workflows
根据工作流配置生成工作流执行代码。

#### POST /api/generate-forms
根据表单配置生成前端表单组件。

#### POST /api/generate-complete
一键生成完整系统代码。

**响应：**
```json
{
  "code": 200,
  "message": "完整系统生成成功",
  "data": {
    "entities": {
      "tables": ["products", "orders", "customers"],
      "models": ["Product.py", "Order.py", "Customer.py"],
      "total": 3
    },
    "workflows": {
      "engines": ["OrderProcessWorkflow.py"],
      "total": 1
    },
    "forms": {
      "components": ["ProductForm.tsx", "OrderForm.tsx"],
      "total": 2
    },
    "apis": {
      "routes": ["products.py", "orders.py"],
      "total": 2
    },
    "frontend": {
      "pages": ["ProductList.tsx", "OrderManagement.tsx"],
      "total": 2
    }
  }
}
```

## 📊 数据模型扩展

### BusinessEntity (业务实体)
```typescript
interface BusinessEntity {
  id: string;
  name: string;                    // 实体名称 (product, order, customer)
  displayName: string;             // 显示名称 (商品, 订单, 客户)
  description: string;             // 实体描述
  icon: string;                    // 图标
  fields: EntityField[];           // 字段列表
  relationships: EntityRelationship[]; // 关系列表
  permissions: EntityPermission[]; // 实体权限
  created_at: string;
  updated_at: string;
}
```

### EntityField (实体字段)
```typescript
interface EntityField {
  id: string;
  name: string;                    // 字段名
  displayName: string;             // 显示名称
  type: FieldType;                 // 字段类型
  required: boolean;               // 是否必填
  unique: boolean;                 // 是否唯一
  defaultValue?: any;              // 默认值
  validation?: FieldValidation;    // 验证规则
  options?: string[];              // 选项列表 (select类型)
  reference?: EntityReference;     // 引用配置 (reference类型)
}
```

### WorkflowDefinition (工作流定义)
```typescript
interface WorkflowDefinition {
  id: string;
  name: string;                    // 工作流名称
  description: string;             // 工作流描述
  triggerType: TriggerType;        // 触发类型
  steps: WorkflowStep[];           // 步骤列表
  conditions: WorkflowCondition[]; // 条件列表
  variables: WorkflowVariable[];   // 变量列表
  created_at: string;
  updated_at: string;
}
```

### DynamicForm (动态表单)
```typescript
interface DynamicForm {
  id: string;
  name: string;                    // 表单名称
  entity: string;                  // 关联实体
  layout: FormLayout;              // 表单布局
  sections: FormSection[];         // 表单分组
  validation: FormValidation;      // 表单验证
  permissions: FormPermission[];   // 表单权限
  created_at: string;
  updated_at: string;
}
```

## 🎯 开发者配置界面升级

### 八步骤配置流程
```
1. 场景配置     (已有) - 基本信息配置
2. 实体建模     (新增) - 业务数据结构设计
3. 工作流设计   (新增) - 业务流程可视化设计
4. 表单配置     (新增) - 动态表单界面设计
5. API配置      (已有) - API接口配置
6. 角色配置     (已有) - 用户角色管理
7. 权限设置     (已有) - 权限矩阵配置
8. 代码生成     (扩展) - 完整系统生成
```

### 新增前端组件架构
```
EntityModeler/
├── EntityCanvas.tsx         # 实体关系图画布
├── EntityEditor.tsx         # 实体编辑器
├── FieldEditor.tsx          # 字段编辑器
├── RelationshipEditor.tsx   # 关系编辑器
└── EntityPreview.tsx        # 实体预览

WorkflowDesigner/
├── WorkflowCanvas.tsx       # 工作流画布 (React Flow)
├── StepPalette.tsx         # 步骤工具箱
├── StepEditor.tsx          # 步骤编辑器
├── ConditionEditor.tsx     # 条件编辑器
└── WorkflowSimulator.tsx   # 工作流模拟器

FormDesigner/
├── FormCanvas.tsx          # 表单设计画布
├── FieldPalette.tsx        # 字段工具箱
├── FieldEditor.tsx         # 字段编辑器
├── LayoutEditor.tsx        # 布局编辑器
└── FormPreview.tsx         # 表单预览
```

## 🚀 实现路线图

### Phase 1: 实体建模系统 (2周)
- [ ] 实体管理API开发
- [ ] 实体建模器前端组件
- [ ] 字段类型支持
- [ ] 关系管理功能

### Phase 2: 工作流引擎 (3周)
- [ ] 工作流定义API
- [ ] 工作流执行引擎
- [ ] 可视化工作流设计器
- [ ] 工作流实例管理

### Phase 3: 动态表单系统 (2周)
- [ ] 表单配置API
- [ ] 动态表单渲染引擎
- [ ] 表单设计器界面
- [ ] 表单验证系统

### Phase 4: 代码生成增强 (2周)
- [ ] 实体代码生成
- [ ] 工作流代码生成
- [ ] 表单代码生成
- [ ] 完整前端页面生成

### Phase 5: 系统集成测试 (1周)
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 文档完善
- [ ] 部署优化

## 📝 总结

通过这个完整的配置规划，AILF将从一个基础的API管理工具升级为真正的**通用业务系统生成平台**，支持：

1. **任意业务场景** - 不再局限于预设模板
2. **零代码配置** - 开发者通过可视化界面完成所有配置
3. **完整系统生成** - 自动生成前后端完整代码
4. **工作流驱动** - 支持复杂业务流程
5. **动态界面** - 根据配置自动生成用户界面

这将使AILF成为一个真正强大的企业级低代码开发平台。
