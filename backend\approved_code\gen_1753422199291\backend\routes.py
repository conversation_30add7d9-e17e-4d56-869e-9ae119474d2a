```python
from fastapi import FastAPI, HTTPException, Depends, status
from pydantic import BaseModel, Field
from typing import List, Optional
from decimal import Decimal
import uvicorn

app = FastAPI(
    title="商品管理系统API",
    description="这是一个商品管理系统的RESTful API，提供商品的CRUD操作",
    version="1.0.0"
)

# 数据模型定义
class ProductBase(BaseModel):
    name: str = Field(..., title="商品名称", description="商品的名称", example="苹果")
    price: Decimal = Field(..., title="价格", description="商品的价格", example=5.99, gt=0)
    category: str = Field(..., title="分类", description="商品的分类", example="水果")

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = Field(None, title="商品名称", description="商品的名称", example="苹果")
    price: Optional[Decimal] = Field(None, title="价格", description="商品的价格", example=5.99, gt=0)
    category: Optional[str] = Field(None, title="分类", description="商品的分类", example="水果")

class Product(ProductBase):
    id: int = Field(..., title="商品ID", description="商品的唯一标识符", example=1)
    
    class Config:
        orm_mode = True

# 模拟数据库存储
products_db = [
    {"id": 1, "name": "苹果", "price": 5.99, "category": "水果"},
    {"id": 2, "name": "香蕉", "price": 2.99, "category": "水果"},
    {"id": 3, "name": "牛奶", "price": 3.49, "category": "乳制品"}
]
next_id = 4

# 权限依赖
def verify_api_key(x_api_key: str = Header(...)):
    if x_api_key != "secret-key":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key",
        )
    return x_api_key

# API路由实现
@app.get(
    "/api/products",
    response_model=List[Product],
    summary="获取商品列表",
    description="获取所有商品的信息列表"
)
async def get_products(
    category: Optional[str] = None,
    min_price: Optional[Decimal] = None,
    max_price: Optional[Decimal] = None
):
    """
    获取商品列表:
    
    - **category**: 可选，按分类筛选商品
    - **min_price**: 可选，筛选价格不低于此值的商品
    - **max_price**: 可选，筛选价格不高于此值的商品
    
    返回:
    - 商品列表
    """
    filtered_products = products_db
    
    if category:
        filtered_products = [p for p in filtered_products if p["category"] == category]
    
    if min_price is not None:
        filtered_products = [p for p in filtered_products if p["price"] >= min_price]
    
    if max_price is not None:
        filtered_products = [p for p in filtered_products if p["price"] <= max_price]
    
    return filtered_products

@app.get(
    "/api/products/{product_id}",
    response_model=Product,
    summary="获取商品详情",
    description="根据商品ID获取单个商品的详细信息"
)
async def get_product(product_id: int):
    """
    获取商品详情:
    
    - **product_id**: 商品的唯一标识符
    
    返回:
    - 商品详细信息
    
    异常:
    - 404: 商品未找到
    """
    product = next((p for p in products_db if p["id"] == product_id), None)
    if product is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    return product

@app.post(
    "/api/products",
    response_model=Product,
    status_code=status.HTTP_201_CREATED,
    summary="创建商品",
    description="创建一个新的商品"
)
async def create_product(product: ProductCreate):
    """
    创建商品:
    
    请求体:
    - **name**: 商品名称
    - **price**: 商品价格
    - **category**: 商品分类
    
    返回:
    - 创建的商品信息
    
    异常:
    - 422: 请求参数验证失败
    """
    global next_id
    new_product = {
        "id": next_id,
        "name": product.name,
        "price": product.price,
        "category": product.category
    }
    products_db.append(new_product)
    next_id += 1
    return new_product

@app.put(
    "/api/products/{product_id}",
    response_model=Product,
    summary="更新商品",
    description="根据商品ID更新商品信息"
)
async def update_product(product_id: int, product: ProductUpdate):
    """
    更新商品:
    
    - **product_id**: 商品的唯一标识符
    
    请求体:
    - **name**: 可选，商品名称
    - **price**: 可选，商品价格
    - **category**: 可选，商品分类
    
    返回:
    - 更新后的商品信息
    
    异常:
    - 404: 商品未找到
    - 422: 请求参数验证失败
    """
    existing_product = next((p for p in products_db if p["id"] == product_id), None)
    if existing_product is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    # 更新字段
    update_data = product.dict(exclude_unset=True)
    for field, value in update_data.items():
        existing_product[field] = value
    
    return existing_product

@app.delete(
    "/api/products/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除商品",
    description="根据商品ID删除商品"
)
async def delete_product(product_id: int):
    """
    删除商品:
    
    - **product_id**: 商品的唯一标识符
    
    返回:
    - 204: 删除成功，无返回内容
    
    异常:
    - 404: 商品未找到
    """
    global products_db
    product_index = next((i for i, p in enumerate(products_db) if p["id"] == product_id), None)
    if product_index is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    products_db.pop(product_index)
    return

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```