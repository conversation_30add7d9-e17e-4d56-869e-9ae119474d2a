{"name": "火车票预订流程", "description": "用户购买火车票时AI应该遵循的标准流程", "business_scenario": "train_ticket_booking", "user_intents": ["我要买火车票", "帮我订火车票", "查询火车票", "预订车票"], "trigger_keywords": ["火车票", "车票", "订票", "买票"], "inputs": [{"name": "departure_city", "type": "string", "description": "出发城市", "required": true, "ai_prompt": "请问您从哪个城市出发？"}, {"name": "destination_city", "type": "string", "description": "目的地城市", "required": true, "ai_prompt": "请问您要到哪个城市？"}, {"name": "travel_date", "type": "date", "description": "出行日期", "required": true, "ai_prompt": "请问您计划什么时候出行？"}], "nodes": [{"id": "start", "name": "开始", "description": "工作流开始节点", "type": "start", "position": {"x": 100, "y": 100}, "parent_nodes": [], "child_nodes": [{"node_id": "collect_info", "condition": {"type": "always", "description": "总是执行信息收集"}}], "config": {}, "outputs": [], "ai_instructions": {"purpose": "初始化火车票预订流程", "action": "开始与用户的对话，了解预订需求", "user_message": "好的，我来帮您预订火车票。", "success_handling": "进入信息收集阶段", "error_handling": "如果初始化失败，请重试"}}, {"id": "collect_info", "name": "收集出行信息", "description": "收集用户的出行信息", "type": "user_input", "position": {"x": 300, "y": 100}, "parent_nodes": ["start"], "child_nodes": [{"node_id": "search_trains", "condition": {"type": "form_completed", "description": "用户完成信息填写后"}}], "config": {"input_type": "form", "form_schema": {"departure_city": {"type": "string", "required": true, "prompt": "请输入出发城市"}, "destination_city": {"type": "string", "required": true, "prompt": "请输入目的地城市"}, "travel_date": {"type": "date", "required": true, "prompt": "请选择出行日期"}}}, "outputs": [{"name": "travel_info", "type": "object", "description": "出行信息"}], "ai_instructions": {"purpose": "收集用户的出行信息", "action": "引导用户填写出发地、目的地和出行日期", "user_message": "请提供您的出行信息", "validation": "确保所有必填信息都已填写且格式正确"}}, {"id": "search_trains", "name": "查询车次", "description": "根据出发地、目的地和日期查询可用车次", "type": "api_call", "position": {"x": 500, "y": 100}, "parent_nodes": ["collect_info"], "child_nodes": [{"node_id": "check_results", "condition": {"type": "always", "description": "总是执行结果检查"}}], "config": {"api_id": "train_search_api", "api_endpoint": "/api/trains/search", "method": "GET", "parameters": {"from": "{{inputs.departure_city}}", "to": "{{inputs.destination_city}}", "date": "{{inputs.travel_date}}"}}, "outputs": [{"name": "trains", "type": "array", "description": "可用车次列表"}, {"name": "total_count", "type": "integer", "description": "总车次数量"}], "ai_instructions": {"purpose": "查询符合用户需求的火车车次信息", "action": "调用火车票查询API获取可用车次", "user_message": "正在为您查询从{{inputs.departure_city}}到{{inputs.destination_city}}的车次...", "success_handling": "将查询结果传递给检查节点", "error_handling": "如果查询失败，告知用户并建议重试"}}, {"id": "check_results", "name": "检查查询结果", "description": "判断是否找到可用的火车车次", "type": "condition", "position": {"x": 700, "y": 100}, "parent_nodes": ["search_trains"], "child_nodes": [{"node_id": "show_train_list", "condition": {"type": "expression", "expression": "{{nodes.search_trains.outputs.total_count}} > 0", "description": "找到可用车次时"}}, {"node_id": "no_trains_found", "condition": {"type": "expression", "expression": "{{nodes.search_trains.outputs.total_count}} == 0", "description": "没有找到车次时"}}], "config": {"evaluation_expression": "{{nodes.search_trains.outputs.total_count}} > 0"}, "outputs": [], "ai_instructions": {"purpose": "判断是否找到可用的火车车次", "action": "检查搜索结果数量，决定后续流程", "true_path": "如果有车次，进入车次选择流程", "false_path": "如果无车次，提示用户并提供建议"}}, {"id": "show_train_list", "name": "显示车次列表", "description": "向用户展示可用的车次列表", "type": "notification", "position": {"x": 600, "y": 200}, "parent_nodes": ["check_results"], "child_nodes": [{"node_id": "end", "condition": {"type": "always", "description": "显示完成后结束"}}], "config": {"message_template": "为您找到{{nodes.search_trains.outputs.total_count}}个车次选择", "recipients": ["user"]}, "outputs": [], "ai_instructions": {"purpose": "向用户展示查询到的车次信息", "action": "格式化显示车次列表，包括时间、价格等信息", "user_message": "以下是为您找到的车次选择："}}, {"id": "no_trains_found", "name": "无车次提示", "description": "提示用户没有找到合适的车次", "type": "notification", "position": {"x": 800, "y": 200}, "parent_nodes": ["check_results"], "child_nodes": [{"node_id": "end", "condition": {"type": "always", "description": "提示完成后结束"}}], "config": {"message_template": "抱歉，没有找到从{{inputs.departure_city}}到{{inputs.destination_city}}在{{inputs.travel_date}}的车次", "recipients": ["user"]}, "outputs": [], "ai_instructions": {"purpose": "告知用户没有找到合适的车次", "action": "提供友好的提示信息和建议", "user_message": "抱歉，没有找到符合条件的车次。建议您：1. 调整出行日期 2. 选择其他交通方式"}}, {"id": "end", "name": "结束", "description": "工作流结束节点", "type": "end", "position": {"x": 700, "y": 300}, "parent_nodes": ["show_train_list", "no_trains_found"], "child_nodes": [], "config": {}, "outputs": [], "ai_instructions": {"purpose": "结束火车票查询流程", "action": "总结查询结果，清理状态", "user_message": "火车票查询完成！如需其他帮助，请随时告诉我。"}}], "metadata": {"tags": ["transportation", "booking", "train"], "complexity": "medium"}}