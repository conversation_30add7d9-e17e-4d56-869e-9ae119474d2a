# AILF - AI-Driven Low-Code Frontend Generator (路由模式 v2.0)

AILF是基于AI的智能前端生成系统，通过自然语言交互动态创建用户界面。企业开发者配置业务场景和API，最终用户通过语音或文字描述需求，AI自动生成对应的amis界面。

## 🆕 v2.0 新特性

- ✅ **React Router路由模式** - 现代化的页面路由管理
- ✅ **pnpm包管理器** - 更快的依赖安装和磁盘空间优化
- ✅ **页面组件化架构** - 遵循单一文件规则的清晰结构
- ✅ **全屏About页面** - Apple风格白色浅色调设计
- ✅ **工作区管理** - 统一的前后端项目管理

## 🎯 核心特性

- **无固定前端**: AI根据用户需求动态生成界面，无需预设UI
- **开发者配置**: 支持配置业务场景、API接口、权限规则
- **智能生成**: 基于qwen-coder-plus模型的amis Schema生成
- **Siri风格交互**: 优雅的语音交互界面
- **通用框架**: 适配任意业务场景（高铁票务、售货机、管理系统等）
- **权限管理**: 完整的用户认证和权限控制系统

## 📁 项目结构

```
AILF/
├── frontend/          # React前端应用
│   ├── src/          # 源代码
│   │   ├── pages/    # 页面组件 (路由模式)
│   │   │   ├── MainPage.tsx    # 主页面
│   │   │   ├── AboutPage.tsx   # 关于页面
│   │   │   ├── ConfigPage.tsx  # 配置页面
│   │   │   └── ErrorPage.tsx   # 错误页面
│   │   ├── router/   # 路由配置
│   │   │   └── index.tsx       # 路由定义
│   │   ├── components/ # 可复用组件
│   │   ├── hooks/    # 自定义Hooks
│   │   └── styles/   # 样式文件
│   ├── public/       # 静态资源
│   ├── package.json  # 前端依赖 (使用pnpm)
│   └── tsconfig.json # TypeScript配置
├── backend/          # FastAPI后端应用
│   ├── api/         # API路由
│   ├── core/        # 核心配置
│   ├── services/    # 业务服务
│   ├── utils/       # 工具函数
│   ├── main.py      # 主应用入口
│   ├── simple_main.py # 简化版启动文件
│   └── requirements.txt # 后端依赖
├── docs/            # 项目文档
├── start-frontend.bat # 前端启动脚本
├── start-backend.bat  # 后端启动脚本
├── start-all.bat     # 一键启动脚本
└── README.md        # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- pnpm >= 8.0.0 (推荐使用pnpm包管理器)

### 安装pnpm (如果还没有安装)
```bash
npm install -g pnpm
```

### 方式一：使用pnpm工作区（推荐）
```bash
# 安装所有依赖
pnpm install

# 启动前端开发服务器
pnpm dev:frontend

# 启动后端开发服务器 (需要Python环境)
pnpm dev:backend

# 同时启动前后端
pnpm dev
```

### 方式二：一键启动脚本
```bash
# Windows
start-all.bat
```

### 方式三：分别启动

#### 启动后端服务器
```bash
# Windows
start-backend.bat

# 或手动启动
cd backend
python simple_main.py
```

#### 启动前端服务器
```bash
# Windows  
start-frontend.bat

# 或手动启动
cd frontend
npm install  # 首次运行需要安装依赖
npm start
```

### 访问应用
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:5000
- **API文档**: http://localhost:5000/docs

## 🛠️ 技术栈

### 前端
- **React 18** + **TypeScript**
- **amis** - 低代码前端框架
- **Web Speech API** - 语音识别
- **Axios** - HTTP客户端

### 后端
- **FastAPI** - 现代Python Web框架
- **qwen-coder-plus** - 阿里云百炼AI模型
- **Pydantic** - 数据验证
- **Uvicorn** - ASGI服务器

## 🎮 使用方法

1. **启动系统**: 运行 `start-all.bat` 或分别启动前后端
2. **打开界面**: 访问 http://localhost:3000
3. **语音交互**: 点击麦克风按钮，说出需求
4. **文字输入**: 或点击测试按钮体验功能
5. **查看结果**: AI将生成对应的amis界面

## 📖 示例

### 语音输入示例
- "创建一个用户管理表格"
- "生成一个登录表单"
- "做一个数据统计图表"

### 生成结果
系统会自动生成包含以下功能的完整界面：
- 数据表格（CRUD操作）
- 表单验证
- 对话框交互
- 按钮操作

## 🔧 开发指南

### 前端开发
```bash
cd frontend
npm install
npm start
```

### 后端开发
```bash
cd backend
pip install -r requirements.txt
python simple_main.py
```

### API测试
访问 http://localhost:5000/docs 查看API文档

## 🌐 路由说明

### 页面路由
- `/` - 主页面 (语音交互界面)
- `/about` - 关于页面 (全屏白色浅色调设计)
- `/config` - 开发者配置页面
- `/*` - 404错误页面 (自动重定向到主页)

### 路由特性
- **声明式路由** - 使用React Router 6的现代化路由
- **错误边界** - 自动处理路由错误
- **导航守卫** - 支持权限控制和重定向
- **浏览器历史** - 支持前进后退按钮

## 📦 pnpm使用指南

### 常用命令
```bash
# 安装依赖
pnpm install

# 添加依赖到特定工作区
pnpm --filter frontend add react-router-dom

# 运行脚本
pnpm --filter frontend start

# 并行运行所有工作区的脚本
pnpm run --parallel dev

# 递归运行所有工作区的脚本
pnpm run --recursive build
```

### 工作区管理
项目使用pnpm工作区来管理前后端依赖：
```yaml
# pnpm-workspace.yaml
packages:
  - 'frontend'
  - 'backend'
```

## 📚 核心文档

- `BACKEND_WORKFLOW.md` - 后端工作流程设计
- `TECHNICAL_DESIGN.md` - 技术架构设计
- `PROJECT_STRUCTURE.md` - 项目结构说明

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证

## 🎉 致谢

感谢以下开源项目：
- [amis](https://github.com/baidu/amis) - 百度低代码前端框架
- [FastAPI](https://github.com/tiangolo/fastapi) - 现代Python Web框架
- [React](https://github.com/facebook/react) - 用户界面库
