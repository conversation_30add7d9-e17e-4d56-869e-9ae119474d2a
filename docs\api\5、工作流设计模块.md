# 工作流设计模块 API 文档 (AI指导版)

## 📋 概述

工作流设计模块提供业务流程的图结构化定义，专门为AI提供业务流程指导。当用户提出需求时（如"我要买火车票"），AI可以查询相应的工作流定义，了解应该遵循什么步骤、每个步骤调用什么API、如何处理用户交互。

### 🎯 核心目标

1. **AI流程指导** - 为AI提供标准化的业务流程执行指南
2. **API调用指导** - 明确告诉AI每个步骤需要调用哪个API
3. **用户交互指导** - 指导AI如何与用户进行交互和数据收集
4. **图结构设计** - 支持复杂业务流程的可视化建模，包含父子节点关系和条件判断

**注意：工作流本身不执行，只是为AI提供流程指导和API调用指南。**

---

## 🔧 核心概念

### 1. 工作流 (Workflow)
- **定义**: 一个业务场景的标准处理流程指南
- **用途**: 告诉AI如何处理特定类型的用户请求
- **结构**: 由节点(nodes)组成的有向图，节点间通过父子关系和条件连接
- **作用**: AI的"操作手册"，不是执行引擎

### 2. 节点 (Node)
- **定义**: 流程中的一个步骤，告诉AI要做什么
- **类型**: start、api_call、user_input、condition、notification、end
- **关系**: 每个节点可以有多个父节点和子节点
- **条件**: 从当前节点到子节点的转换条件
- **配置**: 包含AI执行该步骤所需的所有信息

### 3. 节点关系 (Node Relationship)
- **父节点**: 当前节点的前置步骤
- **子节点**: 当前节点的后续步骤
- **转换条件**: 从当前节点到子节点的判断条件
- **分支逻辑**: 支持条件分支、并行处理、循环等

### 4. API映射 (API Mapping)
- **定义**: 将业务步骤映射到具体的API调用
- **作用**: 告诉AI每个步骤应该调用系统中的哪个API
- **集成**: 与API路由管理模块紧密集成

---

## 🔄 API 端点列表

### 工作流管理 (5个端点)
- **POST** `/api/workflows` - 创建工作流
- **GET** `/api/workflows` - 获取工作流列表
- **GET** `/api/workflows/{workflow_id}` - 获取工作流详情
- **PUT** `/api/workflows/{workflow_id}` - 更新工作流
- **DELETE** `/api/workflows/{workflow_id}` - 删除工作流

### 节点管理 (4个端点)
- **GET** `/api/workflows/{workflow_id}/nodes` - 获取工作流节点列表
- **POST** `/api/workflows/{workflow_id}/nodes` - 添加节点
- **PUT** `/api/workflows/{workflow_id}/nodes/{node_id}` - 更新节点
- **DELETE** `/api/workflows/{workflow_id}/nodes/{node_id}` - 删除节点

---

## 📊 数据结构定义

### 工作流定义 (Workflow Definition)

```json
{
  "id": "workflow_train_booking",
  "name": "火车票预订流程",
  "description": "用户购买火车票时AI应该遵循的标准流程",
  "business_scenario": "train_ticket_booking",
  "user_intents": [
    "我要买火车票",
    "帮我订火车票",
    "查询火车票",
    "预订车票"
  ],
  "trigger_keywords": ["火车票", "车票", "订票", "买票"],
  "inputs": [
    {
      "name": "departure_city",
      "type": "string",
      "description": "出发城市",
      "required": true,
      "ai_prompt": "请问您从哪个城市出发？"
    },
    {
      "name": "destination_city",
      "type": "string",
      "description": "目的地城市",
      "required": true,
      "ai_prompt": "请问您要到哪个城市？"
    },
    {
      "name": "travel_date",
      "type": "date",
      "description": "出行日期",
      "required": true,
      "ai_prompt": "请问您计划什么时候出行？"
    }
  ],
  "nodes": [...],
  "metadata": {
    "created_by": "developer_001",
    "created_at": "2024-01-20T10:00:00Z",
    "tags": ["transportation", "booking"],
    "complexity": "medium"
  }
}
```

### 节点定义 (Node Definition)

```json
{
  "id": "search_trains",
  "name": "查询车次",
  "description": "根据出发地、目的地和日期查询可用车次",
  "type": "api_call",
  "position": {
    "x": 200,
    "y": 100
  },
  "parent_nodes": ["collect_info"],
  "child_nodes": [
    {
      "node_id": "check_results",
      "condition": {
        "type": "always",
        "description": "总是执行结果检查"
      }
    }
  ],
  "config": {
    "api_id": "train_search_api",
    "api_endpoint": "/api/trains/search",
    "method": "GET",
    "parameters": {
      "from": "{{inputs.departure_city}}",
      "to": "{{inputs.destination_city}}",
      "date": "{{inputs.travel_date}}"
    }
  },
  "outputs": [
    {
      "name": "trains",
      "type": "array",
      "description": "可用车次列表"
    },
    {
      "name": "total_count",
      "type": "integer",
      "description": "总车次数量"
    }
  ],
  "ai_instructions": {
    "purpose": "查询符合用户需求的火车车次信息",
    "action": "调用火车票查询API获取可用车次",
    "user_message": "正在为您查询从{{inputs.departure_city}}到{{inputs.destination_city}}的车次...",
    "success_handling": "将查询结果传递给检查节点",
    "error_handling": "如果查询失败，告知用户并建议重试"
  }
}
```

### 条件节点示例

```json
{
  "id": "check_results",
  "name": "检查查询结果",
  "type": "condition",
  "parent_nodes": ["search_trains"],
  "child_nodes": [
    {
      "node_id": "show_train_list",
      "condition": {
        "type": "expression",
        "expression": "{{nodes.search_trains.outputs.total_count}} > 0",
        "description": "找到可用车次时"
      }
    },
    {
      "node_id": "no_trains_found",
      "condition": {
        "type": "expression",
        "expression": "{{nodes.search_trains.outputs.total_count}} == 0",
        "description": "没有找到车次时"
      }
    }
  ],
  "config": {
    "evaluation_expression": "{{nodes.search_trains.outputs.total_count}} > 0"
  },
  "ai_instructions": {
    "purpose": "判断是否找到可用的火车车次",
    "action": "检查搜索结果数量，决定后续流程",
    "true_path": "如果有车次，进入车次选择流程",
    "false_path": "如果无车次，提示用户并提供建议"
  }
}
```

### 用户输入节点示例

```json
{
  "id": "collect_passenger_info",
  "name": "收集乘客信息",
  "type": "user_input",
  "parent_nodes": ["show_train_list"],
  "child_nodes": [
    {
      "node_id": "create_order",
      "condition": {
        "type": "form_completed",
        "description": "用户完成信息填写后"
      }
    }
  ],
  "config": {
    "input_type": "form",
    "form_schema": {
      "passenger_name": {
        "type": "string",
        "required": true,
        "prompt": "请输入乘客姓名"
      },
      "id_number": {
        "type": "string",
        "required": true,
        "pattern": "^[1-9]\\d{17}$",
        "prompt": "请输入身份证号"
      },
      "phone": {
        "type": "string",
        "required": true,
        "pattern": "^1[3-9]\\d{9}$",
        "prompt": "请输入手机号"
      }
    }
  },
  "outputs": [
    {
      "name": "passenger_data",
      "type": "object",
      "description": "乘客信息"
    }
  ],
  "ai_instructions": {
    "purpose": "收集乘客信息用于订票",
    "action": "引导用户填写必要的乘客信息",
    "user_message": "请提供乘客信息以完成订票",
    "validation": "确保所有信息格式正确且完整"
  }
}
```

---

## 📚 API 详细文档

### 1. 创建工作流

**POST** `/api/workflows`

#### 描述
创建新的工作流定义，包括节点配置、父子关系和AI执行指令。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "description": "string",
  "business_scenario": "string",
  "user_intents": ["string"],
  "trigger_keywords": ["string"],
  "inputs": [
    {
      "name": "string",
      "type": "string|number|boolean|date|array|object",
      "description": "string",
      "required": boolean,
      "ai_prompt": "string"
    }
  ],
  "nodes": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "start|api_call|user_input|condition|notification|end",
      "parent_nodes": ["string"],
      "child_nodes": [
        {
          "node_id": "string",
          "condition": {
            "type": "always|expression|form_completed",
            "expression": "string",
            "description": "string"
          }
        }
      ],
      "config": {},
      "outputs": [],
      "ai_instructions": {}
    }
  ],
  "metadata": {
    "tags": ["string"],
    "complexity": "low|medium|high"
  }
}
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "工作流创建成功",
  "data": {
    "workflow": {
      "id": "workflow_1705123456789",
      "name": "火车票预订流程",
      "status": "active",
      "created_at": "2024-01-20T10:00:00Z",
      "node_count": 8,
      "complexity": "medium"
    }
  }
}
```

### 2. 获取工作流列表

**GET** `/api/workflows`

#### 描述
获取工作流列表，支持分页、筛选和排序。用于在开发者配置面板中显示所有可用的工作流。

#### 请求参数

**查询参数：**
| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| limit | integer | 否 | 50 | 每页数量，最大100 |
| status | string | 否 | - | 工作流状态筛选：active, draft, inactive |
| business_scenario | string | 否 | - | 业务场景筛选 |
| search | string | 否 | - | 搜索关键词（匹配名称和描述） |
| sort_by | string | 否 | created_at | 排序字段：name, created_at, updated_at, node_count |
| sort_order | string | 否 | desc | 排序方向：asc, desc |

**请求头：**
```
Authorization: Bearer <token>
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取工作流列表成功",
  "data": {
    "workflows": [
      {
        "id": "workflow_1705123456789",
        "name": "火车票预订流程",
        "description": "用户购买火车票时AI应该遵循的标准流程",
        "business_scenario": "train_ticket_booking",
        "status": "active",
        "version": "1.0.0",
        "node_count": 8,
        "instance_count": 156,
        "created_at": "2024-01-20T10:00:00Z",
        "updated_at": "2024-01-20T15:30:00Z"
      },
      {
        "id": "workflow_1705123456790",
        "name": "用户查询处理流程",
        "description": "处理用户一般性查询的标准流程",
        "business_scenario": "customer_service",
        "status": "active",
        "version": "2.1.0",
        "node_count": 5,
        "instance_count": 89,
        "created_at": "2024-01-18T14:20:00Z",
        "updated_at": "2024-01-19T09:15:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 50,
      "total_count": 12,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

**错误响应 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "errors": [
      {
        "field": "page",
        "message": "页码必须大于0"
      },
      {
        "field": "limit",
        "message": "每页数量不能超过100"
      }
    ]
  }
}
```

### 3. 获取工作流详情

**GET** `/api/workflows/{workflow_id}`

#### 描述
获取指定工作流的完整定义，包括所有节点、关系和AI指令。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| workflow_id | string | 是 | 工作流唯一标识符 |

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取工作流详情成功",
  "data": {
    "workflow": {
      "id": "workflow_train_booking",
      "name": "火车票预订流程",
      "description": "用户购买火车票时AI应该遵循的标准流程",
      "business_scenario": "train_ticket_booking",
      "user_intents": ["我要买火车票", "帮我订火车票"],
      "trigger_keywords": ["火车票", "车票", "订票"],
      "inputs": [...],
      "nodes": [...],
      "node_relationships": {
        "start": {
          "children": ["collect_info"],
          "conditions": [{"type": "always"}]
        },
        "collect_info": {
          "parents": ["start"],
          "children": ["search_trains"],
          "conditions": [{"type": "form_completed"}]
        }
      },
      "metadata": {
        "created_at": "2024-01-20T10:00:00Z",
        "updated_at": "2024-01-20T15:30:00Z",
        "tags": ["transportation", "booking"],
        "complexity": "medium"
      }
    }
  }
}
```

### 4. 更新工作流

**PUT** `/api/workflows/{workflow_id}`

#### 描述
更新指定工作流的信息，包括基本信息、节点配置和关系。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| workflow_id | string | 是 | 工作流唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "description": "string",
  "business_scenario": "string",
  "status": "active|draft|inactive",
  "user_intents": ["string"],
  "trigger_keywords": ["string"],
  "inputs": [...],
  "nodes": [...],
  "metadata": {
    "tags": ["string"],
    "complexity": "low|medium|high"
  }
}
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "工作流更新成功",
  "data": {
    "workflow": {
      "id": "workflow_1705123456789",
      "name": "火车票预订流程",
      "status": "active",
      "updated_at": "2024-01-20T16:30:00Z",
      "node_count": 9,
      "version": "1.1.0"
    }
  }
}
```

### 5. 删除工作流

**DELETE** `/api/workflows/{workflow_id}`

#### 描述
删除指定的工作流及其所有相关节点和配置。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| workflow_id | string | 是 | 工作流唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "工作流删除成功",
  "data": {
    "deleted_workflow_id": "workflow_1705123456789",
    "deleted_nodes_count": 8
  }
}
```

### 6. 添加节点

**POST** `/api/workflows/{workflow_id}/nodes`

#### 描述
向指定工作流添加新节点，包括父子关系和转换条件。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| workflow_id | string | 是 | 工作流唯一标识符 |

**请求体：**
```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "type": "api_call|user_input|condition|notification",
  "position": {
    "x": number,
    "y": number
  },
  "parent_nodes": ["string"],
  "child_nodes": [
    {
      "node_id": "string",
      "condition": {
        "type": "always|expression|form_completed",
        "expression": "string",
        "description": "string"
      }
    }
  ],
  "config": {
    "api_id": "string",
    "api_endpoint": "string",
    "method": "string",
    "parameters": {}
  },
  "outputs": [
    {
      "name": "string",
      "type": "string",
      "description": "string"
    }
  ],
  "ai_instructions": {
    "purpose": "string",
    "action": "string",
    "user_message": "string",
    "success_handling": "string",
    "error_handling": "string"
  }
}
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "节点添加成功",
  "data": {
    "node": {
      "id": "new_node_123",
      "name": "新节点",
      "type": "api_call",
      "parent_count": 1,
      "child_count": 2,
      "created_at": "2024-01-20T16:00:00Z"
    },
    "workflow_updated": true
  }
}
```

---

## 🤖 AI使用指南

### AI如何使用工作流

1. **意图识别**
   - 分析用户输入，识别业务意图
   - 根据user_intents和trigger_keywords匹配合适的工作流
   - 选择最符合用户需求的工作流定义

2. **流程执行**
   - 从start节点开始，按照父子关系和条件执行
   - 根据ai_instructions中的指导与用户交互
   - 调用config中指定的API获取数据

3. **条件判断**
   - 评估child_nodes中的condition表达式
   - 根据条件结果选择下一个执行节点
   - 支持多分支和并行处理

4. **数据流管理**
   - 使用{{}}语法引用节点输出和工作流输入
   - 自动进行数据类型转换和验证
   - 维护执行上下文和状态



---

## 🎯 节点类型说明

### 1. start - 开始节点
- **用途**: 工作流的入口点
- **AI行为**: 初始化对话，收集基本信息
- **示例**: "好的，我来帮您预订火车票"

### 2. api_call - API调用节点
- **用途**: 调用系统中注册的API
- **AI行为**: 根据config调用指定API，处理响应
- **示例**: 调用火车票查询API

### 3. user_input - 用户输入节点
- **用途**: 收集用户信息或选择
- **AI行为**: 显示表单或选项，等待用户输入
- **示例**: 收集乘客信息表单

### 4. condition - 条件判断节点
- **用途**: 根据条件决定流程走向
- **AI行为**: 评估表达式，选择分支
- **示例**: 检查是否找到可用车次

### 5. notification - 通知节点
- **用途**: 向用户显示信息或提示
- **AI行为**: 显示消息，提供建议
- **示例**: "抱歉，没有找到合适的车次"

### 6. end - 结束节点
- **用途**: 工作流的结束点
- **AI行为**: 总结结果，清理状态
- **示例**: "火车票预订完成！"

---

## 📝 最佳实践

### 1. 工作流设计原则
- **单一职责**: 每个工作流专注于一个业务场景
- **清晰命名**: 使用描述性的工作流和节点名称
- **完整指导**: 为每个节点提供详细的ai_instructions
- **合理分支**: 设计清晰的条件分支和错误处理

### 2. 节点关系设计
- **明确父子关系**: 确保节点间的逻辑关系清晰
- **条件完整性**: 为每个分支提供明确的转换条件
- **避免循环**: 防止无限循环的节点关系
- **并行处理**: 合理使用并行节点提高效率

### 3. AI友好设计
- **详细指令**: 在ai_instructions中提供清晰的执行指导
- **用户消息**: 为每个步骤提供用户友好的提示信息
- **错误处理**: 提供详细的错误信息和恢复建议
- **数据验证**: 确保数据类型和格式的一致性

### 4. API集成
- **标准映射**: 使用标准的API ID和端点映射
- **参数绑定**: 正确使用{{}}语法绑定参数
- **响应处理**: 合理映射API响应到节点输出
- **错误处理**: 为API调用失败提供备选方案

这个工作流设计模块为AI提供了完整的业务流程指导框架，通过图结构和详细的执行指令，AI可以准确理解和处理复杂的业务流程。