"""
AI代码生成服务
专门处理代码生成相关的AI调用，使用core/ai/bailian_client
"""
import logging
from typing import Dict, Any, Optional, List
import json

from app.core.ai.bailian_client import bailian_client
from app.core.ai.prompt_templates import prompt_manager, PromptType

logger = logging.getLogger(__name__)


class AICodeService:
    """AI代码生成服务"""
    
    def __init__(self):
        self.client = bailian_client
    
    async def generate_api_code(self, entity_config: Dict[str, Any], 
                               api_type: str = "crud") -> Dict[str, Any]:
        """
        生成API代码
        
        Args:
            entity_config: 实体配置信息
            api_type: API类型 (crud, form, custom)
            
        Returns:
            生成的代码和相关信息
        """
        try:
            # 构建代码生成提示词
            messages = prompt_manager.get_messages(
                PromptType.CODE_GENERATION,
                entity_config=json.dumps(entity_config, ensure_ascii=False),
                api_type=api_type,
                framework="FastAPI"
            )
            
            # 调用AI生成代码
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.1,  # 代码生成使用较低的温度
                max_tokens=2000
            )
            
            if not response.get("success"):
                return {
                    "success": False,
                    "error": "AI代码生成失败",
                    "details": response.get("error", "Unknown error")
                }
            
            generated_code = response.get("content", "")
            
            # 解析生成的代码
            parsed_result = self._parse_generated_code(generated_code)
            
            return {
                "success": True,
                "code": parsed_result.get("code", generated_code),
                "imports": parsed_result.get("imports", []),
                "functions": parsed_result.get("functions", []),
                "classes": parsed_result.get("classes", []),
                "usage": response.get("usage", {}),
                "raw_response": generated_code
            }
            
        except Exception as e:
            logger.error(f"AI代码生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def generate_model_code(self, entity_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成数据模型代码
        
        Args:
            entity_config: 实体配置信息
            
        Returns:
            生成的模型代码
        """
        try:
            messages = prompt_manager.get_messages(
                PromptType.MODEL_GENERATION,
                entity_config=json.dumps(entity_config, ensure_ascii=False),
                orm="SQLAlchemy"
            )
            
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.1,
                max_tokens=1500
            )
            
            if not response.get("success"):
                return {
                    "success": False,
                    "error": "AI模型代码生成失败",
                    "details": response.get("error", "Unknown error")
                }
            
            return {
                "success": True,
                "code": response.get("content", ""),
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"AI模型代码生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def review_generated_code(self, code: str, 
                                   review_type: str = "security") -> Dict[str, Any]:
        """
        AI代码审查
        
        Args:
            code: 要审查的代码
            review_type: 审查类型 (security, performance, style)
            
        Returns:
            代码审查结果
        """
        try:
            messages = prompt_manager.get_messages(
                PromptType.CODE_REVIEW,
                code=code,
                review_type=review_type
            )
            
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.2,
                max_tokens=1000
            )
            
            if not response.get("success"):
                return {
                    "success": False,
                    "error": "AI代码审查失败",
                    "details": response.get("error", "Unknown error")
                }
            
            review_content = response.get("content", "")
            
            # 尝试解析审查结果
            try:
                review_result = json.loads(review_content)
            except json.JSONDecodeError:
                review_result = {
                    "summary": review_content,
                    "issues": [],
                    "suggestions": [],
                    "score": 0
                }
            
            return {
                "success": True,
                "review": review_result,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"AI代码审查失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _parse_generated_code(self, code: str) -> Dict[str, Any]:
        """
        解析生成的代码，提取结构信息
        
        Args:
            code: 生成的代码字符串
            
        Returns:
            解析后的代码结构信息
        """
        try:
            result = {
                "code": code,
                "imports": [],
                "functions": [],
                "classes": []
            }
            
            lines = code.split('\n')
            
            for line in lines:
                stripped = line.strip()
                
                # 提取import语句
                if stripped.startswith('import ') or stripped.startswith('from '):
                    result["imports"].append(stripped)
                
                # 提取函数定义
                elif stripped.startswith('def '):
                    func_name = stripped.split('(')[0].replace('def ', '').strip()
                    result["functions"].append(func_name)
                
                # 提取类定义
                elif stripped.startswith('class '):
                    class_name = stripped.split('(')[0].replace('class ', '').strip(':')
                    result["classes"].append(class_name)
            
            return result
            
        except Exception as e:
            logger.warning(f"代码解析失败: {str(e)}")
            return {
                "code": code,
                "imports": [],
                "functions": [],
                "classes": []
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取代码生成服务健康状态"""
        return {
            "service": "ai_code_service",
            "status": "healthy",
            "client_status": self.client.get_health_status()
        }


# 全局AI代码生成服务实例
ai_code_service = AICodeService()
