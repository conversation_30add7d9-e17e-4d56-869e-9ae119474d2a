import React from 'react';

interface VoiceSynthesisProps {
  isSpeaking: boolean;
  onStopSpeaking: () => void;
}

const VoiceSynthesis: React.FC<VoiceSynthesisProps> = ({
  isSpeaking,
  onStopSpeaking
}) => {
  if (!isSpeaking) return null;

  return (
    <button
      className="speaker-button speaking"
      onClick={onStopSpeaking}
      aria-label="停止语音播放"
      style={{
        position: 'absolute',
        right: '80px',
        bottom: '30px',
        width: '50px',
        height: '50px',
        borderRadius: '50%',
        border: 'none',
        background: 'rgba(255, 59, 48, 0.8)',
        color: 'white',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '20px',
        boxShadow: '0 4px 20px rgba(255, 59, 48, 0.3)',
        transition: 'all 0.3s ease',
        animation: 'pulse 1.5s infinite'
      }}
    >
      <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
      </svg>
    </button>
  );
};

export default VoiceSynthesis;
