"""
API路由管理模块Pydantic模型
"""
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from enum import Enum


class RouteStatus(str, Enum):
    """路由状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"


class HandlerType(str, Enum):
    """处理器类型枚举"""
    ENTITY_CRUD = "entity_crud"
    CUSTOM_FUNCTION = "custom_function"
    PROXY = "proxy"
    WORKFLOW = "workflow"
    STATIC_DATA = "static_data"


class ParameterLocation(str, Enum):
    """参数位置枚举"""
    QUERY = "query"
    PATH = "path"
    HEADER = "header"
    BODY = "body"


class RouteParameter(BaseModel):
    """路由参数模型"""
    name: str = Field(..., description="参数名称")
    type: str = Field(..., description="参数类型")
    location: ParameterLocation = Field(..., description="参数位置")
    required: bool = Field(False, description="是否必填")
    description: Optional[str] = Field(None, description="参数描述")
    validation: Optional[Dict[str, Any]] = Field(None, description="验证配置")
    default: Optional[str] = Field(None, description="默认值")


class RouteResponse(BaseModel):
    """路由响应模型"""
    description: str = Field(..., description="响应描述")
    response_schema: Optional[Dict[str, Any]] = Field(None, description="响应Schema", alias="schema")
    headers: Optional[Dict[str, str]] = Field(None, description="响应头")

    class Config:
        populate_by_name = True


class RouteHandler(BaseModel):
    """路由处理器模型"""
    type: HandlerType = Field(..., description="处理器类型")
    config: Dict[str, Any] = Field(..., description="处理器配置")


# API路由管理相关模型
class APIRouteRegisterRequest(BaseModel):
    """注册API路由请求"""
    api_id: str = Field(..., min_length=1, max_length=100, description="API标识符")
    name: str = Field(..., min_length=1, max_length=200, description="路由名称")
    endpoint: str = Field(..., min_length=1, max_length=500, description="API端点")
    method: str = Field(..., description="HTTP方法")
    description: Optional[str] = Field(None, description="路由描述")
    auth_required: bool = Field(True, description="是否需要认证")
    handler: RouteHandler = Field(..., description="处理器配置")
    parameters: Optional[List[RouteParameter]] = Field([], description="参数列表")
    responses: Dict[str, RouteResponse] = Field(..., description="响应配置")


class APIRouteUpdateRequest(BaseModel):
    """更新API路由请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="路由名称")
    description: Optional[str] = Field(None, description="路由描述")
    auth_required: Optional[bool] = Field(None, description="是否需要认证")
    handler: Optional[RouteHandler] = Field(None, description="处理器配置")
    parameters: Optional[List[RouteParameter]] = Field(None, description="参数列表")
    responses: Optional[Dict[str, RouteResponse]] = Field(None, description="响应配置")


class APIRouteStatusRequest(BaseModel):
    """路由状态请求"""
    status: RouteStatus = Field(..., description="路由状态")


class APIRoute(BaseModel):
    """API路由模型"""
    id: str = Field(..., description="路由ID")
    api_id: str = Field(..., description="API标识符")
    name: str = Field(..., description="路由名称")
    endpoint: str = Field(..., description="API端点")
    method: str = Field(..., description="HTTP方法")
    description: Optional[str] = Field(None, description="路由描述")
    auth_required: bool = Field(..., description="是否需要认证")
    status: RouteStatus = Field(..., description="路由状态")
    handler: RouteHandler = Field(..., description="处理器配置")
    parameters: List[RouteParameter] = Field([], description="参数列表")
    responses: Dict[str, RouteResponse] = Field(..., description="响应配置")
    call_count: int = Field(0, description="调用次数")
    last_called: Optional[str] = Field(None, description="最后调用时间")
    avg_response_time: int = Field(0, description="平均响应时间(ms)")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class APIRouteListItem(BaseModel):
    """API路由列表项模型"""
    id: str = Field(..., description="路由ID")
    api_id: str = Field(..., description="API标识符")
    name: str = Field(..., description="路由名称")
    endpoint: str = Field(..., description="API端点")
    method: str = Field(..., description="HTTP方法")
    description: Optional[str] = Field(None, description="路由描述")
    auth_required: bool = Field(..., description="是否需要认证")
    status: RouteStatus = Field(..., description="路由状态")
    handler_type: HandlerType = Field(..., description="处理器类型")
    call_count: int = Field(0, description="调用次数")
    last_called: Optional[str] = Field(None, description="最后调用时间")
    avg_response_time: int = Field(0, description="平均响应时间(ms)")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class APIRouteHealthStatus(BaseModel):
    """API路由健康状态模型"""
    route_id: str = Field(..., description="路由ID")
    api_id: str = Field(..., description="API标识符")
    endpoint: str = Field(..., description="API端点")
    method: str = Field(..., description="HTTP方法")
    status: RouteStatus = Field(..., description="路由状态")
    health: str = Field(..., description="健康状态")
    last_check: str = Field(..., description="最后检查时间")
    response_time: Optional[int] = Field(None, description="响应时间(ms)")
    error_message: Optional[str] = Field(None, description="错误信息")


# 响应模型
class APIRouteRegisterResponse(BaseModel):
    """注册API路由响应"""
    route: APIRoute = Field(..., description="路由信息")


class APIRouteListResponse(BaseModel):
    """API路由列表响应"""
    routes: List[APIRouteListItem] = Field(..., description="路由列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页数量")


class APIRouteDetailResponse(BaseModel):
    """API路由详情响应"""
    route: APIRoute = Field(..., description="路由详情")


class APIRouteHealthResponse(BaseModel):
    """API路由健康状态响应"""
    routes: List[APIRouteHealthStatus] = Field(..., description="路由健康状态列表")
    total_routes: int = Field(..., description="总路由数")
    healthy_routes: int = Field(..., description="健康路由数")
    unhealthy_routes: int = Field(..., description="不健康路由数")
    check_time: str = Field(..., description="检查时间")
