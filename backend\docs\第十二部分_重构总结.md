# 第十二部分：AI服务重构总结

## 🎯 核心问题解决

您提出的关键问题：
1. **amis生成的schema需要能够正确调用已有的API**
2. **后端需要告诉AI当前用户角色有哪些权限，能访问什么API**
3. **文件结构混乱，不要都堆在ai_service里面**

## ✅ 解决方案

### 1. 清晰的文件结构

**重构前的问题：**
```
app/services/ai_service.py  # 所有AI功能都堆在一起，600+行代码
```

**重构后的结构：**
```
app/
├── core/ai/                          # AI核心组件（保持不变）
│   ├── bailian_client.py            # 阿里云百炼客户端
│   ├── amis_knowledge.py            # amis知识库
│   ├── amis_validator.py            # amis校验器
│   └── prompt_templates.py          # 提示词模板
├── services/
│   ├── ai_code_service.py           # AI代码生成服务
│   ├── ai_amis_service.py           # AI amis schema生成服务（考虑权限）
│   ├── user_permission_service.py   # 用户权限服务
│   ├── ai_service_new.py            # AI服务协调器
│   └── ai_service.py                # 原文件（保留备份）
```

### 2. 权限感知的AMIS Schema生成

**核心实现：**
- `AIAmisService.generate_schema_with_permissions()` - 根据用户权限生成schema
- `UserPermissionService.get_user_context_for_ai()` - 获取用户权限上下文
- 自动为所有API调用添加认证头部：`Authorization: Bearer ${ls:access_token}`

**权限检查流程：**
```python
# 1. 获取用户权限信息
user_permissions = ["user:read", "users:list"]
accessible_apis = [
    {"resource": "users", "action": "list", "endpoint": "/api/users", "method": "GET"}
]

# 2. 检查意图权限
if not self._check_intent_permission(intent, user_permissions, accessible_apis):
    return self._generate_permission_denied_response(intent, command)

# 3. 生成带权限的schema
crud_schema = amis_knowledge.generate_crud_with_permissions(
    entity, accessible_apis, entity_fields
)
```

### 3. 正确的API调用

**自动认证头部添加：**
```python
def _ensure_api_authentication(self, schema: Dict[str, Any]):
    """确保schema中的所有API调用都包含正确的认证头部"""
    if "api" in component:
        api_config = component["api"]
        if isinstance(api_config, dict):
            if "headers" not in api_config:
                api_config["headers"] = {}
            api_config["headers"]["Authorization"] = "Bearer ${ls:access_token}"
```

**生成的schema示例：**
```json
{
  "type": "crud",
  "api": {
    "method": "GET",
    "url": "/api/users",
    "headers": {
      "Authorization": "Bearer ${ls:access_token}"
    }
  },
  "columns": [...],
  "headerToolbar": [...]
}
```

## 🔧 技术实现细节

### 1. AI服务协调器 (`ai_service_new.py`)

**职责：**
- 协调各个AI服务模块
- 处理用户命令的完整流程
- 意图分析和实体识别

**核心方法：**
```python
async def process_command(self, command: str, context: Optional[Dict[str, Any]] = None):
    # 1. 分析用户意图
    intent = await self._analyze_intent(command, context)
    
    # 2. 获取用户权限信息
    user_permissions = context.get("user_permissions", [])
    accessible_apis = context.get("accessible_apis", [])
    
    # 3. 使用AI AMIS服务生成schema（考虑权限）
    schema_result = await self.amis_service.generate_schema_with_permissions(
        command, intent_data, user_permissions, accessible_apis, user_role, entities
    )
```

### 2. AI AMIS服务 (`ai_amis_service.py`)

**职责：**
- 专门处理amis schema生成
- 考虑用户权限和API访问控制
- 确保生成的schema能正确调用API

**核心特性：**
- 权限检查：`_check_intent_permission()`
- API认证确保：`_ensure_api_authentication()`
- 权限约束应用：`_apply_permission_constraints()`

### 3. 用户权限服务 (`user_permission_service.py`)

**职责：**
- 获取用户权限列表
- 获取用户可访问的API列表
- 提供用户上下文信息给AI

**权限级别定义：**
```python
def _get_default_permissions_by_level(self, level: int) -> List[str]:
    if level >= 9:  # 超级管理员
        return ["*:*"]
    elif level >= 5:  # 管理员
        return ["user:*", "role:*", "permission:*", "data:*"]
    elif level >= 1:  # 普通用户
        return ["user:read", "user:update_self", "data:query"]
    else:  # 访客
        return ["user:read"]
```

### 4. AI代码服务 (`ai_code_service.py`)

**职责：**
- 专门处理代码生成
- 代码审查
- 代码解析和结构分析

## 🚀 使用流程

### 1. 用户登录并获取权限

```python
# 用户登录后，获取权限信息
user_context = user_permission_service.get_user_context_for_ai(user, db)
# 返回：
{
    "user_id": 123,
    "user_permissions": ["user:read", "users:list"],
    "accessible_apis": [
        {"endpoint": "/api/users", "method": "GET", "resource": "users", "action": "list"}
    ],
    "user_role": {"id": "role_user", "name": "普通用户", "level": 1}
}
```

### 2. AI命令处理

```python
# 用户发送命令："显示用户列表"
result = await ai_service.process_command(
    command="显示用户列表",
    context=user_context  # 包含用户权限信息
)
```

### 3. 生成的AMIS Schema

```json
{
  "type": "page",
  "title": "用户管理",
  "body": [
    {
      "type": "crud",
      "api": {
        "method": "GET",
        "url": "/api/users",
        "headers": {
          "Authorization": "Bearer ${ls:access_token}"
        }
      },
      "columns": [
        {"name": "username", "label": "用户名"},
        {"name": "email", "label": "邮箱"}
      ],
      "headerToolbar": [
        // 只有有权限的用户才会看到"新增"按钮
      ]
    }
  ]
}
```

## 📊 测试验证

**测试覆盖：**
- ✅ 新文件结构导入测试
- ✅ 用户权限服务测试
- ✅ AI AMIS服务测试
- ✅ AI代码服务测试
- ✅ 服务协调测试
- ✅ API集成测试

**测试结果：6/6 全部通过**

## 🎉 核心价值

### 1. 解决的问题
- **API调用正确性**：所有生成的schema都包含正确的API调用和认证头部
- **权限感知**：AI能够根据用户权限生成相应的界面
- **代码组织**：清晰的文件结构，职责分离

### 2. 技术优势
- **可维护性**：每个服务职责单一，易于维护
- **可扩展性**：新功能可以独立添加到相应服务
- **可测试性**：每个服务都可以独立测试

### 3. 用户体验
- **安全性**：用户只能看到和操作有权限的功能
- **智能性**：AI根据用户角色生成合适的界面
- **一致性**：所有API调用都遵循统一的认证规范

## 🔮 后续优化

1. **缓存优化**：用户权限信息缓存
2. **性能优化**：AI调用结果缓存
3. **监控增强**：API调用成功率监控
4. **功能扩展**：支持更多复杂的权限规则

---

**总结：** 通过这次重构，我们成功解决了您提出的所有核心问题，实现了权限感知的AI界面生成系统，确保amis schema能够正确调用已有API，并且后端能够准确告诉AI当前用户的权限信息。
