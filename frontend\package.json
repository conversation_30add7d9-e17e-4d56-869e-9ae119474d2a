{"name": "my-amis-app", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@ant-design/icons": "^5.6.1", "@types/node": "^16.18.126", "@types/react": "^17.0.80", "@types/react-dom": "^17.0.25", "amis": "^6.11.0", "antd": "^5.26.5", "axios": "^1.8.3", "butterfly-dag": "5.1.0-beta.38", "copy-to-clipboard": "^3.3.3", "monaco-editor-webpack-plugin": "^7.1.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^6.28.0", "react-scripts": "^5.0.1", "siriwave": "^2.4.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "dev": "pnpm start", "install:pnpm": "pnpm install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0"}, "pnpm": {"overrides": {"react-pdf": "8.0.2"}}}