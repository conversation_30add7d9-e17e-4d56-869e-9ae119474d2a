# 模板管理模块 API 文档

## 📋 概述

模板管理模块提供预设业务场景模板的查询和获取功能，帮助开发者快速创建常见的业务场景配置。

## 🎯 API 端点列表

### 1. 获取所有可用模板
### 2. 获取特定模板详细配置

---

## API 详细文档

### 1. 获取所有可用模板

**GET** `/api/templates`

#### 描述
获取系统中所有可用的业务场景模板列表。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| category | string | 否 | 模板分类筛选 |
| include_preview | boolean | 否 | 是否包含预览信息，默认false |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/templates?category=business&include_preview=true" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取模板列表成功",
  "data": {
    "templates": [
      {
        "key": "ecommerce_basic",
        "name": "基础电商系统",
        "description": "适合中小型电商企业的基础管理系统",
        "category": "business",
        "difficulty": "beginner",
        "estimated_setup_time": "30分钟",
        "features": [
          "商品管理",
          "订单处理",
          "用户管理",
          "库存管理"
        ],
        "preview": {
          "entities": 4,
          "workflows": 3,
          "forms": 6,
          "apis": 12
        },
        "tags": ["电商", "零售", "管理系统"],
        "created_at": "2024-01-15T00:00:00.000Z",
        "updated_at": "2024-01-18T00:00:00.000Z"
      },
      {
        "key": "hospital_management",
        "name": "医院管理系统",
        "description": "完整的医院信息管理系统模板",
        "category": "healthcare",
        "difficulty": "intermediate",
        "estimated_setup_time": "45分钟",
        "features": [
          "患者管理",
          "医生排班",
          "预约挂号",
          "病历管理",
          "药品管理"
        ],
        "preview": {
          "entities": 6,
          "workflows": 5,
          "forms": 8,
          "apis": 18
        },
        "tags": ["医疗", "管理系统", "预约"],
        "created_at": "2024-01-15T00:00:00.000Z",
        "updated_at": "2024-01-19T00:00:00.000Z"
      },
      {
        "key": "restaurant_pos",
        "name": "餐厅点餐系统",
        "description": "餐厅点餐和管理一体化系统",
        "category": "hospitality",
        "difficulty": "beginner",
        "estimated_setup_time": "25分钟",
        "features": [
          "菜品管理",
          "订单管理",
          "桌台管理",
          "收银结算"
        ],
        "preview": {
          "entities": 5,
          "workflows": 4,
          "forms": 7,
          "apis": 15
        },
        "tags": ["餐饮", "点餐", "收银"],
        "created_at": "2024-01-15T00:00:00.000Z",
        "updated_at": "2024-01-17T00:00:00.000Z"
      }
    ],
    "total": 3,
    "categories": [
      {
        "key": "business",
        "name": "商业管理",
        "count": 1
      },
      {
        "key": "healthcare",
        "name": "医疗健康",
        "count": 1
      },
      {
        "key": "hospitality",
        "name": "服务行业",
        "count": 1
      }
    ]
  }
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| key | string | 模板唯一标识符 |
| name | string | 模板名称 |
| description | string | 模板描述 |
| category | string | 模板分类 |
| difficulty | string | 难度等级 (beginner/intermediate/advanced) |
| estimated_setup_time | string | 预估配置时间 |
| features | array | 主要功能列表 |
| preview | object | 预览信息（实体、工作流、表单、API数量） |
| tags | array | 标签列表 |

---

### 2. 获取特定模板详细配置

**GET** `/api/templates/{template_key}`

#### 描述
获取指定模板的详细配置信息，包括完整的实体、工作流、表单等配置。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| template_key | string | 是 | 模板唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/templates/ecommerce_basic" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取模板详情成功",
  "data": {
    "template": {
      "key": "ecommerce_basic",
      "name": "基础电商系统",
      "description": "适合中小型电商企业的基础管理系统",
      "category": "business",
      "difficulty": "beginner",
      "estimated_setup_time": "30分钟",
      "version": "1.2.0",
      "author": "AILF Team",
      "license": "MIT",
      "config": {
        "scenario": {
          "type": "ecommerce",
          "business_domain": "电子商务",
          "target_users": ["管理员", "销售人员", "客服"],
          "key_features": ["商品管理", "订单处理", "用户管理", "库存管理"]
        },
        "entities": [
          {
            "name": "product",
            "displayName": "商品",
            "description": "商品信息管理",
            "fields": [
              {
                "name": "name",
                "displayName": "商品名称",
                "type": "text",
                "required": true,
                "validation": {
                  "maxLength": 100
                }
              },
              {
                "name": "price",
                "displayName": "价格",
                "type": "decimal",
                "required": true,
                "validation": {
                  "min": 0,
                  "precision": 2
                }
              },
              {
                "name": "stock",
                "displayName": "库存",
                "type": "number",
                "required": true,
                "defaultValue": 0
              },
              {
                "name": "category",
                "displayName": "分类",
                "type": "select",
                "options": ["电子产品", "服装", "食品", "图书"]
              }
            ]
          },
          {
            "name": "order",
            "displayName": "订单",
            "description": "订单信息管理",
            "fields": [
              {
                "name": "order_no",
                "displayName": "订单号",
                "type": "text",
                "required": true,
                "unique": true
              },
              {
                "name": "customer_name",
                "displayName": "客户姓名",
                "type": "text",
                "required": true
              },
              {
                "name": "total_amount",
                "displayName": "总金额",
                "type": "decimal",
                "required": true
              },
              {
                "name": "status",
                "displayName": "订单状态",
                "type": "select",
                "options": ["待付款", "已付款", "已发货", "已完成", "已取消"],
                "defaultValue": "待付款"
              }
            ]
          }
        ],
        "workflows": [
          {
            "name": "订单处理流程",
            "description": "从下单到完成的完整流程",
            "steps": [
              {
                "name": "创建订单",
                "type": "form",
                "entity": "order"
              },
              {
                "name": "支付确认",
                "type": "condition",
                "condition": "payment_status === 'success'"
              },
              {
                "name": "库存扣减",
                "type": "api_call",
                "endpoint": "/api/inventory/deduct"
              },
              {
                "name": "发货通知",
                "type": "notification",
                "recipients": ["customer", "warehouse"]
              }
            ]
          }
        ],
        "forms": [
          {
            "name": "商品录入表单",
            "entity": "product",
            "layout": "vertical",
            "sections": [
              {
                "title": "基本信息",
                "fields": ["name", "category", "price", "stock"]
              }
            ]
          },
          {
            "name": "订单创建表单",
            "entity": "order",
            "layout": "horizontal",
            "sections": [
              {
                "title": "订单信息",
                "fields": ["order_no", "customer_name", "total_amount"]
              }
            ]
          }
        ],
        "apis": [
          {
            "path": "/api/products",
            "method": "GET",
            "description": "获取商品列表"
          },
          {
            "path": "/api/products",
            "method": "POST",
            "description": "创建商品"
          },
          {
            "path": "/api/orders",
            "method": "GET",
            "description": "获取订单列表"
          },
          {
            "path": "/api/orders",
            "method": "POST",
            "description": "创建订单"
          }
        ],
        "roles": [
          {
            "name": "管理员",
            "code": "admin",
            "level": 10,
            "permissions": ["*"]
          },
          {
            "name": "销售人员",
            "code": "sales",
            "level": 5,
            "permissions": ["products:read", "orders:*"]
          }
        ]
      },
      "setup_guide": {
        "steps": [
          "1. 选择此模板创建场景",
          "2. 根据需要调整商品分类",
          "3. 配置支付接口",
          "4. 设置用户角色权限",
          "5. 生成并部署系统"
        ],
        "prerequisites": [
          "了解基本的电商业务流程",
          "准备商品分类数据"
        ],
        "tips": [
          "可以根据实际需求添加更多商品字段",
          "建议先配置少量测试数据进行验证"
        ]
      },
      "created_at": "2024-01-15T00:00:00.000Z",
      "updated_at": "2024-01-18T00:00:00.000Z"
    }
  }
}
```

**未找到响应 (404 Not Found):**
```json
{
  "code": 404,
  "message": "模板不存在",
  "data": {
    "error": "template_not_found",
    "details": "指定的模板标识符不存在"
  }
}
```

---

## 📝 模板分类说明

| 分类 | 描述 | 包含模板 |
|------|------|----------|
| business | 商业管理 | 电商、零售、企业管理等 |
| healthcare | 医疗健康 | 医院、诊所、健康管理等 |
| education | 教育培训 | 学校、培训机构、在线教育等 |
| hospitality | 服务行业 | 餐饮、酒店、旅游等 |
| finance | 金融服务 | 银行、保险、投资等 |
| logistics | 物流运输 | 货运、仓储、配送等 |
| government | 政府机构 | 政务服务、公共管理等 |
| custom | 自定义 | 其他特殊业务场景 |

---

## 📝 难度等级说明

| 等级 | 描述 | 特点 |
|------|------|------|
| beginner | 初级 | 配置简单，功能基础，适合新手 |
| intermediate | 中级 | 功能较全，需要一定配置经验 |
| advanced | 高级 | 功能复杂，需要深入理解业务流程 |

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| template_not_found | 404 | 模板不存在 | 检查模板标识符是否正确 |
| invalid_category | 400 | 无效的分类参数 | 使用有效的分类值 |
| template_unavailable | 503 | 模板暂时不可用 | 稍后重试或联系管理员 |

---

## 💡 使用建议

### 选择模板
1. **根据业务领域选择** - 选择最接近你业务场景的模板
2. **考虑复杂度** - 新手建议从beginner级别开始
3. **查看预览信息** - 了解模板包含的实体和功能数量

### 定制模板
1. **基础配置** - 先使用默认配置创建场景
2. **逐步调整** - 根据实际需求修改实体字段
3. **测试验证** - 在正式使用前进行充分测试

### 最佳实践
1. **备份配置** - 在大幅修改前备份原始配置
2. **渐进式开发** - 先实现核心功能，再添加高级特性
3. **用户反馈** - 收集最终用户的使用反馈进行优化
