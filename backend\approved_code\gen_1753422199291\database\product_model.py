```python
from sqlalchemy import Column, Integer, String, Numeric, DateTime, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from typing import Optional
from decimal import Decimal

Base = declarative_base()

class Product(Base):
    """
    商品实体模型
    
    用于存储商品信息，包含商品的基本属性如名称、价格和分类等。
    """
    
    __tablename__ = 'products'
    
    # ID字段
    id: int = Column(
        Integer,
        primary_key=True,
        index=True,
        comment="商品唯一标识符"
    )
    
    # 商品名称字段
    name: str = Column(
        String(255),
        nullable=False,
        index=True,
        comment="商品名称"
    )
    
    # 价格字段
    price: Decimal = Column(
        Numeric(precision=10, scale=2),
        nullable=False,
        comment="商品价格"
    )
    
    # 分类字段
    category: str = Column(
        String(100),
        nullable=False,
        index=True,
        comment="商品分类"
    )
    
    # 创建时间戳（如果启用）
    created_at: DateTime = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="记录创建时间"
    )
    
    # 更新时间戳（如果启用）
    updated_at: DateTime = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="记录最后更新时间"
    )
    
    # 创建索引
    __table_args__ = (
        Index('idx_product_name', 'name'),
        Index('idx_product_category', 'category'),
        Index('idx_product_created_at', 'created_at'),
    )
    
    def __repr__(self) -> str:
        """
        返回对象的字符串表示形式
        
        Returns:
            str: Product对象的字符串表示
        """
        return f"<Product(id={self.id}, name='{self.name}', price={self.price}, category='{self.category}')>"
    
    def to_dict(self) -> dict:
        """
        将Product对象转换为字典格式
        
        Returns:
            dict: 包含商品信息的字典
        """
        return {
            "id": self.id,
            "name": self.name,
            "price": float(self.price) if self.price else None,
            "category": self.category,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

# 示例用法（注释掉以避免实际执行）
# from sqlalchemy import create_engine
# from sqlalchemy.orm import sessionmaker
#
# # 创建数据库引擎和会话
# engine = create_engine('sqlite:///example.db')
# SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
#
# # 创建表
# Base.metadata.create_all(bind=engine)
#
# # 使用示例
# db = SessionLocal()
# new_product = Product(
#     name="示例商品",
#     price=99.99,
#     category="电子产品"
# )
# db.add(new_product)
# db.commit()
# db.refresh(new_product)
# print(new_product)
```