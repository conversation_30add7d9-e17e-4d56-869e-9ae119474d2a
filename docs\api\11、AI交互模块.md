# AI交互模块 API 文档

## 📋 概述

AI交互模块提供基于自然语言的智能交互功能，支持语音/文本命令处理、智能分析和动态界面生成。

## 🤖 API 端点列表

### AI命令处理
1. 处理用户的语音/文本命令，生成动态界面
2. AI分析用户需求，推荐配置方案
3. AI优化现有配置
4. AI生成amis schema

### AI辅助配置
5. AI推荐业务实体设计
6. AI推荐工作流设计
7. AI推荐表单设计
8. AI推荐API设计

---

## API 详细文档

### 1. 处理用户的语音/文本命令，生成动态界面

**POST** `/api/command`

#### 描述
处理用户的自然语言命令，理解用户意图并生成相应的动态界面。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "command": "string",
  "context": {
    "user_id": "string",
    "session_id": "string",
    "current_page": "string",
    "history": []
  },
  "options": {
    "language": "string",
    "response_format": "string"
  }
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| command | string | 是 | 用户的语音或文本命令 |
| context | object | 否 | 上下文信息 |
| context.user_id | string | 否 | 用户ID |
| context.session_id | string | 否 | 会话ID |
| context.current_page | string | 否 | 当前页面 |
| context.history | array | 否 | 历史命令记录 |
| options.language | string | 否 | 语言设置，默认zh-CN |
| options.response_format | string | 否 | 响应格式，默认amis |

#### 支持的命令类型

| 命令类型 | 示例 | 生成结果 |
|----------|------|----------|
| 数据查询 | "显示所有商品" | 商品列表页面 |
| 数据创建 | "添加新商品" | 商品创建表单 |
| 数据编辑 | "修改商品信息" | 商品编辑表单 |
| 统计分析 | "显示销售报表" | 统计图表页面 |
| 流程操作 | "处理订单" | 工作流界面 |
| 系统设置 | "用户权限管理" | 权限配置页面 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/command" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "command": "我想查看所有商品的销售情况",
    "context": {
      "user_id": "user_001",
      "session_id": "session_123",
      "current_page": "dashboard",
      "history": [
        {
          "command": "显示商品列表",
          "timestamp": "2024-01-20T11:00:00.000Z"
        }
      ]
    },
    "options": {
      "language": "zh-CN",
      "response_format": "amis"
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "命令处理成功",
  "data": {
    "intent": {
      "type": "data_query",
      "entity": "product",
      "action": "list_with_analytics",
      "confidence": 0.95,
      "parameters": {
        "include_sales": true,
        "time_range": "all"
      }
    },
    "response_text": "为您显示商品销售情况分析页面",
    "schema": {
      "type": "page",
      "title": "商品销售分析",
      "body": [
        {
          "type": "grid",
          "columns": [
            {
              "md": 8,
              "body": [
                {
                  "type": "crud",
                  "title": "商品销售列表",
                  "api": "/api/products?include_sales=true",
                  "columns": [
                    {
                      "name": "name",
                      "label": "商品名称",
                      "type": "text"
                    },
                    {
                      "name": "price",
                      "label": "价格",
                      "type": "number"
                    },
                    {
                      "name": "sales_count",
                      "label": "销售数量",
                      "type": "number"
                    },
                    {
                      "name": "revenue",
                      "label": "销售收入",
                      "type": "number"
                    }
                  ]
                }
              ]
            },
            {
              "md": 4,
              "body": [
                {
                  "type": "chart",
                  "title": "销售趋势",
                  "api": "/api/analytics/product-sales-trend",
                  "config": {
                    "type": "line",
                    "xField": "date",
                    "yField": "sales",
                    "seriesField": "product"
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    "suggestions": [
      "查看特定商品的详细销售数据",
      "按时间段筛选销售情况",
      "导出销售报表"
    ],
    "session_id": "session_123",
    "processed_at": "2024-01-20T12:00:00.000Z"
  }
}
```

**理解失败响应 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "命令理解失败",
  "data": {
    "error": "intent_not_recognized",
    "command": "我想查看所有商品的销售情况",
    "possible_intents": [
      {
        "type": "data_query",
        "entity": "product",
        "confidence": 0.6,
        "suggestion": "显示商品列表"
      },
      {
        "type": "analytics",
        "entity": "sales",
        "confidence": 0.4,
        "suggestion": "显示销售报表"
      }
    ],
    "clarification_questions": [
      "您是想查看商品列表还是销售统计？",
      "需要查看哪个时间段的数据？"
    ]
  }
}
```

---

### 2. AI分析用户需求，推荐配置方案

**POST** `/api/ai/analyze`

#### 描述
基于用户描述的业务需求，AI分析并推荐最适合的系统配置方案。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "requirements": "string",
  "business_type": "string",
  "user_count": number,
  "complexity": "string"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/analyze" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "requirements": "我需要一个小型餐厅管理系统，能够管理菜品、处理订单、管理桌台，支持堂食和外卖",
    "business_type": "restaurant",
    "user_count": 10,
    "complexity": "medium"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "需求分析完成",
  "data": {
    "analysis": {
      "business_domain": "餐饮服务",
      "key_features": [
        "菜品管理",
        "订单处理",
        "桌台管理",
        "外卖支持"
      ],
      "complexity_score": 6.5,
      "estimated_development_time": "2-3周"
    },
    "recommended_template": {
      "key": "restaurant_pos",
      "name": "餐厅点餐系统",
      "match_score": 0.92,
      "reasons": [
        "完全匹配餐饮业务需求",
        "包含所需的核心功能",
        "支持堂食和外卖模式"
      ]
    },
    "suggested_entities": [
      {
        "name": "dish",
        "displayName": "菜品",
        "priority": "high",
        "fields": ["name", "price", "category", "description", "image"]
      },
      {
        "name": "table",
        "displayName": "餐桌",
        "priority": "high",
        "fields": ["number", "capacity", "status", "location"]
      },
      {
        "name": "order",
        "displayName": "订单",
        "priority": "high",
        "fields": ["order_no", "table_id", "items", "total", "status", "type"]
      }
    ],
    "suggested_workflows": [
      {
        "name": "堂食点餐流程",
        "priority": "high",
        "steps": ["选择餐桌", "点餐", "确认订单", "制作", "上菜", "结账"]
      },
      {
        "name": "外卖订单流程",
        "priority": "medium",
        "steps": ["在线点餐", "确认订单", "制作", "配送", "完成"]
      }
    ],
    "implementation_plan": {
      "phase1": {
        "name": "基础功能",
        "duration": "1周",
        "features": ["菜品管理", "基础订单处理"]
      },
      "phase2": {
        "name": "高级功能",
        "duration": "1周",
        "features": ["桌台管理", "外卖支持"]
      },
      "phase3": {
        "name": "优化完善",
        "duration": "1周",
        "features": ["报表统计", "用户体验优化"]
      }
    }
  }
}
```

---

### 3. AI优化现有配置

**POST** `/api/ai/optimize`

#### 描述
AI分析现有配置并提供优化建议和自动优化方案。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "config_type": "string",
  "config_id": "string",
  "optimization_goals": ["string"],
  "constraints": {}
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/optimize" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "config_type": "workflow",
    "config_id": "workflow_1705123456789",
    "optimization_goals": ["performance", "user_experience"],
    "constraints": {
      "max_steps": 10,
      "preserve_approval_steps": true
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "配置优化分析完成",
  "data": {
    "current_analysis": {
      "config_type": "workflow",
      "config_id": "workflow_1705123456789",
      "current_score": 7.2,
      "issues": [
        "步骤过多导致流程复杂",
        "缺少并行处理机制",
        "用户等待时间较长"
      ]
    },
    "optimization_suggestions": [
      {
        "type": "step_merge",
        "description": "合并相似的审批步骤",
        "impact": "减少2个步骤，提升30%效率",
        "priority": "high"
      },
      {
        "type": "parallel_processing",
        "description": "启用并行审批机制",
        "impact": "减少50%处理时间",
        "priority": "medium"
      }
    ],
    "optimized_config": {
      "estimated_score": 8.7,
      "changes": [
        "合并步骤3和步骤4",
        "添加并行审批分支"
      ],
      "preview": {
        "step_count": 6,
        "estimated_time": "2小时",
        "complexity_score": 6.5
      }
    },
    "auto_apply": false
  }
}
```

---

### 4. AI推荐业务实体设计

**POST** `/api/ai/suggest-entities`

#### 描述
基于业务场景和需求，AI推荐合适的业务实体设计方案。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "business_type": "string",
  "description": "string",
  "existing_entities": ["string"]
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/suggest-entities" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "business_type": "healthcare",
    "description": "小型诊所管理系统，需要管理患者、医生、预约和病历",
    "existing_entities": []
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "实体设计推荐完成",
  "data": {
    "suggested_entities": [
      {
        "name": "patient",
        "displayName": "患者",
        "description": "患者基本信息和医疗记录",
        "priority": "high",
        "fields": [
          {
            "name": "name",
            "displayName": "姓名",
            "type": "text",
            "required": true,
            "validation": {"maxLength": 50}
          },
          {
            "name": "id_card",
            "displayName": "身份证号",
            "type": "text",
            "required": true,
            "unique": true,
            "validation": {"pattern": "^[0-9]{17}[0-9Xx]$"}
          },
          {
            "name": "phone",
            "displayName": "联系电话",
            "type": "phone",
            "required": true
          },
          {
            "name": "birth_date",
            "displayName": "出生日期",
            "type": "date",
            "required": true
          },
          {
            "name": "gender",
            "displayName": "性别",
            "type": "select",
            "options": ["男", "女"],
            "required": true
          },
          {
            "name": "address",
            "displayName": "地址",
            "type": "text"
          },
          {
            "name": "emergency_contact",
            "displayName": "紧急联系人",
            "type": "text"
          },
          {
            "name": "medical_history",
            "displayName": "病史",
            "type": "textarea"
          }
        ]
      },
      {
        "name": "doctor",
        "displayName": "医生",
        "description": "医生信息和专业资质",
        "priority": "high",
        "fields": [
          {
            "name": "name",
            "displayName": "姓名",
            "type": "text",
            "required": true
          },
          {
            "name": "license_number",
            "displayName": "执业证号",
            "type": "text",
            "required": true,
            "unique": true
          },
          {
            "name": "department",
            "displayName": "科室",
            "type": "select",
            "options": ["内科", "外科", "儿科", "妇科", "骨科"],
            "required": true
          },
          {
            "name": "title",
            "displayName": "职称",
            "type": "select",
            "options": ["主任医师", "副主任医师", "主治医师", "住院医师"]
          },
          {
            "name": "specialization",
            "displayName": "专业特长",
            "type": "textarea"
          },
          {
            "name": "schedule",
            "displayName": "出诊时间",
            "type": "json"
          }
        ]
      }
    ],
    "relationships": [
      {
        "name": "patient_appointments",
        "sourceEntity": "patient",
        "targetEntity": "appointment",
        "type": "one-to-many",
        "description": "患者可以有多个预约记录"
      },
      {
        "name": "doctor_appointments",
        "sourceEntity": "doctor",
        "targetEntity": "appointment",
        "type": "one-to-many",
        "description": "医生可以有多个预约记录"
      }
    ],
    "implementation_notes": [
      "建议为患者信息添加数据加密保护",
      "医生排班信息可以使用JSON格式存储灵活的时间安排",
      "考虑添加患者分组功能以便管理"
    ]
  }
}
```

---

### 5. AI推荐工作流设计

**POST** `/api/ai/suggest-workflows`

#### 描述
基于业务需求AI推荐合适的工作流设计方案。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "business_process": "string",
  "entities": ["string"],
  "requirements": ["string"]
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/suggest-workflows" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "business_process": "订单处理流程",
    "entities": ["order", "product", "customer"],
    "requirements": ["自动化处理", "多级审批", "异常处理"]
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "工作流设计推荐完成",
  "data": {
    "suggested_workflows": [
      {
        "name": "标准订单处理流程",
        "description": "适用于大部分订单的标准处理流程",
        "priority": "high",
        "steps": [
          {
            "name": "订单创建",
            "type": "start",
            "auto": true
          },
          {
            "name": "库存检查",
            "type": "condition",
            "auto": true
          }
        ],
        "estimated_time": "2小时",
        "complexity": "medium"
      }
    ]
  }
}
```

---

### 6. AI推荐表单设计

**POST** `/api/ai/suggest-forms`

#### 描述
基于实体定义AI推荐合适的表单设计方案。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "entity": "string",
  "form_type": "string",
  "user_type": "string"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/suggest-forms" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "entity": "product",
    "form_type": "create",
    "user_type": "admin"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "表单设计推荐完成",
  "data": {
    "suggested_forms": [
      {
        "name": "商品创建表单",
        "layout": "horizontal",
        "sections": [
          {
            "title": "基本信息",
            "fields": ["name", "price", "category"]
          }
        ],
        "validation_rules": {
          "name": "required|max:100",
          "price": "required|numeric|min:0"
        }
      }
    ]
  }
}
```

---

### 7. AI推荐API设计

**POST** `/api/ai/suggest-apis`

#### 描述
基于业务需求AI推荐合适的API设计方案。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "entities": ["string"],
  "operations": ["string"],
  "api_style": "string"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/suggest-apis" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "entities": ["product", "order"],
    "operations": ["CRUD", "search", "analytics"],
    "api_style": "RESTful"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "API设计推荐完成",
  "data": {
    "suggested_apis": [
      {
        "entity": "product",
        "endpoints": [
          {
            "method": "GET",
            "path": "/api/products",
            "description": "获取商品列表",
            "parameters": ["page", "limit", "category"]
          },
          {
            "method": "POST",
            "path": "/api/products",
            "description": "创建新商品",
            "body_schema": {
              "name": "string",
              "price": "number"
            }
          }
        ]
      }
    ]
  }
}
```

---

### 8. AI生成amis schema

**POST** `/api/ai/generate-schema`

#### 描述
基于用户需求和实体配置，AI自动生成对应的amis schema配置。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "intent": "string",
  "entity": "string",
  "action": "string",
  "parameters": {},
  "ui_preferences": {
    "layout": "string",
    "theme": "string",
    "responsive": boolean
  }
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/ai/generate-schema" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "intent": "create_form",
    "entity": "product",
    "action": "create",
    "parameters": {
      "fields": ["name", "price", "category", "description"],
      "validation": true
    },
    "ui_preferences": {
      "layout": "horizontal",
      "theme": "cxd",
      "responsive": true
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "Schema生成成功",
  "data": {
    "schema": {
      "type": "page",
      "title": "添加商品",
      "body": [
        {
          "type": "form",
          "title": "商品信息",
          "mode": "horizontal",
          "api": {
            "method": "POST",
            "url": "/api/entities/product/data"
          },
          "body": [
            {
              "type": "input-text",
              "name": "name",
              "label": "商品名称",
              "required": true,
              "placeholder": "请输入商品名称",
              "validations": {
                "isLength": {
                  "args": [1, 100],
                  "message": "商品名称长度必须在1-100个字符之间"
                }
              }
            },
            {
              "type": "input-number",
              "name": "price",
              "label": "商品价格",
              "required": true,
              "min": 0.01,
              "precision": 2,
              "prefix": "¥"
            },
            {
              "type": "select",
              "name": "category",
              "label": "商品分类",
              "required": true,
              "options": [
                {"label": "电子产品", "value": "electronics"},
                {"label": "服装", "value": "clothing"},
                {"label": "食品", "value": "food"}
              ]
            },
            {
              "type": "textarea",
              "name": "description",
              "label": "商品描述",
              "placeholder": "请输入商品描述",
              "maxLength": 500
            }
          ],
          "actions": [
            {
              "type": "submit",
              "label": "保存",
              "primary": true
            },
            {
              "type": "reset",
              "label": "重置"
            }
          ]
        }
      ]
    },
    "metadata": {
      "generated_by": "ai",
      "entity": "product",
      "action": "create",
      "version": "1.0.0",
      "responsive": true,
      "estimated_render_time": "< 100ms"
    },
    "optimizations": [
      "使用了响应式布局适配移动端",
      "添加了表单验证提升用户体验",
      "使用了合适的输入组件类型"
    ]
  }
}
```

---

## 📝 AI能力说明

### 自然语言理解
- **中文支持**：完整支持中文语音和文本理解
- **意图识别**：准确识别用户的操作意图
- **实体提取**：从命令中提取关键业务实体
- **上下文理解**：结合历史对话理解当前需求

### 智能推荐
- **模板推荐**：基于业务需求推荐最适合的模板
- **实体设计**：推荐合理的数据结构设计
- **工作流设计**：推荐高效的业务流程
- **界面生成**：生成符合用户习惯的界面

### 学习优化
- **使用反馈**：根据用户使用情况优化推荐
- **配置学习**：学习用户的配置偏好
- **错误纠正**：自动纠正常见的配置错误

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| intent_not_recognized | 400 | 无法理解用户意图 | 重新表述命令或提供更多上下文 |
| ai_service_unavailable | 503 | AI服务不可用 | 稍后重试或联系管理员 |
| insufficient_context | 400 | 上下文信息不足 | 提供更多背景信息 |
| schema_generation_failed | 500 | Schema生成失败 | 检查实体配置是否完整 |
| recommendation_failed | 500 | 推荐生成失败 | 简化需求描述重新尝试 |
| language_not_supported | 400 | 不支持的语言 | 使用支持的语言（中文/英文） |
