"""
测试阿里云百炼API连接
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from app.core.ai.bailian_client import bailian_client

async def test_api():
    try:
        print('🔧 测试阿里云百炼API连接...')
        health = bailian_client.get_health_status()
        print(f'健康状态: {health}')
        
        if health['status'] == 'healthy':
            print('✅ API密钥配置正确')
            
            # 测试简单的聊天完成
            result = await bailian_client.chat_completion(
                messages=[{'role': 'user', 'content': '你好，请回复一个简单的问候'}],
                temperature=0.1,
                max_tokens=50
            )
            print(f'API调用结果: {result.get("success", False)}')
            if result.get('success'):
                print(f'响应内容: {result.get("content", "")}')
            else:
                print(f'错误信息: {result.get("error", "未知错误")}')
        else:
            print('❌ API密钥配置有问题')
    except Exception as e:
        print(f'❌ 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_api())
