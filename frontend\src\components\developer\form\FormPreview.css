/* FormPreview.css */

.form-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e8e9ea;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1f2937;
}

.preview-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.preview-form-card {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-description {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.preview-form .ant-form-item {
  margin-bottom: 20px;
}

.tabs-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.section-content {
  padding: 24px;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f3f4f6;
  text-align: center;
}

.preview-footer {
  background: white;
  border-top: 1px solid #e8e9ea;
  padding: 16px 24px;
}

.form-info {
  display: flex;
  gap: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.info-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.form-section .ant-collapse-header {
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
}

.form-section .ant-collapse-content {
  border: none;
  background: transparent;
}
