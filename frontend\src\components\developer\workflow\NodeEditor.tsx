/**
 * 工作流节点编辑器组件 - Apple风格设计
 * 支持编辑不同类型节点的属性和配置
 */

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Card,
  Divider,
  Row,
  Col,
  Typography,
  Switch,
  InputNumber,
  Alert,
  message,
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined,
  ApiOutlined,
  FormOutlined,
  BranchesOutlined,
  NotificationOutlined,
} from '@ant-design/icons';
import type {
  WorkflowNode,
  NodeType,
  APICallConfig,
  UserInputConfig,
  ConditionConfig,
  NotificationConfig
} from '../../../types/developer/workflowTypes';
import { routeAPI } from '../../../services/developer/routeAPI';
import type { APIRoute } from '../../../types/developer/route';
import './NodeEditor.css';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface NodeEditorProps {
  node: WorkflowNode;
  onSave: (updatedNode: WorkflowNode) => void;
  onCancel: () => void;
}

const NodeEditor: React.FC<NodeEditorProps> = ({ node, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [apiRoutes, setApiRoutes] = useState<APIRoute[]>([]);
  const [loadingRoutes, setLoadingRoutes] = useState(false);

  // 获取API路由列表
  const fetchApiRoutes = async () => {
    if (node.type !== 'api_call') return;

    setLoadingRoutes(true);
    try {
      const response = await routeAPI.getRoutes({ status: 'active' });
      if (response.code === 200 && response.data) {
        setApiRoutes(response.data.routes || []);
      } else {
        message.error(response.message || '获取API路由列表失败');
      }
    } catch (error) {
      console.error('Failed to fetch API routes:', error);
      message.error('获取API路由列表失败');
    } finally {
      setLoadingRoutes(false);
    }
  };

  // 初始化表单数据
  useEffect(() => {
    const config = node.config as any;
    const formValues: any = {
      name: node.name,
      description: node.description,
      ai_purpose: node.ai_instructions.purpose,
      ai_action: node.ai_instructions.action,
      ai_user_message: node.ai_instructions.user_message,
      ai_success_handling: node.ai_instructions.success_handling,
      ai_error_handling: node.ai_instructions.error_handling,
    };

    // 根据节点类型设置特定字段
    if (node.type === 'api_call' && config) {
      formValues.api_id = config.api_id;
      formValues.api_endpoint = config.api_endpoint;
      formValues.method = config.method;
      formValues.parameters = JSON.stringify(config.parameters || {}, null, 2);
      // 获取API路由列表
      fetchApiRoutes();
    } else if (node.type === 'user_input' && config) {
      formValues.input_type = config.input_type;
      formValues.placeholder = config.placeholder;
      formValues.default_value = config.default_value;
      formValues.multiple = config.multiple;
      formValues.required = config.validation?.required;
      formValues.min = config.validation?.min;
      formValues.max = config.validation?.max;
      formValues.pattern = config.validation?.pattern;
      formValues.validation_message = config.validation?.message;
      formValues.form_schema = config.form_schema ? JSON.stringify(config.form_schema, null, 2) : '';
      formValues.choices = config.choices;
    } else if (node.type === 'condition' && config) {
      formValues.conditions = config.conditions || [];
      formValues.logic = config.logic || 'and';
      formValues.evaluation_expression = config.evaluation_expression;
    } else {
      // 其他节点类型的配置
      Object.assign(formValues, config);
    }

    form.setFieldsValue(formValues);
  }, [node, form]);

  // 保存节点
  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      const updatedNode: WorkflowNode = {
        ...node,
        name: values.name,
        description: values.description,
        config: extractConfig(values, node.type),
        ai_instructions: {
          ...node.ai_instructions,
          purpose: values.ai_purpose,
          action: values.ai_action,
          user_message: values.ai_user_message,
          success_handling: values.ai_success_handling,
          error_handling: values.ai_error_handling,
        },
      };

      onSave(updatedNode);
    } catch (error) {
      console.error('Form validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 根据节点类型提取配置
  const extractConfig = (values: any, nodeType: NodeType) => {
    switch (nodeType) {
      case 'api_call':
        return {
          api_id: values.api_id,
          api_endpoint: values.api_endpoint,
          method: values.method,
          parameters: values.parameters ? JSON.parse(values.parameters || '{}') : {},
        } as APICallConfig;
      
      case 'user_input':
        return {
          input_type: values.input_type,
          form_schema: values.form_schema ? JSON.parse(values.form_schema || '{}') : {},
          choices: values.choices || [],
          validation: {
            required: values.required || false,
            min: values.min,
            max: values.max,
            pattern: values.pattern,
            message: values.validation_message,
          },
          placeholder: values.placeholder,
          default_value: values.default_value,
          multiple: values.multiple || false,
        } as UserInputConfig;

      case 'condition':
        return {
          conditions: values.conditions || [],
          logic: values.logic || 'and',
          evaluation_expression: values.evaluation_expression,
        } as ConditionConfig;
      
      case 'notification':
        return {
          message: values.message,
          type: values.notification_type,
          channels: values.channels || [],
        } as NotificationConfig;
      
      default:
        return {};
    }
  };

  // 渲染节点类型特定的配置表单
  // 处理API路由选择变化
  const handleApiRouteChange = (routeId: string) => {
    const selectedRoute = apiRoutes.find(route => route.id === routeId);
    if (selectedRoute) {
      form.setFieldsValue({
        api_id: selectedRoute.api_id,
        api_endpoint: selectedRoute.endpoint,
        method: selectedRoute.method,
      });
    }
  };

  const renderNodeSpecificConfig = () => {
    switch (node.type) {
      case 'api_call':
        return (
          <Card title="API调用配置" size="small" className="config-section">
            <Form.Item
              name="selected_route"
              label="选择API路由"
              rules={[{ required: true, message: '请选择API路由' }]}
            >
              <Select
                placeholder="选择要调用的API路由"
                loading={loadingRoutes}
                onChange={handleApiRouteChange}
                showSearch
                filterOption={(input, option) =>
                  (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {apiRoutes.map(route => (
                  <Option key={route.id} value={route.id}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>{route.name}</span>
                      <span style={{ color: '#666', fontSize: '12px' }}>
                        {route.method} {route.endpoint}
                      </span>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="api_id"
                  label="API标识"
                  rules={[{ required: true, message: '请输入API标识' }]}
                >
                  <Input placeholder="例如: user_search_api" disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="method"
                  label="请求方法"
                  rules={[{ required: true, message: '请选择请求方法' }]}
                >
                  <Select disabled>
                    <Option value="GET">GET</Option>
                    <Option value="POST">POST</Option>
                    <Option value="PUT">PUT</Option>
                    <Option value="DELETE">DELETE</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="api_endpoint"
              label="API端点"
              rules={[{ required: true, message: '请输入API端点' }]}
            >
              <Input placeholder="例如: /api/users/search" disabled />
            </Form.Item>

            <Form.Item
              name="parameters"
              label="请求参数 (JSON)"
              help="使用 {{}} 语法引用前置节点的输出，例如: {&quot;user_id&quot;: &quot;{{user_input.value}}&quot;}"
            >
              <Input.TextArea
                rows={4}
                placeholder='{"key": "{{inputs.value}}"}'
              />
            </Form.Item>

            <Alert
              message="API路由信息"
              description="选择API路由后，API标识、请求方法和端点将自动填充。您只需要配置请求参数即可。"
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          </Card>
        );

      case 'user_input':
        return (
          <Card title="用户输入配置" size="small" className="config-section">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="input_type"
                  label="输入类型"
                  rules={[{ required: true, message: '请选择输入类型' }]}
                >
                  <Select>
                    <Option value="text">文本输入</Option>
                    <Option value="number">数字输入</Option>
                    <Option value="choice">单选</Option>
                    <Option value="form">表单输入</Option>
                    <Option value="date">日期选择</Option>
                    <Option value="file">文件上传</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="multiple"
                  label="多选模式"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="placeholder"
              label="提示文本"
            >
              <Input placeholder="请输入提示文本" />
            </Form.Item>

            <Form.Item
              name="default_value"
              label="默认值"
            >
              <Input placeholder="设置默认值" />
            </Form.Item>

            <Divider orientation="left" plain>验证规则</Divider>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="required"
                  label="必填"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="min"
                  label="最小值/长度"
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="max"
                  label="最大值/长度"
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="pattern"
              label="正则表达式"
            >
              <Input placeholder="^[a-zA-Z0-9]+$" />
            </Form.Item>

            <Form.Item
              name="validation_message"
              label="验证失败提示"
            >
              <Input placeholder="输入格式不正确" />
            </Form.Item>

            <Form.Item
              name="form_schema"
              label="高级配置 (JSON)"
            >
              <TextArea
                rows={4}
                placeholder='{"fields": [{"name": "username", "type": "string", "required": true}]}'
              />
            </Form.Item>
          </Card>
        );

      case 'condition':
        return (
          <Card title="条件判断配置" size="small" className="config-section">
            <Form.Item
              name="logic"
              label="条件逻辑"
            >
              <Select defaultValue="and">
                <Option value="and">所有条件都满足 (AND)</Option>
                <Option value="or">任一条件满足 (OR)</Option>
              </Select>
            </Form.Item>

            <Divider orientation="left" plain>条件设置</Divider>

            <Form.List name="conditions">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Card key={key} size="small" style={{ marginBottom: 16 }}>
                      <Row gutter={16}>
                        <Col span={6}>
                          <Form.Item
                            {...restField}
                            name={[name, 'field']}
                            label="字段"
                            rules={[{ required: true, message: '请输入字段名' }]}
                          >
                            <Input placeholder="user.age" />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...restField}
                            name={[name, 'operator']}
                            label="操作符"
                            rules={[{ required: true, message: '请选择操作符' }]}
                          >
                            <Select>
                              <Option value="equals">等于</Option>
                              <Option value="not_equals">不等于</Option>
                              <Option value="greater_than">大于</Option>
                              <Option value="less_than">小于</Option>
                              <Option value="contains">包含</Option>
                              <Option value="not_contains">不包含</Option>
                              <Option value="exists">存在</Option>
                              <Option value="not_exists">不存在</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            label="比较值"
                          >
                            <Input placeholder="18" />
                          </Form.Item>
                        </Col>
                        <Col span={4}>
                          <Form.Item
                            {...restField}
                            name={[name, 'data_type']}
                            label="数据类型"
                          >
                            <Select defaultValue="string">
                              <Option value="string">文本</Option>
                              <Option value="number">数字</Option>
                              <Option value="boolean">布尔</Option>
                              <Option value="array">数组</Option>
                              <Option value="object">对象</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Button
                            type="text"
                            danger
                            onClick={() => remove(name)}
                            style={{ marginTop: 30 }}
                          >
                            删除
                          </Button>
                        </Col>
                      </Row>
                    </Card>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block>
                      + 添加条件
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>

            <Divider orientation="left" plain>高级设置</Divider>
            <Form.Item
              name="evaluation_expression"
              label="自定义表达式"
            >
              <TextArea
                rows={3}
                placeholder="{{nodes.search_api.outputs.count}} > 0 && {{user.role}} === 'admin'"
              />
            </Form.Item>
            <Alert
              message="表达式语法提示"
              description="使用 {{}} 语法引用节点输出，支持 JavaScript 表达式。如果设置了自定义表达式，将忽略上面的条件设置。"
              type="info"
              showIcon
              className="syntax-tip"
            />
          </Card>
        );

      case 'notification':
        return (
          <Card title="通知配置" size="small" className="config-section">
            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="message"
                  label="通知消息"
                  rules={[{ required: true, message: '请输入通知消息' }]}
                >
                  <TextArea 
                    rows={3} 
                    placeholder="通知用户的消息内容"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="notification_type"
                  label="消息类型"
                  rules={[{ required: true, message: '请选择消息类型' }]}
                >
                  <Select>
                    <Option value="info">信息</Option>
                    <Option value="success">成功</Option>
                    <Option value="warning">警告</Option>
                    <Option value="error">错误</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="node-editor">
      <Form form={form} layout="vertical" className="editor-form">
        {/* 基本信息 */}
        <Card title="基本信息" size="small" className="config-section">
          <Form.Item
            name="name"
            label="节点名称"
            rules={[{ required: true, message: '请输入节点名称' }]}
          >
            <Input placeholder="请输入节点名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="节点描述"
            rules={[{ required: true, message: '请输入节点描述' }]}
          >
            <TextArea rows={2} placeholder="描述节点的功能和用途" />
          </Form.Item>
        </Card>

        {/* 节点特定配置 */}
        {renderNodeSpecificConfig()}

        {/* AI指令配置 */}
        <Card title="AI执行指令" size="small" className="config-section">
          <Form.Item
            name="ai_purpose"
            label="执行目的"
            rules={[{ required: true, message: '请输入执行目的' }]}
          >
            <TextArea rows={2} placeholder="描述这个节点的执行目的" />
          </Form.Item>
          <Form.Item
            name="ai_action"
            label="执行动作"
            rules={[{ required: true, message: '请输入执行动作' }]}
          >
            <TextArea rows={2} placeholder="描述AI应该执行的具体动作" />
          </Form.Item>
          <Form.Item
            name="ai_user_message"
            label="用户消息"
          >
            <TextArea rows={2} placeholder="向用户显示的消息" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ai_success_handling"
                label="成功处理"
              >
                <TextArea rows={2} placeholder="成功时的处理方式" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ai_error_handling"
                label="错误处理"
              >
                <TextArea rows={2} placeholder="失败时的处理方式" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <div className="editor-actions">
          <Space>
            <Button onClick={onCancel}>
              <CloseOutlined /> 取消
            </Button>
            <Button 
              type="primary" 
              loading={loading}
              onClick={handleSave}
            >
              <SaveOutlined /> 保存
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default NodeEditor; 