#!/usr/bin/env python3
"""
修复数据库中的工作流状态枚举值
将小写的枚举值转换为大写
"""
import sys
import os
sys.path.append('.')

from sqlalchemy import create_engine, text
from app.config import Settings
settings = Settings()

def fix_enum_values():
    """修复数据库中的枚举值"""
    print('🔧 开始修复数据库中的工作流状态枚举值...')
    
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        trans = conn.begin()
        try:
            # 检查当前数据
            print('📊 检查当前数据...')
            result = conn.execute(text('SELECT id, name, status FROM workflows ORDER BY created_at DESC'))
            rows = result.fetchall()
            print(f'📋 当前数据库中有 {len(rows)} 条工作流记录:')
            
            for row in rows:
                print(f'  - {row[0]}: {row[1]} -> {row[2]}')
            
            # 更新枚举值映射
            status_mapping = {
                'draft': 'DRAFT',
                'active': 'ACTIVE', 
                'inactive': 'INACTIVE',
                'archived': 'ARCHIVED'
            }
            
            total_updated = 0
            print('\n🔄 开始更新枚举值...')
            
            for old_status, new_status in status_mapping.items():
                result = conn.execute(
                    text('UPDATE workflows SET status = :new_status WHERE status = :old_status'),
                    {'old_status': old_status, 'new_status': new_status}
                )
                if result.rowcount > 0:
                    print(f'✅ 更新了 {result.rowcount} 条记录：{old_status} -> {new_status}')
                    total_updated += result.rowcount
            
            if total_updated == 0:
                print('ℹ️ 没有需要更新的记录，所有枚举值已经是正确的格式')
            else:
                print(f'🎯 总共更新了 {total_updated} 条记录')
            
            # 提交事务
            trans.commit()
            print('✅ 事务提交成功')
            
            # 验证更新结果
            print('\n📊 验证更新结果...')
            result = conn.execute(text('SELECT id, name, status FROM workflows ORDER BY created_at DESC'))
            rows = result.fetchall()
            print(f'📋 更新后的数据：')
            for row in rows:
                print(f'  - {row[0]}: {row[1]} -> {row[2]}')
            
            print('\n🎉 工作流状态枚举值修复完成！')
            return True
            
        except Exception as e:
            trans.rollback()
            print(f'❌ 修复失败，已回滚：{e}')
            import traceback
            print(f'📋 错误堆栈：{traceback.format_exc()}')
            return False

if __name__ == "__main__":
    success = fix_enum_values()
    sys.exit(0 if success else 1)
