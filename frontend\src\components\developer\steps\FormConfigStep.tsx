/**
 * 表单配置步骤组件
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  message,
  Spin,
  Empty,
  Tabs,
  Row,
  Col,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  DownloadOutlined,
  FormOutlined,
  TableOutlined
} from '@ant-design/icons';
import { FormAPI } from '../../../services/developer/formAPI';
import { entityAPI } from '../../../services/developer/entityAPI';
import type { FormConfig, FormTemplate } from '../../../types/developer/form';
import type { Entity } from '../../../types/developer/entity';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface FormConfigStepProps {
  onNext: () => void;
  onPrev: () => void;
}

const FormConfigStep: React.FC<FormConfigStepProps> = ({ onNext, onPrev }) => {
  const [loading, setLoading] = useState(false);
  const [forms, setForms] = useState<FormConfig[]>([]);
  const [entities, setEntities] = useState<Entity[]>([]);
  const [templates, setTemplates] = useState<FormTemplate[]>([]);
  const [selectedForm, setSelectedForm] = useState<FormConfig | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  // 加载数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [formsData, entitiesData, templatesData] = await Promise.all([
        FormAPI.getForms(),
        entityAPI.getEntitiesList(),
        FormAPI.getFormTemplates()
      ]);
      setForms(formsData.forms);
      setEntities(entitiesData.data.entities);
      setTemplates(templatesData);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建新表单
  const handleCreateForm = () => {
    setSelectedForm(null);
    setShowEditor(true);
  };

  // 从模板创建表单
  const handleCreateFromTemplate = () => {
    setShowTemplates(true);
  };

  // 编辑表单
  const handleEditForm = (form: FormConfig) => {
    setSelectedForm(form);
    setShowEditor(true);
  };

  // 预览表单
  const handlePreviewForm = (form: FormConfig) => {
    setSelectedForm(form);
    setShowPreview(true);
  };

  // 复制表单
  const handleDuplicateForm = async (form: FormConfig) => {
    try {
      setLoading(true);
      await FormAPI.duplicateForm(form.id, {
        name: `${form.name} (副本)`,
        description: `${form.description} - 副本`
      });
      message.success('表单复制成功');
      loadData();
    } catch (error) {
      console.error('复制表单失败:', error);
      message.error('复制表单失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除表单
  const handleDeleteForm = async (form: FormConfig) => {
    try {
      setLoading(true);
      await FormAPI.deleteForm(form.id);
      message.success('表单删除成功');
      loadData();
    } catch (error) {
      console.error('删除表单失败:', error);
      message.error('删除表单失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出表单
  const handleExportForm = async (form: FormConfig) => {
    try {
      const blob = await FormAPI.exportForm(form.id, 'json');
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${form.name}.json`;
      a.click();
      URL.revokeObjectURL(url);
      message.success('表单导出成功');
    } catch (error) {
      console.error('导出表单失败:', error);
      message.error('导出表单失败');
    }
  };

  // 保存表单配置
  const handleSaveForm = async (formData: any) => {
    try {
      setLoading(true);
      if (selectedForm) {
        await FormAPI.updateForm(selectedForm.id, formData);
        message.success('表单更新成功');
      } else {
        await FormAPI.createForm(formData);
        message.success('表单创建成功');
      }
      setShowEditor(false);
      loadData();
    } catch (error) {
      console.error('保存表单失败:', error);
      message.error('保存表单失败');
    } finally {
      setLoading(false);
    }
  };

  // 从模板创建表单
  const handleCreateFromTemplateConfirm = async (template: FormTemplate, formData: any) => {
    try {
      setLoading(true);
      await FormAPI.createFormFromTemplate(template.id, formData);
      message.success('从模板创建表单成功');
      setShowTemplates(false);
      loadData();
    } catch (error) {
      console.error('从模板创建表单失败:', error);
      message.error('从模板创建表单失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染表单卡片
  const renderFormCard = (form: FormConfig) => (
    <Card
      key={form.id}
      title={
        <Space>
          <FormOutlined />
          <span>{form.name}</span>
        </Space>
      }
      extra={
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handlePreviewForm(form)}
            title="预览"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditForm(form)}
            title="编辑"
          />
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => handleDuplicateForm(form)}
            title="复制"
          />
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => handleExportForm(form)}
            title="导出"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteForm(form)}
            title="删除"
          />
        </Space>
      }
      className="form-card"
    >
      <div className="form-card-content">
        <Text type="secondary">{form.description}</Text>
        <Divider />
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>关联实体:</Text>
            <br />
            <Text>{form.entity}</Text>
          </Col>
          <Col span={8}>
            <Text strong>字段数量:</Text>
            <br />
            <Text>{form.sections.reduce((total, section) => total + section.fields.length, 0)}</Text>
          </Col>
          <Col span={8}>
            <Text strong>分组数量:</Text>
            <br />
            <Text>{form.sections.length}</Text>
          </Col>
        </Row>
      </div>
    </Card>
  );

  return (
    <div className="form-config-step">
      <div className="step-header">
        <Title level={2}>
          <FormOutlined /> 表单配置
        </Title>
        <Text type="secondary">
          配置动态表单，定义用户界面的字段布局、验证规则和权限设置
        </Text>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab} className="config-tabs">
        <TabPane tab="表单列表" key="list">
          <div className="forms-list">
            <div className="list-header">
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateForm}
                >
                  创建表单
                </Button>
                <Button
                  icon={<TableOutlined />}
                  onClick={handleCreateFromTemplate}
                >
                  从模板创建
                </Button>
              </Space>
            </div>

            <Spin spinning={loading}>
              {forms.length > 0 ? (
                <Row gutter={[16, 16]} className="forms-grid">
                  {forms.map(form => (
                    <Col key={form.id} xs={24} sm={12} lg={8}>
                      {renderFormCard(form)}
                    </Col>
                  ))}
                </Row>
              ) : (
                <Empty
                  description="暂无表单配置"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button type="primary" onClick={handleCreateForm}>
                    创建第一个表单
                  </Button>
                </Empty>
              )}
            </Spin>
          </div>
        </TabPane>

        <TabPane tab="配置指南" key="guide">
          <Card title="表单配置指南">
            <div className="config-guide">
              <Title level={4}>1. 创建表单</Title>
              <Text>选择关联的实体，定义表单的基本信息和布局方式。</Text>

              <Title level={4}>2. 配置字段</Title>
              <Text>为每个实体字段选择合适的显示类型，设置验证规则和权限。</Text>

              <Title level={4}>3. 组织分组</Title>
              <Text>将相关字段组织到不同的分组中，提升用户体验。</Text>

              <Title level={4}>4. 预览测试</Title>
              <Text>使用预览功能测试表单的显示效果和交互逻辑。</Text>

              <Title level={4}>5. 权限配置</Title>
              <Text>为不同角色配置表单的访问权限和操作权限。</Text>
            </div>
          </Card>
        </TabPane>
      </Tabs>

      <div className="step-actions">
        <Space>
          <Button onClick={onPrev}>上一步</Button>
          <Button type="primary" onClick={onNext}>
            下一步
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default FormConfigStep;