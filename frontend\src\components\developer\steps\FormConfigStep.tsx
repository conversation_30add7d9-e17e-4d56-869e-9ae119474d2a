/**
 * 表单配置步骤组件
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  Layout,
  List,
  Modal,
  Typography,
  Space,
  message,
  Empty,
  Tooltip,
  Spin
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  FormOutlined,
  SettingOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { formAPI, FormListItem, FormConfig } from '../../../services/developer/formAPI';
import { entityAPI } from '../../../services/developer/entityAPI';
import { Entity } from '../../../types/developer/entity';
import FormDesigner from '../form/FormDesigner';
import FormPreview from '../form/FormPreview';
import './FormConfigStep.css';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;
const { confirm } = Modal;

interface FormConfigStepProps {
  onNext?: () => void;
  onPrev?: () => void;
}

const FormConfigStep: React.FC<FormConfigStepProps> = ({ onNext, onPrev }) => {
  const [loading, setLoading] = useState(false);
  const [forms, setForms] = useState<FormListItem[]>([]);
  const [entities, setEntities] = useState<Entity[]>([]);
  const [selectedForm, setSelectedForm] = useState<FormListItem | null>(null);
  const [selectedFormConfig, setSelectedFormConfig] = useState<FormConfig | null>(null);
  const [isDesignerVisible, setIsDesignerVisible] = useState(false);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [designerMode, setDesignerMode] = useState<'create' | 'edit'>('create');
  const [refreshKey, setRefreshKey] = useState(0);

  const formDesignerRef = useRef<any>(null);

  // 初始化数据
  useEffect(() => {
    loadInitialData();
  }, [refreshKey]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // 并发加载实体列表和表单列表
      const [entitiesResponse, formsResponse] = await Promise.all([
        entityAPI.getEntitiesList({ include_fields: false }),
        formAPI.getForms()
      ]);

      if (entitiesResponse.code === 200) {
        setEntities(entitiesResponse.data.entities);
      }

      if (formsResponse.code === 200) {
        setForms(formsResponse.data.forms);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建新表单
  const handleCreateForm = () => {
    if (entities.length === 0) {
      message.warning('请先创建实体模型后再配置表单');
      return;
    }
    setSelectedForm(null);
    setSelectedFormConfig(null);
    setDesignerMode('create');
    setIsDesignerVisible(true);
  };

  // 编辑表单
  const handleEditForm = async (form: FormListItem) => {
    setLoading(true);
    try {
      const response = await formAPI.getForm(form.id);
      if (response.code === 200) {
        setSelectedForm(form);
        setSelectedFormConfig(response.data.form);
        setDesignerMode('edit');
        setIsDesignerVisible(true);
      } else {
        message.error('获取表单配置失败');
      }
    } catch (error) {
      console.error('获取表单配置失败:', error);
      message.error('获取表单配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 预览表单
  const handlePreviewForm = async (form: FormListItem) => {
    setLoading(true);
    try {
      const response = await formAPI.getForm(form.id);
      if (response.code === 200) {
        setSelectedForm(form);
        setSelectedFormConfig(response.data.form);
        setIsPreviewVisible(true);
      } else {
        message.error('获取表单配置失败');
      }
    } catch (error) {
      console.error('获取表单配置失败:', error);
      message.error('获取表单配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除表单
  const handleDeleteForm = (form: FormListItem) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除表单 "${form.name}" 吗？此操作不可恢复。`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const response = await formAPI.deleteForm(form.id);
          if (response.code === 200) {
            message.success('表单删除成功');
            setRefreshKey(prev => prev + 1);
          } else {
            message.error('表单删除失败');
          }
        } catch (error) {
          console.error('删除表单失败:', error);
          message.error('删除表单失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 表单设计器保存成功回调
  const handleDesignerSave = () => {
    setIsDesignerVisible(false);
    setRefreshKey(prev => prev + 1);
    message.success(`表单${designerMode === 'create' ? '创建' : '更新'}成功`);
  };

  // 表单设计器取消回调
  const handleDesignerCancel = () => {
    setIsDesignerVisible(false);
  };

  // 预览关闭回调
  const handlePreviewClose = () => {
    setIsPreviewVisible(false);
  };

  // 继续下一步
  const handleNext = () => {
    if (forms.length === 0) {
      message.warning('请至少配置一个表单后再继续');
      return;
    }
    onNext?.();
  };

  // 获取实体显示名称
  const getEntityDisplayName = (entityName: string) => {
    const entity = entities.find(e => e.name === entityName);
    return entity?.displayName || entityName;
  };

  return (
    <div className="form-config-step">
      <div className="step-header">
        <div className="header-content">
          <div className="header-left">
            <div className="step-icon">
              <FormOutlined />
            </div>
            <div className="step-info">
              <Title level={3}>表单配置</Title>
              <Text type="secondary">为实体配置用户交互表单，支持动态布局和字段验证</Text>
            </div>
          </div>
          <div className="header-right">
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateForm}
                disabled={entities.length === 0}
              >
                创建表单
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <div className="step-content">
        <Spin spinning={loading}>
          {forms.length === 0 ? (
            <div className="empty-state">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <div>
                    <Text>还没有配置任何表单</Text>
                    <br />
                    <Text type="secondary">表单是用户与系统交互的界面，用于数据的录入和编辑</Text>
                  </div>
                }
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateForm}
                  disabled={entities.length === 0}
                >
                  {entities.length === 0 ? '请先创建实体模型' : '创建第一个表单'}
                </Button>
              </Empty>
            </div>
          ) : (
            <div className="forms-list">
              <List
                grid={{
                  gutter: 16,
                  xs: 1,
                  sm: 2,
                  md: 2,
                  lg: 3,
                  xl: 3,
                  xxl: 4,
                }}
                dataSource={forms}
                renderItem={(form) => (
                  <List.Item>
                    <Card
                      className="form-card"
                      title={
                        <div className="card-title">
                          <FormOutlined />
                          <span>{form.name}</span>
                        </div>
                      }
                      extra={
                        <Space>
                          <Tooltip title="预览">
                            <Button
                              type="text"
                              size="small"
                              icon={<EyeOutlined />}
                              onClick={() => handlePreviewForm(form)}
                            />
                          </Tooltip>
                          <Tooltip title="编辑">
                            <Button
                              type="text"
                              size="small"
                              icon={<EditOutlined />}
                              onClick={() => handleEditForm(form)}
                            />
                          </Tooltip>
                          <Tooltip title="删除">
                            <Button
                              type="text"
                              size="small"
                              icon={<DeleteOutlined />}
                              danger
                              onClick={() => handleDeleteForm(form)}
                            />
                          </Tooltip>
                        </Space>
                      }
                      actions={[
                        <div key="entity" className="card-info">
                          <Text type="secondary">实体:</Text>
                          <Text>{getEntityDisplayName(form.entity)}</Text>
                        </div>,
                        <div key="fields" className="card-info">
                          <Text type="secondary">字段:</Text>
                          <Text>{form.field_count}</Text>
                        </div>
                      ]}
                    >
                      <div className="card-content">
                        <Text type="secondary">{form.description}</Text>
                        <div className="card-meta">
                          <div className="meta-item">
                            <Text type="secondary" className="meta-label">
                              分组数量:
                            </Text>
                            <Text>{form.section_count}</Text>
                          </div>
                          <div className="meta-item">
                            <Text type="secondary" className="meta-label">
                              创建时间:
                            </Text>
                            <Text>{new Date(form.created_at).toLocaleDateString()}</Text>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </List.Item>
                )}
              />
            </div>
          )}
        </Spin>
      </div>

      <div className="step-footer">
        <div className="footer-content">
          <Button onClick={onPrev}>
            上一步
          </Button>
          <div className="footer-right">
            <Space>
              <Button
                type="primary"
                onClick={handleNext}
                disabled={forms.length === 0}
              >
                下一步
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 表单设计器模态框 */}
      <Modal
        title={designerMode === 'create' ? '创建表单' : '编辑表单'}
        open={isDesignerVisible}
        onCancel={handleDesignerCancel}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        <FormDesigner
          ref={formDesignerRef}
          mode={designerMode}
          formConfig={selectedFormConfig}
          entities={entities}
          onSave={handleDesignerSave}
          onCancel={handleDesignerCancel}
        />
      </Modal>

      {/* 表单预览模态框 */}
      <Modal
        title={`预览表单: ${selectedForm?.name}`}
        open={isPreviewVisible}
        onCancel={handlePreviewClose}
        width="80%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        {selectedFormConfig && (
          <FormPreview
            formConfig={selectedFormConfig}
            onClose={handlePreviewClose}
          />
        )}
      </Modal>
    </div>
  );
};

export default FormConfigStep;
