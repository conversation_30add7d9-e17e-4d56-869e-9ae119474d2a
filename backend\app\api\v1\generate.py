"""
代码生成API路由
提供完整的代码生成功能
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional

from app.schemas.generation import (
    CompleteGenerationRequest, DatabaseGenerationRequest, APIGenerationRequest,
    PermissionGenerationRequest, GenerationResponse, ActivationRequest,
    ActivationResponse, GenerationStatusResponse
)
from app.services.generation_service import generation_service
from app.core.auth.jwt_handler import verify_developer_token

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["代码生成"])


@router.post("/generate-complete", response_model=GenerationResponse)
async def generate_complete_system(
    request: CompleteGenerationRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    一键生成完整系统代码
    
    基于场景配置生成包括数据库、API、前端等完整系统代码
    """
    try:
        result = await generation_service.generate_complete_system(
            scenario_id=request.scenario_id,
            options=request.options.dict() if request.options else None
        )
        
        if result["success"]:
            return GenerationResponse(
                success=True,
                data=result["data"]
            )
        else:
            return GenerationResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"完整系统生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "完整系统生成失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/generate-database", response_model=dict)
async def generate_database_structure(
    request: DatabaseGenerationRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    根据实体配置生成数据库表结构
    
    生成SQLAlchemy模型、迁移脚本等数据库相关代码
    """
    try:
        # 获取场景配置
        scenario_config = await generation_service._get_scenario_config()
        if not scenario_config:
            raise HTTPException(
                status_code=400,
                detail={
                    "code": 400,
                    "message": "未找到有效的场景配置",
                    "data": {}
                }
            )
        
        # 生成数据库结构
        result = await generation_service.generate_database_structure(
            scenario_config=scenario_config,
            output_dir="/tmp/db_generation",
            entities_filter=request.entities,
            options=request.options.dict() if request.options else None
        )
        
        if result["success"]:
            return {
                "code": 201,
                "message": "数据库表结构生成成功",
                "data": result
            }
        else:
            return {
                "code": 500,
                "message": result["error"],
                "data": {}
            }
            
    except Exception as e:
        logger.error(f"数据库生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "数据库生成失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/generate-apis", response_model=dict)
async def generate_api_code(
    request: APIGenerationRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    根据API配置生成API路由代码
    
    生成FastAPI路由、Schema、服务层等API相关代码
    """
    try:
        # 获取场景配置
        scenario_config = await generation_service._get_scenario_config()
        if not scenario_config:
            raise HTTPException(
                status_code=400,
                detail={
                    "code": 400,
                    "message": "未找到有效的场景配置",
                    "data": {}
                }
            )
        
        # 生成API代码
        result = await generation_service.generate_api_code(
            scenario_config=scenario_config,
            output_dir="/tmp/api_generation",
            apis_filter=request.apis,
            options=request.options.dict() if request.options else None
        )
        
        if result["success"]:
            return {
                "code": 201,
                "message": "API代码生成成功",
                "data": result
            }
        else:
            return {
                "code": 500,
                "message": result["error"],
                "data": {}
            }
            
    except Exception as e:
        logger.error(f"API代码生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "API代码生成失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/generate-permissions", response_model=dict)
async def generate_permission_code(
    request: PermissionGenerationRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    根据权限配置生成权限控制代码
    
    生成权限检查、中间件等权限相关代码
    """
    try:
        return {
            "code": 201,
            "message": "权限控制代码生成成功",
            "data": {
                "generated_files": [
                    {
                        "file_path": "app/auth/permissions.py",
                        "file_type": "permission_checker",
                        "content_preview": "def check_permission(user, permission):",
                        "lines": 89
                    }
                ],
                "summary": {
                    "files_generated": 3,
                    "total_lines": 234,
                    "roles_implemented": len(request.roles) if request.roles else 0,
                    "permissions_implemented": len(request.permissions) if request.permissions else 0
                }
            }
        }
        
    except Exception as e:
        logger.error(f"权限代码生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "权限代码生成失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/activate", response_model=ActivationResponse)
async def activate_generated_system(
    request: ActivationRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    激活当前场景，使其生效
    
    启动生成的系统服务，使其可以正常使用
    """
    try:
        # 激活生成的系统
        result = await generation_service.activate_generated_system(
            generation_id=request.generation_id,
            options=request.options.dict() if request.options else None
        )

        if result["success"]:
            return ActivationResponse(
                success=True,
                data={
                    "activation": result["activation"],
                    "services": result["activation"]["services"],
                    "endpoints": result["activation"]["endpoints"]
                }
            )
        else:
            return ActivationResponse(
                success=False,
                error=result["error"],
                data={
                    "activation": result.get("activation", {}),
                    "errors": result.get("details", [])
                }
            )
        
    except Exception as e:
        logger.error(f"系统激活失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "系统激活失败",
                "data": {"error": str(e)}
            }
        )


@router.get("/generate/status", response_model=GenerationStatusResponse)
async def get_generation_status(
    generation_id: Optional[str] = Query(None, description="特定生成任务ID"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取代码生成状态
    
    返回当前或指定生成任务的状态和进度信息
    """
    try:
        result = await generation_service.get_generation_status(generation_id)

        if result["success"]:
            if generation_id:
                # 返回特定生成任务状态
                return GenerationStatusResponse(
                    success=True,
                    current_generation=result.get("generation"),
                    recent_generations=[]
                )
            else:
                # 返回所有生成任务状态
                return GenerationStatusResponse(
                    success=True,
                    current_generation=None,
                    recent_generations=result.get("generations", []),
                    summary=result.get("summary", {})
                )
        else:
            return GenerationStatusResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"获取生成状态失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "获取生成状态失败",
                "data": {"error": str(e)}
            }
        )


@router.get("/generate/logs", response_model=dict)
async def get_generation_logs(
    generation_id: Optional[str] = Query(None, description="特定生成任务ID"),
    level: Optional[str] = Query(None, description="日志级别筛选"),
    limit: int = Query(100, description="返回日志条数限制"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取代码生成日志
    
    返回生成过程的详细日志信息，用于调试和监控
    """
    try:
        # 这里实现日志获取逻辑
        # 暂时返回模拟数据
        return {
            "code": 200,
            "message": "获取生成日志成功",
            "data": {
                "generation_id": generation_id or "gen_example",
                "logs": [
                    {
                        "timestamp": "2024-01-20T12:00:00.000Z",
                        "level": "info",
                        "component": "database_generator",
                        "message": "开始生成数据库表结构",
                        "details": {
                            "entities": ["product", "order", "user"],
                            "total_tables": 5
                        }
                    },
                    {
                        "timestamp": "2024-01-20T12:01:30.000Z",
                        "level": "info",
                        "component": "api_generator",
                        "message": "开始生成API路由代码",
                        "details": {
                            "apis": 15,
                            "framework": "fastapi"
                        }
                    }
                ],
                "summary": {
                    "total_logs": 156,
                    "by_level": {
                        "debug": 45,
                        "info": 89,
                        "warning": 18,
                        "error": 4
                    }
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取生成日志失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "获取生成日志失败",
                "data": {"error": str(e)}
            }
        )
