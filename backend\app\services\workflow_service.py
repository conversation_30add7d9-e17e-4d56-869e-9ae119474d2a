"""
工作流设计模块服务 (简化版)
实现工作流定义的增删改查功能
基于API文档，只处理工作流配置，不包含执行功能
"""
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.core.database import get_db_session
from app.models.workflow import WorkflowDBModel, WorkflowStatus

def normalize_status(status_value):
    """标准化状态值，将旧的小写值转换为大写"""
    if isinstance(status_value, str):
        status_mapping = {
            'draft': 'DRAFT',
            'active': 'ACTIVE',
            'inactive': 'INACTIVE',
            'archived': 'ARCHIVED'
        }
        return status_mapping.get(status_value.lower(), status_value.upper())
    return status_value
from app.schemas.workflow import WorkflowCreateRequest, WorkflowUpdateRequest


def format_datetime(dt: datetime) -> str:
    """格式化datetime为ISO 8601格式，符合API文档要求"""
    if dt is None:
        return ""
    # 格式化为 "2024-01-20T10:00:00.000Z"
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"


class WorkflowService:
    """工作流服务 (简化版)"""
    
    def _generate_id(self, prefix: str = "workflow") -> str:
        """生成唯一ID"""
        timestamp = int(time.time() * 1000)
        return f"{prefix}_{timestamp}"
    
    def create_workflow(self, request: WorkflowCreateRequest) -> Dict[str, Any]:
        """创建工作流定义"""
        try:
            print(f"🚀 开始创建工作流: {request.name}")
            
            with get_db_session() as db:
                # 生成工作流ID
                workflow_id = self._generate_id("workflow")
                print(f"📋 生成工作流ID: {workflow_id}")
                
                # 创建工作流记录
                db_workflow = WorkflowDBModel(
                    id=workflow_id,
                    name=request.name,
                    description=request.description,
                    business_scenario=request.business_scenario,
                    status="DRAFT",
                    user_intents=request.user_intents or [],
                    trigger_keywords=request.trigger_keywords or [],
                    inputs=[input_item.model_dump() if hasattr(input_item, 'model_dump') else input_item for input_item in (request.inputs or [])],
                    nodes=[node.model_dump() if hasattr(node, 'model_dump') else node for node in (request.nodes or [])],
                    workflow_metadata=request.metadata.model_dump() if hasattr(request.metadata, 'model_dump') else (request.metadata or {}),
                    created_by="developer"
                )
                
                db.add(db_workflow)
                db.commit()
                
                print(f"✅ 工作流创建成功: {workflow_id}")
                
                return {
                    "success": True,
                    "data": {
                        "workflow": {
                            "id": workflow_id,
                            "name": request.name,
                            "status": "DRAFT",
                            "created_at": format_datetime(datetime.now()),
                            "node_count": len(request.nodes or []),
                            "complexity": "medium"
                        }
                    }
                }
                
        except Exception as e:
            print(f"❌ 创建工作流失败: {str(e)}")
            import traceback
            print(f"📋 错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"创建工作流失败: {str(e)}"
            }
    
    def get_workflows_list(self, page: int = 1, limit: int = 50, status: Optional[str] = None,
                          business_scenario: Optional[str] = None, search: Optional[str] = None,
                          sort_by: str = "created_at", sort_order: str = "desc") -> Dict[str, Any]:
        """获取工作流列表"""
        try:
            print(f"🔍 查询工作流列表: page={page}, limit={limit}")
            
            with get_db_session() as db:
                # 构建查询
                query = db.query(WorkflowDBModel)
                
                # 应用筛选条件
                if status:
                    normalized_status = normalize_status(status)
                    query = query.filter(WorkflowDBModel.status == normalized_status)
                if business_scenario:
                    query = query.filter(WorkflowDBModel.business_scenario == business_scenario)
                if search:
                    search_pattern = f"%{search}%"
                    query = query.filter(
                        or_(
                            WorkflowDBModel.name.like(search_pattern),
                            WorkflowDBModel.description.like(search_pattern)
                        )
                    )
                
                # 计算总数
                total_count = query.count()
                print(f"📊 总工作流数量: {total_count}")
                
                # 应用排序
                if sort_by == "name":
                    order_column = WorkflowDBModel.name
                elif sort_by == "updated_at":
                    order_column = WorkflowDBModel.updated_at
                else:
                    order_column = WorkflowDBModel.created_at
                
                if sort_order == "asc":
                    query = query.order_by(order_column.asc())
                else:
                    query = query.order_by(order_column.desc())
                
                # 应用分页
                offset = (page - 1) * limit

                # 使用原始SQL查询避免枚举值解析问题
                try:
                    workflows = query.offset(offset).limit(limit).all()
                except Exception as query_error:
                    print(f"⚠️ ORM查询失败，尝试原始SQL查询: {query_error}")
                    # 使用原始SQL查询
                    from sqlalchemy import text
                    sql = text("""
                        SELECT id, name, description, business_scenario, status,
                               user_intents, trigger_keywords, inputs, nodes,
                               workflow_metadata, created_by, created_at, updated_at
                        FROM workflows
                        ORDER BY created_at DESC
                        LIMIT :limit OFFSET :offset
                    """)
                    result = db.execute(sql, {"limit": limit, "offset": offset})
                    rows = result.fetchall()

                    # 手动构建工作流对象
                    workflows = []
                    for row in rows:
                        workflow_dict = {
                            'id': row[0],
                            'name': row[1],
                            'description': row[2],
                            'business_scenario': row[3],
                            'status': normalize_status(row[4]),  # 标准化状态值
                            'user_intents': row[5],
                            'trigger_keywords': row[6],
                            'inputs': row[7],
                            'nodes': row[8],
                            'workflow_metadata': row[9],
                            'created_by': row[10],
                            'created_at': row[11],
                            'updated_at': row[12]
                        }
                        workflows.append(type('Workflow', (), workflow_dict)())
                
                print(f"📋 查询到 {len(workflows)} 个工作流")
                
                # 构建响应
                workflow_list = []
                for workflow in workflows:
                    try:
                        node_count = 0
                        if workflow.nodes:
                            node_count = len(workflow.nodes)

                        # 标准化状态值
                        status_value = normalize_status(workflow.status) if workflow.status else "DRAFT"

                        workflow_item = {
                            "id": workflow.id,
                            "name": workflow.name,
                            "description": workflow.description,
                            "business_scenario": workflow.business_scenario,
                            "status": status_value,
                            "node_count": node_count,
                            "created_at": format_datetime(workflow.created_at),
                            "updated_at": format_datetime(workflow.updated_at)
                        }
                        workflow_list.append(workflow_item)
                    except Exception as item_error:
                        print(f"⚠️ 跳过有问题的工作流记录 {workflow.id}: {item_error}")
                        continue
                
                # 计算分页信息
                total_pages = (total_count + limit - 1) // limit
                
                pagination = {
                    "current_page": page,
                    "per_page": limit,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
                
                return {
                    "success": True,
                    "data": {
                        "workflows": workflow_list,
                        "pagination": pagination
                    }
                }
                
        except Exception as e:
            print(f"❌ 查询工作流列表失败: {str(e)}")
            import traceback
            print(f"📋 错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询工作流列表失败: {str(e)}"
            }
    
    def get_workflow_detail(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流详情"""
        try:
            with get_db_session() as db:
                # 查询工作流
                print(f"🔍 查询工作流详情: {workflow_id}")
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()

                if not db_workflow:
                    print(f"❌ 工作流不存在: {workflow_id}")
                    return {
                        "success": False,
                        "error": "workflow_not_found",
                        "details": "工作流不存在"
                    }

                print(f"✅ 找到工作流: {db_workflow.name}")
                print(f"📊 工作流状态: {db_workflow.status}")
                
                # 计算节点数量
                node_count = 0
                if db_workflow.nodes:
                    node_count = len(db_workflow.nodes)
                print(f"📊 节点数量: {node_count}")

                # 构建响应
                print("🔧 开始构建工作流响应...")
                workflow = self._build_workflow_response(db_workflow)
                print("✅ 工作流响应构建成功")

                return {
                    "success": True,
                    "data": {"workflow": workflow}
                }

        except Exception as e:
            import traceback
            print(f"❌ 查询工作流详情失败: {str(e)}")
            print(f"📋 错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询工作流详情失败: {str(e)}"
            }
    
    def update_workflow(self, workflow_id: str, request: WorkflowUpdateRequest) -> Dict[str, Any]:
        """更新工作流定义"""
        try:
            with get_db_session() as db:
                # 查询工作流
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()
                
                if not db_workflow:
                    return {
                        "success": False,
                        "error": "workflow_not_found",
                        "details": "工作流不存在"
                    }
                
                # 更新基本信息
                if request.name is not None:
                    db_workflow.name = request.name
                if request.description is not None:
                    db_workflow.description = request.description
                if request.business_scenario is not None:
                    db_workflow.business_scenario = request.business_scenario
                if request.status is not None:
                    db_workflow.status = request.status
                if request.user_intents is not None:
                    db_workflow.user_intents = request.user_intents
                if request.trigger_keywords is not None:
                    db_workflow.trigger_keywords = request.trigger_keywords
                if request.inputs is not None:
                    db_workflow.inputs = [input_item.model_dump() if hasattr(input_item, 'model_dump') else input_item for input_item in request.inputs]
                if request.nodes is not None:
                    db_workflow.nodes = [node.model_dump() if hasattr(node, 'model_dump') else node for node in request.nodes]
                if request.metadata is not None:
                    db_workflow.workflow_metadata = request.metadata.model_dump() if hasattr(request.metadata, 'model_dump') else request.metadata
                
                db.commit()
                
                return {
                    "success": True,
                    "data": {
                        "workflow": {
                            "id": workflow_id,
                            "name": db_workflow.name,
                            "status": db_workflow.status,
                            "updated_at": format_datetime(db_workflow.updated_at),
                            "node_count": len(db_workflow.nodes or [])
                        }
                    }
                }
                
        except Exception as e:
            print(f"❌ 更新工作流失败: {str(e)}")
            import traceback
            print(f"📋 错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新工作流失败: {str(e)}"
            }
    
    def delete_workflow(self, workflow_id: str, force: bool = False) -> Dict[str, Any]:
        """删除工作流定义"""
        try:
            print(f"🗑️ 开始删除工作流: {workflow_id}, force={force}")

            with get_db_session() as db:
                # 查询工作流
                print(f"📋 查询工作流: {workflow_id}")
                db_workflow = db.query(WorkflowDBModel).filter(WorkflowDBModel.id == workflow_id).first()

                if not db_workflow:
                    print(f"❌ 工作流不存在: {workflow_id}")
                    return {
                        "success": False,
                        "error": "workflow_not_found",
                        "details": "工作流不存在"
                    }

                print(f"✅ 找到工作流: {db_workflow.name}")

                # 工作流不执行，直接删除（无需检查实例）
                print("🔍 工作流只是配置，无需检查运行实例...")

                # 删除工作流
                workflow_name = db_workflow.name
                print(f"🗑️ 开始删除工作流: {workflow_name}")

                db.delete(db_workflow)
                print("💾 提交数据库事务...")
                db.commit()
                print("✅ 工作流删除成功")

                return {
                    "success": True,
                    "data": {
                        "deleted_workflow_id": workflow_id,
                        "deleted_nodes_count": len(db_workflow.nodes or [])
                    }
                }

        except Exception as e:
            print(f"❌ 删除工作流异常: {str(e)}")
            import traceback
            print(f"📋 错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除工作流失败: {str(e)}"
            }
    
    def _build_workflow_response(self, db_workflow: WorkflowDBModel) -> Dict[str, Any]:
        """构建工作流响应对象，完全按照API文档定义"""
        print(f"🔧 构建工作流响应: {db_workflow.id}")

        # 从JSON字段获取节点信息
        nodes = db_workflow.nodes or []
        print(f"📊 找到 {len(nodes)} 个节点")

        # 安全地获取状态值，处理字符串状态
        raw_status = str(db_workflow.status) if db_workflow.status else "DRAFT"
        status_mapping = {
            'draft': 'DRAFT',
            'active': 'ACTIVE',
            'inactive': 'INACTIVE',
            'archived': 'ARCHIVED'
        }
        status_value = status_mapping.get(raw_status.lower(), raw_status.upper())

        # 构建工作流响应数据 (基于API文档)
        workflow_data = {
            "id": db_workflow.id,
            "name": db_workflow.name,
            "description": db_workflow.description,
            "business_scenario": db_workflow.business_scenario,
            "status": status_value,
            "user_intents": db_workflow.user_intents or [],
            "trigger_keywords": db_workflow.trigger_keywords or [],
            "inputs": db_workflow.inputs or [],
            "nodes": nodes,
            "metadata": db_workflow.workflow_metadata or {},
            "created_at": format_datetime(db_workflow.created_at),
            "updated_at": format_datetime(db_workflow.updated_at),
            "created_by": db_workflow.created_by
        }
        
        print(f"✅ 工作流响应构建完成: {workflow_data['name']}")
        return workflow_data


# 创建全局实例
workflow_service = WorkflowService()
