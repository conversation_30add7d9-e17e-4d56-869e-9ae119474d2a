"""
表单配置API路由
处理表单配置相关的HTTP请求
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import Optional

from app.core.auth import verify_developer_token
from app.services.form_service import form_service
from app.services.form_field_service import form_field_service
from app.schemas.form import (
    FormCreateRequest, FormUpdateRequest, FormRenderRequest, FormSubmitRequest,
    FormFieldCreateRequest, FormFieldUpdateRequest
)

router = APIRouter()


# 表单定义管理
@router.post("/forms", status_code=status.HTTP_201_CREATED)
async def create_form(
    request: FormCreateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    创建动态表单配置
    
    创建新的动态表单配置，包括字段布局、验证规则和权限设置。
    """
    result = form_service.create_form(request)
    
    if not result["success"]:
        if result["error"] == "creation_failed":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 201,
        "message": "表单配置创建成功",
        "data": result["data"]
    }


@router.get("/forms")
async def get_forms_list(
    entity: Optional[str] = Query(None, description="筛选特定实体的表单"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取所有表单配置
    
    获取系统中所有已定义的表单配置列表。
    """
    result = form_service.get_forms_list(entity, page, limit)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "获取表单配置列表成功",
        "data": result["data"]
    }


@router.get("/forms/{form_id}")
async def get_form_detail(
    form_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取特定表单配置
    
    获取指定表单的详细配置信息。
    """
    result = form_service.get_form_detail(form_id)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "获取表单配置详情成功",
        "data": result["data"]
    }


@router.put("/forms/{form_id}")
async def update_form(
    form_id: str,
    request: FormUpdateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    更新表单配置
    
    更新指定表单的配置信息。
    """
    result = form_service.update_form(form_id, request)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "表单配置更新成功",
        "data": result["data"]
    }


@router.delete("/forms/{form_id}")
async def delete_form(
    form_id: str,
    force: bool = Query(False, description="是否强制删除"),
    _: dict = Depends(verify_developer_token)
):
    """
    删除表单配置
    
    删除指定的表单配置。
    """
    result = form_service.delete_form(form_id, force)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "has_related_data":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "表单配置删除成功",
        "data": result["data"]
    }


# 表单渲染管理
@router.get("/forms/{form_id}/render")
async def get_form_render_schema(
    form_id: str,
    mode: str = Query("create", description="渲染模式: create/edit/view"),
    entity_id: Optional[str] = Query(None, description="实体记录ID（编辑模式）"),
    user_role: str = Query("user", description="用户角色"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取表单渲染配置（amis schema）
    
    获取指定表单的amis渲染配置。
    """
    request = FormRenderRequest(
        mode=mode,
        entity_id=entity_id,
        user_role=user_role
    )
    
    result = form_service.get_form_render_schema(form_id, request)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "permission_denied":
            raise HTTPException(status_code=403, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "获取表单渲染配置成功",
        "data": result["data"]
    }


@router.post("/forms/{form_id}/submit")
async def submit_form_data(
    form_id: str,
    request: FormSubmitRequest,
    token_data: dict = Depends(verify_developer_token)
):
    """
    提交表单数据
    
    提交表单数据并进行验证。
    """
    submitted_by = token_data.get("user_id", "developer")
    result = form_service.submit_form_data(form_id, request, submitted_by)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "validation_failed":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "表单数据提交成功",
        "data": result["data"]
    }


@router.get("/forms/{form_id}/data/{entity_id}")
async def get_form_data_for_edit(
    form_id: str,
    entity_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取表单数据用于编辑
    
    获取指定实体的表单数据用于编辑。
    """
    result = form_service.get_form_data_for_edit(form_id, entity_id)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "data_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "获取表单数据成功",
        "data": result["data"]
    }


@router.post("/forms/{form_id}/validate")
async def validate_form_config(
    form_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    验证表单配置
    
    验证表单配置的完整性和正确性。
    """
    result = form_service.validate_form_config(form_id)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "表单配置验证完成",
        "data": result["data"]
    }


# 表单字段管理
@router.get("/forms/{form_id}/fields")
async def get_form_fields(
    form_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取表单字段列表
    
    获取指定表单的所有字段配置。
    """
    result = form_field_service.get_form_fields(form_id)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "获取表单字段列表成功",
        "data": result["data"]
    }


@router.post("/forms/{form_id}/fields", status_code=status.HTTP_201_CREATED)
async def add_form_field(
    form_id: str,
    request: FormFieldCreateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    添加表单字段
    
    向指定表单添加新的字段。
    """
    result = form_field_service.add_form_field(form_id, request)
    
    if not result["success"]:
        if result["error"] == "form_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] in ["field_id_exists", "section_not_found"]:
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 201,
        "message": "表单字段添加成功",
        "data": result["data"]
    }


@router.put("/forms/{form_id}/fields/{field_id}")
async def update_form_field(
    form_id: str,
    field_id: str,
    request: FormFieldUpdateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    更新表单字段
    
    更新指定表单中的特定字段配置。
    """
    result = form_field_service.update_form_field(form_id, field_id, request)
    
    if not result["success"]:
        if result["error"] == "field_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "表单字段更新成功",
        "data": result["data"]
    }


@router.delete("/forms/{form_id}/fields/{field_id}")
async def delete_form_field(
    form_id: str,
    field_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    删除表单字段
    
    从指定表单中删除特定字段。
    """
    result = form_field_service.delete_form_field(form_id, field_id)
    
    if not result["success"]:
        if result["error"] == "field_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "表单字段删除成功",
        "data": result["data"]
    }
