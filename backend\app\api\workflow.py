"""
工作流设计模块API路由 (AI指导版)
实现工作流定义、节点管理的9个API端点
专门为AI提供业务流程指导和API调用指南
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status, Response
from typing import Optional

from app.core.auth import verify_developer_token
from app.schemas.workflow import (
    WorkflowCreateRequest, WorkflowUpdateRequest, WorkflowExecuteRequest,
    WorkflowNodeCreateRequest, WorkflowNodeUpdateRequest
)
from app.services.workflow_service import workflow_service

router = APIRouter(prefix="/api", tags=["工作流设计模块"])


# 工作流管理 (5个API端点)

@router.post("/workflows", status_code=status.HTTP_201_CREATED)
async def create_workflow(
    request: WorkflowCreateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    创建工作流定义 (AI指导版)

    创建新的工作流定义，包括节点配置、父子关系和AI执行指令。
    """
    result = workflow_service.create_workflow(request)

    if not result["success"]:
        if result["error"] == "creation_failed":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 201,
        "message": "工作流创建成功",
        "data": result["data"]
    }


@router.get("/workflows")
async def get_workflows_list(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    limit: int = Query(50, ge=1, le=100, description="每页数量，最大100"),
    status: Optional[str] = Query(None, description="工作流状态筛选：active, draft, inactive"),
    business_scenario: Optional[str] = Query(None, description="业务场景筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（匹配名称和描述）"),
    sort_by: str = Query("created_at", description="排序字段：name, created_at, updated_at, node_count"),
    sort_order: str = Query("desc", description="排序方向：asc, desc"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取工作流列表

    获取工作流列表，支持分页、筛选和排序。用于在开发者配置面板中显示所有可用的工作流。
    """
    result = workflow_service.get_workflows_list(
        page=page,
        limit=limit,
        status=status,
        business_scenario=business_scenario,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "获取工作流列表成功",
        "data": result["data"]
    }


# 工作流执行管理 (5个API端点) - 需要在{workflow_id}路由之前定义

@router.get("/workflows/instances")
async def get_workflow_instances(
    workflow_id: Optional[str] = Query(None, description="筛选特定工作流的实例"),
    status: Optional[str] = Query(None, description="筛选实例状态"),
    user_id: Optional[str] = Query(None, description="筛选特定用户的实例"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    sort: str = Query("created_at", description="排序字段"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取工作流实例列表

    获取工作流实例列表，支持按状态、时间等条件筛选。
    """
    result = workflow_service.get_workflow_instances(workflow_id, status, user_id, page, limit, sort)

    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "获取工作流实例列表成功",
        "data": result["data"]
    }


@router.get("/workflows/instances/{instance_id}")
async def get_workflow_instance_detail(
    instance_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取工作流实例详情

    获取特定工作流实例的详细信息，包括执行历史和当前状态。
    """
    result = workflow_service.get_workflow_instance_detail(instance_id)

    if not result["success"]:
        if result["error"] == "instance_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "获取工作流实例详情成功",
        "data": result["data"]
    }


@router.get("/workflows/{workflow_id}")
async def get_workflow_detail(
    workflow_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取特定工作流详情

    获取指定工作流的详细信息，包括完整的步骤配置。
    """
    result = workflow_service.get_workflow_detail(workflow_id)

    if not result["success"]:
        if result["error"] == "workflow_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "获取工作流详情成功",
        "data": result["data"]
    }


@router.put("/workflows/{workflow_id}")
async def update_workflow(
    workflow_id: str,
    request: WorkflowUpdateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    更新工作流定义

    更新指定工作流的配置信息，包括步骤和条件。
    """
    try:
        print(f"🔍 收到工作流更新请求: workflow_id={workflow_id}")
        print(f"📝 请求数据: {request.model_dump()}")

        result = workflow_service.update_workflow(workflow_id, request)

        if not result["success"]:
            print(f"❌ 工作流更新失败: {result}")
            if result["error"] == "workflow_not_found":
                raise HTTPException(status_code=404, detail=result["details"])
            elif result["error"] == "update_failed":
                raise HTTPException(status_code=400, detail=result["details"])
            else:
                raise HTTPException(status_code=500, detail=result["details"])

        print(f"✅ 工作流更新成功: {result['data']}")
        return {
            "code": 200,
            "message": "工作流更新成功",
            "data": result["data"]
        }
    except Exception as e:
        print(f"💥 工作流更新异常: {str(e)}")
        print(f"📊 异常类型: {type(e)}")
        raise


@router.delete("/workflows/{workflow_id}")
async def delete_workflow(
    workflow_id: str,
    force: bool = Query(False, description="是否强制删除"),
    _: dict = Depends(verify_developer_token)
):
    """
    删除工作流定义

    删除指定的工作流定义及其相关数据。
    """
    print(f"🌐 API: 收到删除工作流请求: {workflow_id}, force={force}")

    try:
        result = workflow_service.delete_workflow(workflow_id, force)
        print(f"📋 Service返回结果: {result}")

        if not result["success"]:
            print(f"❌ 删除失败: {result['error']} - {result['details']}")
            if result["error"] == "workflow_not_found":
                raise HTTPException(status_code=404, detail=result["details"])
            elif result["error"] == "has_running_instances":
                raise HTTPException(status_code=409, detail=result["details"])
            else:
                raise HTTPException(status_code=500, detail=result["details"])

        print(f"✅ 删除成功，返回响应")
        return {
            "code": 200,
            "message": "工作流删除成功",
            "data": result["data"]
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        print(f"❌ API异常: {str(e)}")
        import traceback
        print(f"📋 API错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"删除工作流时发生异常: {str(e)}")


# 节点管理 (4个API端点)

@router.get("/workflows/{workflow_id}/nodes")
async def get_workflow_nodes(
    workflow_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    获取工作流节点列表

    获取指定工作流的所有节点信息。
    """
    result = workflow_service.get_workflow_nodes(workflow_id)

    if not result["success"]:
        if result["error"] == "workflow_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "获取工作流节点列表成功",
        "data": result["data"]
    }


@router.post("/workflows/{workflow_id}/nodes", status_code=status.HTTP_201_CREATED)
async def add_workflow_node(
    workflow_id: str,
    request: WorkflowNodeCreateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    添加工作流节点

    向指定工作流添加新节点，包括父子关系和转换条件。
    """
    result = workflow_service.add_workflow_node(workflow_id, request)

    if not result["success"]:
        if result["error"] == "workflow_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "node_exists":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 201,
        "message": "节点添加成功",
        "data": result["data"]
    }


@router.put("/workflows/{workflow_id}/nodes/{node_id}")
async def update_workflow_node(
    workflow_id: str,
    node_id: str,
    request: WorkflowNodeUpdateRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    更新工作流节点

    更新指定工作流中的节点配置。
    """
    result = workflow_service.update_workflow_node(workflow_id, node_id, request)

    if not result["success"]:
        if result["error"] == "workflow_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "node_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "节点更新成功",
        "data": result["data"]
    }


@router.delete("/workflows/{workflow_id}/nodes/{node_id}")
async def delete_workflow_node(
    workflow_id: str,
    node_id: str,
    _: dict = Depends(verify_developer_token)
):
    """
    删除工作流节点

    从指定工作流中删除节点。
    """
    result = workflow_service.delete_workflow_node(workflow_id, node_id)

    if not result["success"]:
        if result["error"] == "workflow_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "node_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "cannot_delete_critical_node":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "节点删除成功",
        "data": result["data"]
    }


# amis schema生成器专用接口

@router.get("/workflows/for-amis")
async def get_workflows_for_amis(
    _: dict = Depends(verify_developer_token)
):
    """
    为amis schema生成器提供工作流选项

    返回所有可用的工作流列表，供amis schema生成器选择使用。
    """
    result = workflow_service.get_workflows_for_amis()

    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "获取工作流选项成功",
        "data": result["data"]
    }


@router.get("/configuration/for-amis")
async def get_configuration_for_amis(
    _: dict = Depends(verify_developer_token)
):
    """
    为amis schema生成器提供完整的配置信息

    返回当前应用配置的所有API和工作流信息，供amis schema生成器使用。
    包括：
    - 所有可用的API接口及其配置
    - 所有可用的工作流及其节点配置
    - 配置摘要信息
    """
    result = workflow_service.get_amis_configuration_info()

    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 200,
        "message": "获取配置信息成功",
        "data": result["data"]
    }


# 工作流执行管理 (保留原有功能)

@router.post("/workflows/{workflow_id}/execute", status_code=status.HTTP_201_CREATED)
async def execute_workflow(
    workflow_id: str,
    request: WorkflowExecuteRequest,
    token_data: dict = Depends(verify_developer_token)
):
    """
    执行工作流

    启动指定工作流的新实例并开始执行。
    """
    started_by = token_data.get("user_id", "developer")
    result = workflow_service.execute_workflow(workflow_id, request, started_by)

    if not result["success"]:
        if result["error"] == "workflow_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] in ["workflow_inactive", "no_start_step"]:
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])

    return {
        "code": 201,
        "message": "工作流执行启动成功",
        "data": result["data"]
    }




@router.post("/workflows/instances/{instance_id}/continue")
async def continue_workflow_instance(
    instance_id: str,
    request: dict = None,
    _: dict = Depends(verify_developer_token)
):
    """
    继续执行工作流实例

    继续执行处于等待状态的工作流实例，通常用于用户操作后的流程继续。
    """
    # 处理请求数据，支持action、data、comment字段
    action = request.get("action", "continue") if request else "continue"
    data = request.get("data", {}) if request else {}
    comment = request.get("comment", "") if request else ""

    result = workflow_service.continue_workflow_instance(instance_id, action, data, comment)
    
    if not result["success"]:
        if result["error"] == "instance_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "instance_not_waiting":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "工作流实例继续执行成功",
        "data": result["data"]
    }


@router.post("/workflows/instances/{instance_id}/cancel")
async def cancel_workflow_instance(
    instance_id: str,
    reason: str = Query("用户取消", description="取消原因"),
    _: dict = Depends(verify_developer_token)
):
    """
    取消工作流实例
    
    取消正在执行或等待中的工作流实例。
    """
    result = workflow_service.cancel_workflow_instance(instance_id, reason)
    
    if not result["success"]:
        if result["error"] == "instance_not_found":
            raise HTTPException(status_code=404, detail=result["details"])
        elif result["error"] == "instance_already_finished":
            raise HTTPException(status_code=400, detail=result["details"])
        else:
            raise HTTPException(status_code=500, detail=result["details"])
    
    return {
        "code": 200,
        "message": "工作流实例取消成功",
        "data": result["data"]
    }


# 旧的步骤管理端点已移除，请使用节点管理端点


