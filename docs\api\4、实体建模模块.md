# 实体建模模块 API 文档

## 📋 概述

实体建模模块提供业务实体的定义、管理和数据操作功能，支持创建任意业务场景的数据结构和关系。

## 🏗️ API 端点列表

### 实体定义管理
1. 创建业务实体定义
2. 获取所有业务实体列表
3. 获取特定实体详情
4. 更新实体定义
5. 删除实体定义

### 实体数据管理
6. 获取实体数据列表
7. 创建实体数据记录
8. 更新实体数据记录
9. 删除实体数据记录

### 实体关系管理
10. 创建实体关系
11. 获取实体关系列表
12. 更新实体关系
13. 删除实体关系

---

## API 详细文档

### 1. 创建业务实体定义

**POST** `/api/entities`

#### 描述
创建新的业务实体定义，包括字段、验证规则和关系配置。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "displayName": "string",
  "description": "string",
  "icon": "string",
  "fields": [
    {
      "name": "string",
      "displayName": "string",
      "type": "string",
      "required": boolean,
      "unique": boolean,
      "defaultValue": "any",
      "validation": {
        "min": number,
        "max": number,
        "minLength": number,
        "maxLength": number,
        "pattern": "string"
      },
      "options": ["string"]
    }
  ]
}
```

#### 字段类型说明

| 类型 | 描述 | 示例 |
|------|------|------|
| text | 文本字段 | 姓名、地址 |
| number | 数字字段 | 年龄、数量 |
| decimal | 小数字段 | 价格、重量 |
| boolean | 布尔字段 | 是否激活 |
| date | 日期字段 | 生日、创建日期 |
| datetime | 日期时间字段 | 预约时间 |
| email | 邮箱字段 | 联系邮箱 |
| phone | 电话字段 | 联系电话 |
| select | 单选字段 | 状态、分类 |
| multiselect | 多选字段 | 标签、权限 |
| image | 图片字段 | 头像、商品图片 |
| file | 文件字段 | 附件、文档 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/entities" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "product",
    "displayName": "商品",
    "description": "商品信息管理实体",
    "icon": "shopping-cart",
    "fields": [
      {
        "name": "name",
        "displayName": "商品名称",
        "type": "text",
        "required": true,
        "validation": {
          "minLength": 1,
          "maxLength": 100
        }
      },
      {
        "name": "price",
        "displayName": "价格",
        "type": "decimal",
        "required": true,
        "validation": {
          "min": 0,
          "precision": 2
        }
      },
      {
        "name": "category",
        "displayName": "分类",
        "type": "select",
        "required": true,
        "options": ["电子产品", "服装", "食品", "图书"]
      },
      {
        "name": "status",
        "displayName": "状态",
        "type": "select",
        "required": true,
        "defaultValue": "active",
        "options": ["active", "inactive", "discontinued"]
      }
    ]
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "实体创建成功",
  "data": {
    "entity": {
      "id": "entity_1705123456789",
      "name": "product",
      "displayName": "商品",
      "description": "商品信息管理实体",
      "icon": "shopping-cart",
      "fields": [
        {
          "id": "field_1705123456790",
          "name": "name",
          "displayName": "商品名称",
          "type": "text",
          "required": true,
          "unique": false,
          "validation": {
            "minLength": 1,
            "maxLength": 100
          }
        }
      ],
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

---

### 2. 获取所有业务实体列表

**GET** `/api/entities`

#### 描述
获取当前场景中所有已定义的业务实体列表。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| include_fields | boolean | 否 | 是否包含字段详情，默认true |
| status | string | 否 | 筛选实体状态 |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/entities?include_fields=true" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取实体列表成功",
  "data": {
    "entities": [
      {
        "id": "entity_1705123456789",
        "name": "product",
        "displayName": "商品",
        "description": "商品信息管理实体",
        "icon": "shopping-cart",
        "status": "active",
        "field_count": 4,
        "record_count": 156,
        "fields": [
          {
            "id": "field_1705123456790",
            "name": "name",
            "displayName": "商品名称",
            "type": "text",
            "required": true
          }
        ],
        "created_at": "2024-01-20T10:00:00.000Z",
        "updated_at": "2024-01-20T10:30:00.000Z"
      }
    ],
    "total": 1
  }
}
```

---

### 3. 获取特定实体详情

**GET** `/api/entities/{entity_id}`

#### 描述
获取指定实体的详细信息，包括字段定义、关系和统计信息。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/entities/entity_1705123456789" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取实体详情成功",
  "data": {
    "entity": {
      "id": "entity_1705123456789",
      "name": "product",
      "displayName": "商品",
      "description": "商品信息管理实体",
      "icon": "shopping-cart",
      "status": "active",
      "fields": [
        {
          "id": "field_1705123456790",
          "name": "name",
          "displayName": "商品名称",
          "type": "text",
          "required": true,
          "unique": false,
          "validation": {
            "minLength": 1,
            "maxLength": 100
          }
        }
      ],
      "relationships": [
        {
          "id": "rel_1705123456791",
          "name": "orders",
          "targetEntity": "order",
          "type": "one-to-many"
        }
      ],
      "statistics": {
        "record_count": 156,
        "field_count": 8,
        "relationship_count": 2,
        "last_updated": "2024-01-20T10:30:00.000Z"
      },
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:30:00.000Z"
    }
  }
}
```

---

### 4. 更新实体定义

**PUT** `/api/entities/{entity_id}`

#### 描述
更新指定实体的定义信息，包括字段、验证规则等。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "displayName": "string",
  "description": "string",
  "icon": "string",
  "fields": []
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/entities/entity_1705123456789" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "displayName": "商品信息",
    "description": "完整的商品信息管理实体",
    "icon": "shopping-bag"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "实体更新成功",
  "data": {
    "entity": {
      "id": "entity_1705123456789",
      "name": "product",
      "displayName": "商品信息",
      "description": "完整的商品信息管理实体",
      "icon": "shopping-bag",
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 5. 删除实体定义

**DELETE** `/api/entities/{entity_id}`

#### 描述
删除指定的实体定义，包括相关的数据和关系。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| force | boolean | 否 | 是否强制删除，默认false |
| backup | boolean | 否 | 是否备份数据，默认true |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/entities/entity_1705123456789?force=false&backup=true" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "实体删除成功",
  "data": {
    "entity_id": "entity_1705123456789",
    "name": "product",
    "deleted_at": "2024-01-20T12:00:00.000Z",
    "backup_file": "/backups/entity_product_20240120.json",
    "affected_records": 156,
    "affected_relationships": 2
  }
}
```

---

### 6. 获取实体数据列表

**GET** `/api/entities/{entity_id}/data`

#### 描述
获取指定实体的数据记录列表，支持分页、筛选和排序。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |
| sort | string | 否 | 排序字段，格式：field:asc/desc |
| filter | string | 否 | 筛选条件，JSON格式 |
| search | string | 否 | 搜索关键词 |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/entities/entity_1705123456789/data?page=1&limit=10&sort=created_at:desc" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取实体数据成功",
  "data": {
    "records": [
      {
        "id": "record_1705123456791",
        "name": "iPhone 15 Pro",
        "price": 7999.00,
        "category": "电子产品",
        "status": "active",
        "created_at": "2024-01-20T10:15:00.000Z",
        "updated_at": "2024-01-20T10:15:00.000Z"
      },
      {
        "id": "record_1705123456792",
        "name": "MacBook Air",
        "price": 8999.00,
        "category": "电子产品",
        "status": "active",
        "created_at": "2024-01-20T10:20:00.000Z",
        "updated_at": "2024-01-20T10:20:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 156,
      "pages": 16,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

---

### 4. 创建实体数据记录

**POST** `/api/entities/{entity_id}/data`

#### 描述
为指定实体创建新的数据记录。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "field_name": "field_value"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/entities/entity_1705123456789/data" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "iPad Pro",
    "price": 6999.00,
    "category": "电子产品",
    "status": "active"
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "数据记录创建成功",
  "data": {
    "record": {
      "id": "record_1705123456793",
      "name": "iPad Pro",
      "price": 6999.00,
      "category": "电子产品",
      "status": "active",
      "created_at": "2024-01-20T11:00:00.000Z",
      "updated_at": "2024-01-20T11:00:00.000Z"
    }
  }
}
```

**验证错误响应 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "数据验证失败",
  "data": {
    "error": "validation_error",
    "details": {
      "name": ["商品名称不能为空"],
      "price": ["价格必须大于0"]
    }
  }
}
```

---

### 8. 更新实体数据记录

**PUT** `/api/entities/{entity_id}/data/{record_id}`

#### 描述
更新指定实体的数据记录。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |
| record_id | string | 是 | 记录唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "field_name": "field_value"
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/entities/entity_1705123456789/data/record_1705123456791" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "iPhone 15 Pro Max",
    "price": 8999.00,
    "category": "电子产品"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "数据记录更新成功",
  "data": {
    "record": {
      "id": "record_1705123456791",
      "name": "iPhone 15 Pro Max",
      "price": 8999.00,
      "category": "电子产品",
      "status": "active",
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 9. 删除实体数据记录

**DELETE** `/api/entities/{entity_id}/data/{record_id}`

#### 描述
删除指定实体的数据记录。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |
| record_id | string | 是 | 记录唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/entities/entity_1705123456789/data/record_1705123456791" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "数据记录删除成功",
  "data": {
    "record_id": "record_1705123456791",
    "deleted_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

### 10. 创建实体关系

**POST** `/api/entities/{entity_id}/relationships`

#### 描述
为实体创建与其他实体的关系定义。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 源实体唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "targetEntity": "string",
  "type": "string",
  "foreignKey": "string",
  "constraints": {
    "cascadeDelete": boolean,
    "required": boolean,
    "unique": boolean
  }
}
```

#### 关系类型说明

| 类型 | 描述 | 示例 |
|------|------|------|
| one-to-one | 一对一关系 | 用户 ↔ 用户资料 |
| one-to-many | 一对多关系 | 订单 → 订单项 |
| many-to-many | 多对多关系 | 学生 ↔ 课程 |
| hierarchy | 层级关系 | 部门树形结构 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/entities/entity_1705123456789/relationships" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "orders",
    "targetEntity": "order",
    "type": "one-to-many",
    "foreignKey": "product_id",
    "constraints": {
      "cascadeDelete": false,
      "required": false,
      "unique": false
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "实体关系创建成功",
  "data": {
    "relationship": {
      "id": "relationship_1705123456794",
      "name": "orders",
      "sourceEntity": "product",
      "targetEntity": "order",
      "type": "one-to-many",
      "foreignKey": "product_id",
      "constraints": {
        "cascadeDelete": false,
        "required": false,
        "unique": false
      },
      "created_at": "2024-01-20T11:30:00.000Z"
    }
  }
}
```

---

### 11. 获取实体关系列表

**GET** `/api/entities/{entity_id}/relationships`

#### 描述
获取指定实体的所有关系列表。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity_id | string | 是 | 实体唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/entities/entity_1705123456789/relationships" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取实体关系成功",
  "data": {
    "relationships": [
      {
        "id": "relationship_1705123456794",
        "name": "orders",
        "sourceEntity": "product",
        "targetEntity": "order",
        "type": "one-to-many",
        "foreignKey": "product_id",
        "created_at": "2024-01-20T11:30:00.000Z"
      }
    ],
    "total": 1
  }
}
```

---

### 12. 更新实体关系

**PUT** `/api/relationships/{relationship_id}`

#### 描述
更新指定的实体关系配置。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| relationship_id | string | 是 | 关系唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "constraints": {}
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/relationships/relationship_1705123456794" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "product_orders",
    "constraints": {
      "cascadeDelete": true,
      "required": false
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "实体关系更新成功",
  "data": {
    "relationship": {
      "id": "relationship_1705123456794",
      "name": "product_orders",
      "constraints": {
        "cascadeDelete": true,
        "required": false
      },
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 13. 删除实体关系

**DELETE** `/api/relationships/{relationship_id}`

#### 描述
删除指定的实体关系。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| relationship_id | string | 是 | 关系唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/relationships/relationship_1705123456794" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "实体关系删除成功",
  "data": {
    "relationship_id": "relationship_1705123456794",
    "deleted_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

## 📝 字段验证规则

### 文本类型验证
```json
{
  "minLength": 1,
  "maxLength": 255,
  "pattern": "^[a-zA-Z0-9]+$"
}
```

### 数字类型验证
```json
{
  "min": 0,
  "max": 999999,
  "precision": 2
}
```

### 日期类型验证
```json
{
  "minDate": "2024-01-01",
  "maxDate": "2024-12-31",
  "format": "YYYY-MM-DD"
}
```

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| entity_not_found | 404 | 实体不存在 | 检查实体ID是否正确 |
| field_validation_error | 400 | 字段验证失败 | 检查字段值是否符合验证规则 |
| duplicate_entity_name | 409 | 实体名称重复 | 使用不同的实体名称 |
| invalid_field_type | 400 | 无效的字段类型 | 使用支持的字段类型 |
| relationship_conflict | 409 | 关系冲突 | 检查关系配置是否合理 |
| record_not_found | 404 | 记录不存在 | 检查记录ID是否正确 |
