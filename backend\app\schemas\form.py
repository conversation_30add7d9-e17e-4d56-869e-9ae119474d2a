"""
表单配置相关Pydantic模型
定义API请求和响应的数据结构
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum


# 枚举类型
class FormStatus(str, Enum):
    """表单状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"


class LayoutType(str, Enum):
    """布局类型枚举"""
    GRID = "grid"
    FLEX = "flex"
    TABS = "tabs"
    ACCORDION = "accordion"


class DisplayType(str, Enum):
    """字段显示类型枚举"""
    INPUT = "input"
    TEXTAREA = "textarea"
    SELECT = "select"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    SWITCH = "switch"
    DATE_PICKER = "date-picker"
    UPLOAD = "upload"
    RICH_TEXT = "rich-text"


# 基础模型
class FormLayout(BaseModel):
    """表单布局配置"""
    type: LayoutType = Field(..., description="布局类型")
    columns: int = Field(2, ge=1, le=6, description="布局列数")
    spacing: int = Field(16, ge=0, le=64, description="布局间距")


class ValidationRule(BaseModel):
    """验证规则"""
    rules: List[str] = Field([], description="验证规则列表")
    messages: Dict[str, str] = Field({}, description="验证消息映射")


class FieldOption(BaseModel):
    """字段选项"""
    label: str = Field(..., description="选项标签")
    value: Union[str, int, bool] = Field(..., description="选项值")
    disabled: bool = Field(False, description="是否禁用")


class FormPermission(BaseModel):
    """表单权限"""
    role: str = Field(..., description="角色名称")
    actions: List[str] = Field(..., description="允许的操作列表")


# 表单字段相关模型
class FormFieldCreate(BaseModel):
    """创建表单字段请求"""
    id: str = Field(..., description="字段ID")
    entityField: str = Field(..., description="实体字段名")
    displayType: DisplayType = Field(..., description="显示类型")
    label: str = Field(..., description="字段标签")
    placeholder: Optional[str] = Field(None, description="占位符文本")
    required: bool = Field(False, description="是否必填")
    readonly: bool = Field(False, description="是否只读")
    hidden: bool = Field(False, description="是否隐藏")
    validation: Optional[ValidationRule] = Field(None, description="验证配置")
    options: Optional[List[FieldOption]] = Field(None, description="选项配置")
    gridSpan: int = Field(1, ge=1, le=6, description="网格跨度")


class FormField(BaseModel):
    """表单字段"""
    id: str = Field(..., description="字段ID")
    entityField: str = Field(..., description="实体字段名")
    displayType: DisplayType = Field(..., description="显示类型")
    label: str = Field(..., description="字段标签")
    placeholder: Optional[str] = Field(None, description="占位符文本")
    required: bool = Field(..., description="是否必填")
    readonly: bool = Field(..., description="是否只读")
    hidden: bool = Field(..., description="是否隐藏")
    validation: Optional[ValidationRule] = Field(None, description="验证配置")
    options: Optional[List[FieldOption]] = Field(None, description="选项配置")
    gridSpan: int = Field(..., description="网格跨度")
    orderIndex: int = Field(..., description="排序索引")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


# 表单分组相关模型
class FormSectionCreate(BaseModel):
    """创建表单分组请求"""
    id: str = Field(..., description="分组ID")
    title: str = Field(..., description="分组标题")
    collapsible: bool = Field(False, description="是否可折叠")
    fields: List[FormFieldCreate] = Field([], description="字段列表")


class FormSection(BaseModel):
    """表单分组"""
    id: str = Field(..., description="分组ID")
    title: str = Field(..., description="分组标题")
    collapsible: bool = Field(..., description="是否可折叠")
    field_count: int = Field(..., description="字段数量")
    orderIndex: int = Field(..., description="排序索引")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class FormSectionDetail(BaseModel):
    """表单分组详情"""
    id: str = Field(..., description="分组ID")
    title: str = Field(..., description="分组标题")
    collapsible: bool = Field(..., description="是否可折叠")
    fields: List[FormField] = Field([], description="字段列表")
    orderIndex: int = Field(..., description="排序索引")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


# 表单定义相关模型
class FormCreateRequest(BaseModel):
    """创建表单请求"""
    name: str = Field(..., min_length=1, max_length=200, description="表单名称")
    entity: str = Field(..., min_length=1, max_length=100, description="关联实体")
    description: Optional[str] = Field(None, description="表单描述")
    layout: FormLayout = Field(..., description="布局配置")
    sections: List[FormSectionCreate] = Field(..., description="分组列表")
    permissions: Optional[List[FormPermission]] = Field([], description="权限配置")


class FormUpdateRequest(BaseModel):
    """更新表单请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="表单名称")
    description: Optional[str] = Field(None, description="表单描述")
    layout: Optional[FormLayout] = Field(None, description="布局配置")
    status: Optional[FormStatus] = Field(None, description="表单状态")
    permissions: Optional[List[FormPermission]] = Field(None, description="权限配置")


class FormListItem(BaseModel):
    """表单列表项"""
    id: str = Field(..., description="表单ID")
    name: str = Field(..., description="表单名称")
    entity: str = Field(..., description="关联实体")
    description: Optional[str] = Field(None, description="表单描述")
    status: FormStatus = Field(..., description="表单状态")
    field_count: int = Field(..., description="字段数量")
    section_count: int = Field(..., description="分组数量")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class Form(BaseModel):
    """表单详情"""
    id: str = Field(..., description="表单ID")
    name: str = Field(..., description="表单名称")
    entity: str = Field(..., description="关联实体")
    description: Optional[str] = Field(None, description="表单描述")
    layout: FormLayout = Field(..., description="布局配置")
    status: FormStatus = Field(..., description="表单状态")
    sections: List[FormSectionDetail] = Field([], description="分组列表")
    permissions: List[FormPermission] = Field([], description="权限配置")
    field_count: int = Field(..., description="字段数量")
    section_count: int = Field(..., description="分组数量")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


# 表单渲染相关模型
class FormRenderRequest(BaseModel):
    """表单渲染请求"""
    mode: str = Field("create", description="渲染模式: create/edit/view")
    entity_id: Optional[str] = Field(None, description="实体记录ID（编辑模式）")
    user_role: Optional[str] = Field("user", description="用户角色")


class FormSubmitRequest(BaseModel):
    """表单提交请求"""
    form_data: Dict[str, Any] = Field(..., description="表单数据")
    entity_id: Optional[str] = Field(None, description="实体记录ID（更新模式）")


class FormDataResponse(BaseModel):
    """表单数据响应"""
    form_data: Dict[str, Any] = Field(..., description="表单数据")
    entity_id: Optional[str] = Field(None, description="实体记录ID")
    submitted_at: str = Field(..., description="提交时间")


# 表单字段管理相关模型
class FormFieldCreateRequest(BaseModel):
    """添加表单字段请求"""
    field_id: str = Field(..., description="字段ID")
    section_id: Optional[str] = Field(None, description="分组ID")
    entityField: str = Field(..., description="实体字段名")
    displayType: DisplayType = Field(..., description="显示类型")
    label: str = Field(..., description="字段标签")
    placeholder: Optional[str] = Field(None, description="占位符文本")
    required: bool = Field(False, description="是否必填")
    readonly: bool = Field(False, description="是否只读")
    hidden: bool = Field(False, description="是否隐藏")
    validation: Optional[ValidationRule] = Field(None, description="验证配置")
    options: Optional[List[FieldOption]] = Field(None, description="选项配置")
    gridSpan: int = Field(1, ge=1, le=6, description="网格跨度")


class FormFieldUpdateRequest(BaseModel):
    """更新表单字段请求"""
    label: Optional[str] = Field(None, description="字段标签")
    placeholder: Optional[str] = Field(None, description="占位符文本")
    required: Optional[bool] = Field(None, description="是否必填")
    readonly: Optional[bool] = Field(None, description="是否只读")
    hidden: Optional[bool] = Field(None, description="是否隐藏")
    validation: Optional[ValidationRule] = Field(None, description="验证配置")
    options: Optional[List[FieldOption]] = Field(None, description="选项配置")
    gridSpan: Optional[int] = Field(None, ge=1, le=6, description="网格跨度")


# 分页和统计模型
class PaginationInfo(BaseModel):
    """分页信息"""
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页数量")
    total: int = Field(..., description="总数量")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
