/**
 * AILF About页面 - 完全独立的样式系统
 * 设计理念：不继承主界面CSS，独立的滚动和动画系统
 */

/* 重置About页面的全局样式 - 完全独立 */
.about-page-container {
  /* 重置所有可能的继承样式 */
  all: initial;

  /* 设置基础样式 */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #1d1d1f;

  /* 确保滚动正常 */
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  scroll-behavior: smooth;

  /* 独立的背景 */
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 122, 255, 0.05) 0%, transparent 50%),
    linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);

  /* 确保在最顶层 */
  z-index: 9999;
}

/* About页面内容容器 */
.siri-about-page {
  /* 重置继承的样式 */
  position: relative;
  width: 100%;
  min-height: 100vh;

  /* 统一的渐变背景系统 */
  background:
    linear-gradient(180deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(248, 249, 250, 0.98) 15%,
      rgba(245, 246, 248, 0.95) 30%,
      rgba(248, 249, 250, 0.98) 45%,
      rgba(255, 255, 255, 1) 60%,
      rgba(248, 249, 250, 0.98) 75%,
      rgba(245, 246, 248, 0.95) 90%,
      rgba(255, 255, 255, 1) 100%
    ),
    radial-gradient(circle at 20% 20%, rgba(0, 122, 255, 0.05) 0%, transparent 40%),
    radial-gradient(circle at 80% 40%, rgba(175, 82, 222, 0.05) 0%, transparent 40%),
    radial-gradient(circle at 40% 80%, rgba(255, 107, 53, 0.05) 0%, transparent 40%);

  /* 页面加载动画 */
  opacity: 0;
  animation: pageEnter 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.siri-about-page.visible {
  animation-delay: 0.2s;
}

@keyframes pageEnter {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 页面容器 */
.siri-about-page {
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 122, 255, 0.05) 0%, transparent 50%),
    linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
  color: #1d1d1f;
  position: relative;
  width: 100%;
}

/* 页面加载动画 */
.siri-about-page {
  opacity: 0;
  animation: pageEnter 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.siri-about-page.visible {
  animation-delay: 0.2s;
}

@keyframes pageEnter {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Section之间的渐变过渡效果 */
.siri-scenarios::after,
.siri-workflow::after,
.siri-features::after,
.siri-privacy::after,
.siri-tech::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(0, 122, 255, 0.02) 25%,
    rgba(175, 82, 222, 0.02) 50%,
    rgba(255, 107, 53, 0.02) 75%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* 确保section内容在渐变叠加之上 */
.siri-scenarios > *,
.siri-workflow > *,
.siri-features > *,
.siri-privacy > *,
.siri-tech > *,
.siri-cta > * {
  position: relative;
  z-index: 2;
}

/* 为每个section添加微妙的渐变叠加 */
.siri-scenarios {
  position: relative;
}

.siri-scenarios::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 50%, rgba(0, 122, 255, 0.03) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.siri-workflow {
  position: relative;
}

.siri-workflow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 50%, rgba(175, 82, 222, 0.03) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.siri-features {
  position: relative;
}

.siri-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 30%, rgba(255, 107, 53, 0.03) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.siri-privacy {
  position: relative;
}

.siri-privacy::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 70%, rgba(120, 119, 198, 0.03) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.siri-tech {
  position: relative;
}

.siri-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 80%, rgba(0, 122, 255, 0.03) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.siri-cta {
  position: relative;
}

.siri-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(175, 82, 222, 0.04) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

/* 顶部导航 - 增强版 */
.siri-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(30px) saturate(180%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 16px 32px;
  transform: translateY(-100%);
  animation: navSlideDown 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s forwards;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05);
}

@keyframes navSlideDown {
  to {
    transform: translateY(0);
  }
}

.siri-nav-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  color: #007AFF;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.siri-nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.siri-nav-button:hover {
  background: rgba(0, 122, 255, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.siri-nav-button:hover::before {
  left: 100%;
}

.siri-nav-button:active {
  transform: translateY(0);
}

/* 英雄区域 - 增强版 */
.siri-hero {
  padding: 140px 24px 100px;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.siri-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 30% 30%, rgba(0, 122, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(175, 82, 222, 0.06) 0%, transparent 50%);
  animation: heroBackground 15s ease-in-out infinite;
  z-index: -1;
}

@keyframes heroBackground {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.siri-hero-content {
  margin-bottom: 80px;
  opacity: 0;
  animation: heroContentEnter 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
}

@keyframes heroContentEnter {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.siri-hero-title {
  margin: 0 0 32px;
  position: relative;
}

.siri-logo {
  font-size: 120px;
  font-weight: 700;
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 30%, #AF52DE 60%, #FF6B35 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.03em;
  display: block;
  animation: gradientShift 8s ease-in-out infinite;
  position: relative;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.siri-logo::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 30%, #AF52DE 60%, #FF6B35 100%);
  background-size: 200% 200%;
  animation: gradientShift 8s ease-in-out infinite;
  filter: blur(20px);
  opacity: 0.3;
  z-index: -1;
}

.siri-hero-subtitle {
  font-size: 36px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 28px;
  line-height: 1.2;
  opacity: 0;
  animation: subtitleEnter 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.2s forwards;
}

@keyframes subtitleEnter {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.siri-hero-description {
  font-size: 24px;
  font-weight: 400;
  color: #86868b;
  line-height: 1.5;
  margin: 0 auto;
  max-width: 700px;
  opacity: 0;
  animation: descriptionEnter 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.6s forwards;
}

@keyframes descriptionEnter {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 设备生态展示 - 增强版 */
.siri-devices {
  margin-top: 80px;
  opacity: 0;
  animation: devicesEnter 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 2s forwards;
}

@keyframes devicesEnter {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.device-showcase {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 32px;
  position: relative;
}

.device-showcase::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 122, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  animation: deviceGlow 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes deviceGlow {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

.device-item {
  font-size: 64px;
  opacity: 0.9;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: deviceFloat 4s ease-in-out infinite;
  cursor: pointer;
  position: relative;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.device-item:nth-child(1) {
  animation-delay: 0s;
}

.device-item:nth-child(2) {
  animation-delay: -1.3s;
}

.device-item:nth-child(3) {
  animation-delay: -2.6s;
}

.device-item:hover {
  opacity: 1;
  transform: scale(1.2) translateY(-8px);
  filter: drop-shadow(0 8px 16px rgba(0, 122, 255, 0.3));
}

.device-item::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(0, 122, 255, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.device-item:hover::before {
  opacity: 1;
}

.devices-text {
  font-size: 19px;
  color: #86868b;
  margin: 0;
  font-weight: 500;
  opacity: 0;
  animation: deviceTextEnter 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 2.5s forwards;
}

@keyframes deviceTextEnter {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes deviceFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-12px) rotate(1deg);
  }
  50% {
    transform: translateY(-8px) rotate(0deg);
  }
  75% {
    transform: translateY(-15px) rotate(-1deg);
  }
}

/* Section动画基础类 - 超流畅版本 */
.siri-scenarios,
.siri-workflow,
.siri-features,
.siri-privacy,
.siri-tech,
.siri-cta {
  opacity: 0;
  transform: translateY(80px) scale(0.95);
  filter: blur(8px);
  transition:
    opacity 1.2s cubic-bezier(0.16, 1, 0.3, 1),
    transform 1.2s cubic-bezier(0.16, 1, 0.3, 1),
    filter 1.2s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: opacity, transform, filter;
}

.siri-scenarios.animate-in,
.siri-workflow.animate-in,
.siri-features.animate-in,
.siri-privacy.animate-in,
.siri-tech.animate-in,
.siri-cta.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
  filter: blur(0px);
}

/* 分层动画延迟 */
.siri-scenarios.animate-in {
  transition-delay: 0.1s;
}

.siri-workflow.animate-in {
  transition-delay: 0.15s;
}

.siri-features.animate-in {
  transition-delay: 0.2s;
}

.siri-privacy.animate-in {
  transition-delay: 0.1s;
}

.siri-tech.animate-in {
  transition-delay: 0.2s;
}

.siri-cta.animate-in {
  transition-delay: 0.1s;
}

/* 情境化场景展示 - 使用统一渐变背景 */
.siri-scenarios {
  padding: 100px 24px;
  background: transparent;
  margin: 100px 0;
  position: relative;
  overflow: hidden;
}

/* 移除scenarios的独立背景效果，使用统一渐变 */

/* 移除patternMove动画 */

/* AILF工作流程 - 使用统一渐变背景 */
.siri-workflow {
  padding: 120px 24px;
  background: transparent;
  color: #1d1d1f;
  position: relative;
  overflow: hidden;
}

.siri-workflow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(175, 82, 222, 0.1) 0%, transparent 50%);
  animation: workflowBackground 20s ease-in-out infinite;
}

@keyframes workflowBackground {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
}

.workflow-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.workflow-header {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

.siri-workflow.animate-in .workflow-header {
  opacity: 1;
  transform: translateY(0);
}

.workflow-header h2 {
  font-size: 56px;
  font-weight: 700;
  margin: 0 0 20px;
  background: linear-gradient(135deg, #1d1d1f 0%, #007AFF 50%, #5856D6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.workflow-header p {
  font-size: 24px;
  color: #a1a1a6;
  margin: 0;
}

.workflow-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 80px;
  gap: 40px;
}

.workflow-step {
  flex: 1;
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

.siri-workflow.animate-in .workflow-step:nth-child(1) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}

.siri-workflow.animate-in .workflow-step:nth-child(3) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.6s;
}

.siri-workflow.animate-in .workflow-step:nth-child(5) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.9s;
}

.workflow-step:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 122, 255, 0.3);
  border-color: rgba(0, 122, 255, 0.5);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.step-visual {
  font-size: 48px;
  margin: 20px 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.workflow-step h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px;
  color: white;
}

.workflow-step p {
  font-size: 16px;
  color: #a1a1a6;
  line-height: 1.5;
  margin: 0;
}

.workflow-arrow {
  font-size: 32px;
  color: #007AFF;
  font-weight: bold;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.siri-workflow.animate-in .workflow-arrow {
  opacity: 1;
  transform: scale(1);
  transition-delay: 0.7s;
}

.workflow-demo {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 24px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateY(30px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

.siri-workflow.animate-in .workflow-demo {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1.2s;
}

.demo-showcase {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 60px;
}

.demo-input,
.demo-output {
  flex: 1;
  text-align: center;
}

.demo-label {
  display: block;
  font-size: 14px;
  color: #007AFF;
  font-weight: 600;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.demo-text {
  background: rgba(0, 122, 255, 0.2);
  color: #007AFF;
  padding: 16px 24px;
  border-radius: 16px;
  font-size: 18px;
  font-style: italic;
  border: 1px solid rgba(0, 122, 255, 0.3);
}

.demo-interface {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.demo-chart,
.demo-table,
.demo-controls {
  font-size: 32px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.demo-chart:hover,
.demo-table:hover,
.demo-controls:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.2);
}

.scenario-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  align-items: center;
  min-height: 500px;
  position: relative;
  z-index: 1;
}

.scenario-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.scenario-visual::before {
  content: '';
  position: absolute;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(0, 122, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: scenarioGlow 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes scenarioGlow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.scenario-icon {
  font-size: 140px;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
  animation: scenarioIconFloat 4s ease-in-out infinite;
}

@keyframes scenarioIconFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(2deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-12px) rotate(-2deg);
  }
}

.siri-scenarios.animate-in .scenario-icon {
  animation-play-state: running;
}

.scenario-content {
  max-width: 500px;
  opacity: 0;
  transform: translateX(30px);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.siri-scenarios.animate-in .scenario-content {
  opacity: 1;
  transform: translateX(0);
}

.scenario-title {
  font-size: 56px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 16px;
  line-height: 1.1;
  background: linear-gradient(135deg, #1d1d1f 0%, #333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.scenario-subtitle {
  font-size: 28px;
  font-weight: 600;
  color: #007AFF;
  margin: 0 0 28px;
  position: relative;
}

.scenario-subtitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #007AFF, #5856D6);
  border-radius: 2px;
}

.scenario-description {
  font-size: 21px;
  color: #86868b;
  line-height: 1.5;
  margin: 0 0 36px;
  font-weight: 400;
}

.scenario-quote {
  margin-top: 36px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s;
}

.siri-scenarios.animate-in .scenario-quote {
  opacity: 1;
  transform: translateY(0);
}

.quote-bubble {
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 24px;
  font-size: 18px;
  font-style: italic;
  font-weight: 500;
  position: relative;
  display: inline-block;
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.quote-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 122, 255, 0.4);
}

.quote-bubble::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 28px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #5856D6;
}

/* 场景指示器 - 增强版 */
.scenario-indicators {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 60px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.9s;
}

.siri-scenarios.animate-in .scenario-indicators {
  opacity: 1;
  transform: translateY(0);
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #d1d1d6;
  background: transparent;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: #007AFF;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translate(-50%, -50%);
}

.indicator:hover {
  border-color: #007AFF;
  transform: scale(1.1);
}

.indicator.active {
  border-color: #007AFF;
  background: #007AFF;
  transform: scale(1.3);
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
}

.indicator.active::before {
  width: 100%;
  height: 100%;
}

/* 核心特性 - 增强版 */
.siri-features {
  padding: 120px 24px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.siri-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(0, 122, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(175, 82, 222, 0.05) 0%, transparent 50%);
  z-index: -1;
}

.features-header {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.siri-features.animate-in .features-header {
  opacity: 1;
  transform: translateY(0);
}

.features-header h2 {
  font-size: 56px;
  font-weight: 700;
  background: linear-gradient(135deg, #1d1d1f 0%, #007AFF 50%, #5856D6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 20px;
  line-height: 1.1;
}

.features-header p {
  font-size: 24px;
  color: #86868b;
  margin: 0;
  font-weight: 400;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 48px;
}

.feature-item {
  text-align: center;
  padding: 48px 32px;
  border-radius: 24px;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(40px);
}

.siri-features.animate-in .feature-item {
  opacity: 1;
  transform: translateY(0);
}

.siri-features.animate-in .feature-item:nth-child(1) {
  transition-delay: 0.2s;
}

.siri-features.animate-in .feature-item:nth-child(2) {
  transition-delay: 0.4s;
}

.siri-features.animate-in .feature-item:nth-child(3) {
  transition-delay: 0.6s;
}

.siri-features.animate-in .feature-item:nth-child(4) {
  transition-delay: 0.8s;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.feature-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 122, 255, 0.2);
  border-color: rgba(0, 122, 255, 0.3);
}

.feature-item:hover::before {
  left: 100%;
}

.feature-visual {
  font-size: 64px;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.feature-item:hover .feature-visual {
  transform: scale(1.1);
}

.feature-item h3 {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 16px;
  line-height: 1.2;
}

.feature-item p {
  font-size: 18px;
  color: #86868b;
  line-height: 1.5;
  margin: 0;
  font-weight: 400;
}

/* 主要特性突出显示 */
.feature-primary {
  background:
    linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(88, 86, 214, 0.1) 100%);
  border: 2px solid rgba(0, 122, 255, 0.3);
  transform: scale(1.05);
}

.feature-primary .feature-visual {
  font-size: 72px;
  animation: primaryFeaturePulse 3s ease-in-out infinite;
}

@keyframes primaryFeaturePulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 4px 8px rgba(0, 122, 255, 0.3));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 8px 16px rgba(0, 122, 255, 0.5));
  }
}

.feature-primary h3 {
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.feature-primary:hover {
  transform: scale(1.08);
  box-shadow: 0 25px 70px rgba(0, 122, 255, 0.4);
}

/* 隐私保护 - 使用统一渐变背景 */
.siri-privacy {
  padding: 80px 24px;
  background: transparent;
  color: #1d1d1f;
  text-align: center;
}

.privacy-content {
  max-width: 800px;
  margin: 0 auto;
}

.privacy-content h2 {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 24px;
  line-height: 1.1;
}

.privacy-content p {
  font-size: 21px;
  color: #a1a1a6;
  line-height: 1.4;
  margin: 0 0 40px;
}

.privacy-features {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.privacy-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.privacy-icon {
  font-size: 32px;
}

.privacy-item span:last-child {
  font-size: 17px;
  color: #a1a1a6;
}

/* 技术生态 */
.siri-tech {
  padding: 80px 24px;
  max-width: 980px;
  margin: 0 auto;
}

.tech-content {
  text-align: center;
}

.tech-content h2 {
  font-size: 48px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 16px;
  line-height: 1.1;
}

.tech-content > p {
  font-size: 21px;
  color: #86868b;
  margin: 0 0 60px;
}

.tech-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.tech-category h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 20px;
}

.tech-items {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.tech-items span {
  background: #f5f5f7;
  color: #1d1d1f;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 500;
}

/* 行动召唤 - 使用统一渐变背景 */
.siri-cta {
  padding: 80px 24px;
  background: transparent;
  color: #1d1d1f;
  text-align: center;
}

.cta-content h2 {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 16px;
  line-height: 1.1;
}

.cta-content p {
  font-size: 21px;
  margin: 0 0 32px;
  opacity: 0.9;
}

.cta-button {
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 24px;
  font-size: 19px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
}

/* 页脚 */
.siri-footer {
  padding: 40px 24px;
  background: #f5f5f7;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer-content {
  max-width: 980px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-info p {
  font-size: 14px;
  color: #86868b;
  margin: 0;
  line-height: 1.4;
}

.footer-links {
  display: flex;
  gap: 24px;
}

.footer-link-button {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.footer-link-button:hover {
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .siri-logo {
    font-size: 64px;
  }
  
  .siri-hero-subtitle {
    font-size: 24px;
  }
  
  .siri-hero-description {
    font-size: 19px;
  }
  
  .scenario-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .scenario-title {
    font-size: 36px;
  }
  
  .features-header h2,
  .privacy-content h2,
  .tech-content h2,
  .cta-content h2 {
    font-size: 36px;
  }
  
  .device-showcase {
    gap: 20px;
  }
  
  .device-item {
    font-size: 36px;
  }
  
  .privacy-features {
    gap: 20px;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  /* 工作流程响应式 */
  .workflow-steps {
    flex-direction: column;
    gap: 20px;
  }

  .workflow-arrow {
    transform: rotate(90deg);
    margin: 10px 0;
  }

  .demo-showcase {
    flex-direction: column;
    gap: 30px;
  }

  .workflow-step {
    padding: 30px 20px;
  }

  .step-visual {
    font-size: 36px;
  }

  .workflow-header h2 {
    font-size: 36px;
  }
}
