/* AILF - Siri风格线条动画 */

/* Siri线条容器 */
.siri-lines {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  z-index: 10;
}

/* 单条线 */
.siri-line {
  width: 4px;
  background: linear-gradient(45deg, #00d4ff, #0099cc, #0066ff, #3366ff);
  border-radius: 2px;
  animation: siriWave 1.5s ease-in-out infinite;
  transform-origin: center;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* 线条波动动画 */
@keyframes siriWave {
  0%, 100% {
    height: 20px;
    opacity: 0.6;
    transform: scaleY(1);
  }
  50% {
    height: 80px;
    opacity: 1;
    transform: scaleY(1.2);
  }
}

/* 为每条线设置不同的延迟 */
.siri-line:nth-child(1) { animation-delay: 0s; }
.siri-line:nth-child(2) { animation-delay: 0.1s; }
.siri-line:nth-child(3) { animation-delay: 0.2s; }
.siri-line:nth-child(4) { animation-delay: 0.3s; }
.siri-line:nth-child(5) { animation-delay: 0.4s; }
.siri-line:nth-child(6) { animation-delay: 0.5s; }
.siri-line:nth-child(7) { animation-delay: 0.6s; }
.siri-line:nth-child(8) { animation-delay: 0.7s; }
.siri-line:nth-child(9) { animation-delay: 0.8s; }
.siri-line:nth-child(10) { animation-delay: 0.9s; }
.siri-line:nth-child(11) { animation-delay: 1s; }
.siri-line:nth-child(12) { animation-delay: 1.1s; }
.siri-line:nth-child(13) { animation-delay: 1.2s; }
.siri-line:nth-child(14) { animation-delay: 1.3s; }
.siri-line:nth-child(15) { animation-delay: 1.4s; }

/* 激活状态的线条 */
.siri-lines.active .siri-line {
  animation: siriActiveWave 0.8s ease-in-out infinite;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53, #ff6b9d, #c44569);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.7);
}

/* 激活状态的波动动画 */
@keyframes siriActiveWave {
  0%, 100% {
    height: 30px;
    opacity: 0.8;
    transform: scaleY(1);
  }
  50% {
    height: 100px;
    opacity: 1;
    transform: scaleY(1.5);
  }
}

/* 监听状态的线条 */
.siri-lines.listening .siri-line {
  animation: siriListeningWave 1.2s ease-in-out infinite;
  background: linear-gradient(45deg, #00ff88, #00cc6a, #00ff44, #33ff77);
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
}

/* 监听状态的波动动画 */
@keyframes siriListeningWave {
  0%, 100% {
    height: 25px;
    opacity: 0.7;
    transform: scaleY(1) rotateZ(0deg);
  }
  25% {
    height: 60px;
    opacity: 0.9;
    transform: scaleY(1.3) rotateZ(1deg);
  }
  50% {
    height: 90px;
    opacity: 1;
    transform: scaleY(1.6) rotateZ(0deg);
  }
  75% {
    height: 60px;
    opacity: 0.9;
    transform: scaleY(1.3) rotateZ(-1deg);
  }
}

/* 思考状态的线条 */
.siri-lines.thinking .siri-line {
  animation: siriThinkingWave 2s ease-in-out infinite;
  background: linear-gradient(45deg, #ffa500, #ff8c00, #ffb347, #ffd700);
  box-shadow: 0 0 12px rgba(255, 165, 0, 0.6);
}

/* 思考状态的波动动画 */
@keyframes siriThinkingWave {
  0%, 100% {
    height: 15px;
    opacity: 0.5;
    transform: scaleY(0.8);
  }
  33% {
    height: 40px;
    opacity: 0.8;
    transform: scaleY(1.1);
  }
  66% {
    height: 70px;
    opacity: 1;
    transform: scaleY(1.4);
  }
}

/* 错误状态的线条 */
.siri-lines.error .siri-line {
  animation: siriErrorWave 0.5s ease-in-out infinite;
  background: linear-gradient(45deg, #ff4757, #ff3838, #ff6b6b, #ff5252);
  box-shadow: 0 0 15px rgba(255, 71, 87, 0.8);
}

/* 错误状态的波动动画 */
@keyframes siriErrorWave {
  0%, 100% {
    height: 20px;
    opacity: 0.6;
    transform: scaleY(1) translateX(0);
  }
  25% {
    height: 35px;
    opacity: 0.8;
    transform: scaleY(1.2) translateX(-2px);
  }
  50% {
    height: 50px;
    opacity: 1;
    transform: scaleY(1.5) translateX(0);
  }
  75% {
    height: 35px;
    opacity: 0.8;
    transform: scaleY(1.2) translateX(2px);
  }
}

/* 成功状态的线条 */
.siri-lines.success .siri-line {
  animation: siriSuccessWave 1s ease-in-out infinite;
  background: linear-gradient(45deg, #2ed573, #1dd1a1, #00d2d3, #54a0ff);
  box-shadow: 0 0 18px rgba(46, 213, 115, 0.7);
}

/* 成功状态的波动动画 */
@keyframes siriSuccessWave {
  0%, 100% {
    height: 25px;
    opacity: 0.7;
    transform: scaleY(1);
  }
  50% {
    height: 75px;
    opacity: 1;
    transform: scaleY(1.8);
  }
}

/* 静音状态的线条 */
.siri-lines.muted .siri-line {
  animation: none;
  height: 10px;
  opacity: 0.3;
  background: linear-gradient(45deg, #747d8c, #57606f, #2f3542, #40407a);
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .siri-lines {
    width: 250px;
    height: 80px;
    gap: 2px;
  }
  
  .siri-line {
    width: 3px;
  }
}

@media (max-width: 480px) {
  .siri-lines {
    width: 200px;
    height: 60px;
    gap: 1px;
  }
  
  .siri-line {
    width: 2px;
  }
}
