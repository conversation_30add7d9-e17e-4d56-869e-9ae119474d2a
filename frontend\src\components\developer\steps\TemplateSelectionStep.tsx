/**
 * 模板选择步骤组件
 */

import React, { useState } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Tag, 
  Typography, 
  Space, 
  Spin, 
  Alert, 
  Select, 
  Input,
  Empty,
  Badge,
  Tooltip
} from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  InfoCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ApiOutlined,
  DatabaseOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTemplateManagement } from '../../../hooks/developer/useTemplateManagement';
import { templateAPI } from '../../../services/developer/templateAPI';
import type { TemplateListItem, TemplateCategory } from '../../../types/developer/template';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

interface TemplateSelectionStepProps {
  onTemplateSelect: (template: TemplateListItem) => void;
  onNext: () => void;
}

const TemplateSelectionStep: React.FC<TemplateSelectionStepProps> = ({
  onTemplateSelect,
  onNext,
}) => {
  const {
    selectedTemplate,
    templates,
    categories,
    selectedCategory,
    isLoading,
    error,
    selectTemplate,
    filterTemplates,
    clearError,
  } = useTemplateManagement();

  const [searchText, setSearchText] = useState('');

  // 处理模板选择
  const handleTemplateSelect = (template: TemplateListItem) => {
    selectTemplate(template);
    onTemplateSelect(template);
  };

  // 处理分类筛选
  const handleCategoryFilter = (category: TemplateCategory | null) => {
    filterTemplates(category);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 过滤模板
  const filteredTemplates = templates.filter(template => {
    if (!searchText) return true;
    const searchLower = searchText.toLowerCase();
    return (
      template.name.toLowerCase().includes(searchLower) ||
      template.description.toLowerCase().includes(searchLower) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  });

  // 获取难度标签颜色
  const getDifficultyColor = (difficulty: string) => {
    const info = templateAPI.getDifficultyInfo(difficulty);
    return info.color;
  };

  // 获取分类选项
  const categoryOptions = [
    { value: null, label: '全部分类' },
    ...templateAPI.getCategories().map(cat => ({
      value: cat.value,
      label: cat.label,
    })),
  ];

  return (
    <div className="template-selection-step">
      <div style={{ marginBottom: 32 }}>
        <Title level={2} style={{ margin: 0, color: '#1d1d1f' }}>
          选择业务场景模板
        </Title>
        <Paragraph style={{ margin: '8px 0 0', color: '#6e6e73', fontSize: 16 }}>
          从预设模板中选择最适合您业务场景的配置，快速开始系统搭建
        </Paragraph>
      </div>

      {/* 筛选和搜索 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Search
              placeholder="搜索模板名称、描述或标签..."
              allowClear
              size="large"
              prefix={<SearchOutlined />}
              onSearch={handleSearch}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ maxWidth: 400 }}
            />
          </Col>
          <Col>
            <Select
              placeholder="选择分类"
              size="large"
              style={{ width: 160 }}
              value={selectedCategory}
              onChange={handleCategoryFilter}
              suffixIcon={<FilterOutlined />}
            >
              {categoryOptions.map(option => (
                <Select.Option key={option.value || 'all'} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 模板列表 */}
      <Spin spinning={isLoading}>
        {filteredTemplates.length === 0 ? (
          <Empty
            description="没有找到匹配的模板"
            style={{ padding: '60px 0' }}
          />
        ) : (
          <Row gutter={[16, 16]}>
            {filteredTemplates.map((template) => (
              <Col xs={24} sm={12} lg={8} key={template.key}>
                <Card
                  hoverable
                  className={`template-card ${
                    selectedTemplate?.key === template.key ? 'selected' : ''
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                  style={{
                    height: '100%',
                    border: selectedTemplate?.key === template.key 
                      ? '2px solid #007aff' 
                      : '1px solid #d9d9d9',
                    position: 'relative',
                  }}
                  bodyStyle={{ padding: 20, height: '100%', display: 'flex', flexDirection: 'column' }}
                >
                  {/* 选中标识 */}
                  {selectedTemplate?.key === template.key && (
                    <div
                      style={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        color: '#007aff',
                        fontSize: 20,
                      }}
                    >
                      <CheckCircleOutlined />
                    </div>
                  )}

                  {/* 模板头部 */}
                  <div style={{ marginBottom: 16 }}>
                    <Title level={4} style={{ margin: 0, fontSize: 18 }}>
                      {template.name}
                    </Title>
                    <Space style={{ marginTop: 8 }}>
                      <Tag color={getDifficultyColor(template.difficulty)}>
                        {templateAPI.getDifficultyInfo(template.difficulty).label}
                      </Tag>
                      <Tag color="blue">
                        {templateAPI.getCategories().find(c => c.value === template.category)?.label}
                      </Tag>
                    </Space>
                  </div>

                  {/* 模板描述 */}
                  <Paragraph
                    style={{ 
                      flex: 1, 
                      color: '#6e6e73', 
                      fontSize: 14,
                      lineHeight: 1.5,
                      marginBottom: 16,
                    }}
                    ellipsis={{ rows: 3 }}
                  >
                    {template.description}
                  </Paragraph>

                  {/* 预览信息 */}
                  {template.preview && (
                    <div style={{ marginBottom: 16 }}>
                      <Row gutter={8}>
                        <Col span={12}>
                          <Tooltip title="实体数量">
                            <Space size={4}>
                              <DatabaseOutlined style={{ color: '#6e6e73' }} />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {template.preview.entities}
                              </Text>
                            </Space>
                          </Tooltip>
                        </Col>
                        <Col span={12}>
                          <Tooltip title="API数量">
                            <Space size={4}>
                              <ApiOutlined style={{ color: '#6e6e73' }} />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {template.preview.apis}
                              </Text>
                            </Space>
                          </Tooltip>
                        </Col>
                      </Row>
                    </div>
                  )}

                  {/* 底部信息 */}
                  <div>
                    <Space size={4} style={{ marginBottom: 8 }}>
                      <ClockCircleOutlined style={{ color: '#6e6e73', fontSize: 12 }} />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {template.estimated_setup_time}
                      </Text>
                    </Space>
                    
                    {/* 标签 */}
                    <div>
                      {template.tags.slice(0, 3).map(tag => (
                        <Tag key={tag} style={{ fontSize: 11, marginBottom: 4 }}>
                          {tag}
                        </Tag>
                      ))}
                      {template.tags.length > 3 && (
                        <Tag style={{ fontSize: 11 }}>
                          +{template.tags.length - 3}
                        </Tag>
                      )}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Spin>

      {/* 底部操作 */}
      {selectedTemplate && (
        <div style={{ marginTop: 32, textAlign: 'center' }}>
          <Button
            type="primary"
            size="large"
            onClick={onNext}
            style={{
              height: 48,
              fontSize: 16,
              fontWeight: 600,
              borderRadius: 8,
              minWidth: 200,
            }}
          >
            使用此模板继续配置
          </Button>
        </div>
      )}
    </div>
  );
};

export default TemplateSelectionStep;
