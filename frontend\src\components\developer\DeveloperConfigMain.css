/* 统一设计系统 - Apple风格开发者配置界面 */

:root {
  /* 主色调 */
  --primary-color: #007aff;
  --primary-light: #5ac8fa;
  --primary-dark: #0051d5;

  /* 中性色 */
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #a1a1a6;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f7;
  --bg-tertiary: #f2f2f7;
  --bg-card: rgba(255, 255, 255, 0.8);

  /* 边框色 */
  --border-light: rgba(0, 0, 0, 0.04);
  --border-medium: rgba(0, 0, 0, 0.08);
  --border-strong: rgba(0, 0, 0, 0.12);

  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.08);

  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
}

.developer-config-main {
  min-height: 100vh;
  background: linear-gradient(135deg, #fbfbfd 0%, #f7f8fc 50%, #f1f5f9 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  color: var(--text-primary);
}

/* 顶部导航栏 */
.config-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  padding: 20px 32px;
  backdrop-filter: blur(20px);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.main-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.6px;
}

.subtitle {
  padding: 6px 16px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border-radius: var(--radius-md);
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 2px 12px rgba(0, 122, 255, 0.25);
  letter-spacing: -0.2px;
}

.header-right .ant-btn {
  color: #6e6e73;
  border: none;
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 14px;
}

.logout-button {
  color: #ff3b30 !important;
}

.home-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  background: var(--primary-color) !important;
  border: none !important;
  border-radius: var(--radius-xl) !important;
  color: white !important;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.2px;
  box-shadow: 0 2px 12px rgba(0, 122, 255, 0.25);
}

.home-button:hover {
  background: var(--primary-dark) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 122, 255, 0.35);
}

/* 主要内容区域 */
.config-main {
  display: flex;
  width: 100%;
  min-height: calc(100vh - 88px);
}

/* 左侧步骤导航 - 统一设计 */
.steps-sidebar {
  width: 340px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-light);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 40px 0;
  position: relative;
  box-shadow: var(--shadow-md);
}

.steps-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(248, 250, 252, 0.08) 30%,
    rgba(241, 245, 249, 0.05) 70%,
    rgba(255, 255, 255, 0.12) 100%
  );
  pointer-events: none;
}

.steps-container {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.steps-header {
  margin-bottom: 32px;
}

.steps-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.5px;
}

.steps-subtitle {
  margin: 0;
  font-size: 15px;
  color: var(--text-secondary);
  line-height: 1.5;
  font-weight: 500;
}

.steps-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px 0;
}

.step-item {
  display: flex;
  align-items: flex-start;
  padding: 20px 16px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  margin: 0 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.step-item.enabled:hover {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
  transform: translateX(6px);
  box-shadow: var(--shadow-md);
}

.step-item.active {
  background:
    linear-gradient(135deg,
      rgba(0, 122, 255, 0.15) 0%,
      rgba(59, 130, 246, 0.12) 100%
    ),
    rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 122, 255, 0.4);
  box-shadow:
    0 4px 20px rgba(0, 122, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateX(6px);
  position: relative;
  z-index: 1;
}

.step-item.completed {
  background:
    linear-gradient(135deg,
      rgba(52, 199, 89, 0.15) 0%,
      rgba(34, 197, 94, 0.12) 100%
    ),
    rgba(255, 255, 255, 0.3);
  border-color: rgba(52, 199, 89, 0.4);
  box-shadow: 0 2px 10px rgba(52, 199, 89, 0.2);
}

.step-item.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.step-item.active .step-title {
  color: var(--primary-color);
  font-weight: 700;
}

.step-item.active .step-description {
  color: var(--text-secondary);
}

.step-item.completed .step-title {
  color: #34c759;
  font-weight: 600;
}

.step-item.completed .step-description {
  color: var(--text-secondary);
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  position: relative;
}

.step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  border: 2px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-item.active .step-number {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 3px 12px rgba(0, 122, 255, 0.3);
}

.step-item.completed .step-number {
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(52, 199, 89, 0.2);
}

.step-connector {
  width: 2px;
  height: 40px;
  background: #e5e5ea;
  margin-top: 8px;
  transition: all 0.2s ease;
}

.step-item.completed .step-connector {
  background: #34c759;
}

.step-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.step-icon {
  font-size: 16px;
  color: #6e6e73;
  transition: all 0.2s ease;
}

.step-item.active .step-icon {
  color: #007aff;
}

.step-item.completed .step-icon {
  color: #34c759;
}

.step-info {
  flex: 1;
}

.step-title {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.2px;
}

.step-description {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.steps-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.progress-info {
  text-align: center;
}

.progress-text {
  font-size: 13px;
  color: #6e6e73;
  margin-bottom: 8px;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e5ea;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff 0%, #5856d6 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 右侧内容区域 - 统一设计 */
.content-area {
  flex: 1;
  background: var(--bg-primary);
  backdrop-filter: blur(20px);
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  margin: 16px 16px 16px 0;
  box-shadow: var(--shadow-md);
}

.content-container {
  padding: 48px;
  height: 100%;
  overflow-y: auto;
}

/* 统一卡片样式 */
.ant-card {
  border: 1px solid var(--border-light) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-sm) !important;
  background: var(--bg-primary) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-card:hover {
  box-shadow: var(--shadow-md) !important;
  border-color: var(--border-medium) !important;
}

.ant-card .ant-card-head {
  border-bottom: 1px solid var(--border-light) !important;
  background: var(--bg-secondary) !important;
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
}

.ant-card .ant-card-head-title {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  font-size: 18px !important;
  letter-spacing: -0.3px !important;
}

/* 统一表单样式 */
.ant-form-item-label > label {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  letter-spacing: -0.1px !important;
}

.ant-input,
.ant-input:focus,
.ant-input:hover {
  border: 1px solid var(--border-medium) !important;
  border-radius: var(--radius-sm) !important;
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1) !important;
}

/* 统一按钮样式 */
.ant-btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
  border: none !important;
  border-radius: var(--radius-sm) !important;
  font-weight: 600 !important;
  letter-spacing: -0.1px !important;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3) !important;
}

/* 步骤占位符样式 */
.step-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.placeholder-content {
  text-align: center;
  max-width: 400px;
}

.placeholder-icon {
  font-size: 48px;
  color: #007aff;
  margin-bottom: 24px;
}

.placeholder-title {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1d1d1f;
  letter-spacing: -0.5px;
}

.placeholder-description {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6e6e73;
  line-height: 1.5;
}

.placeholder-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #34c759;
}

.feature-item .anticon {
  font-size: 16px;
}

/* 模板选择步骤样式 */
.template-selection-step {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: calc(100vh - 200px);
  padding: 24px;
  border-radius: 12px;
}

.template-selection-step .template-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.template-selection-step .template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e1;
}

.template-selection-step .template-card.selected {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 122, 255, 0.3);
  border-color: #007aff;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.template-selection-step .ant-card-body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 增强搜索和筛选区域 */
.template-selection-step .ant-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 增强标题对比度 */
.template-selection-step h2 {
  color: #1e293b !important;
  font-weight: 700;
}

.template-selection-step .ant-typography {
  color: #475569 !important;
}

/* 增强模板卡片内容对比度 */
.template-selection-step .template-card h4 {
  color: #1e293b !important;
  font-weight: 600;
}

.template-selection-step .template-card .ant-typography {
  color: #64748b !important;
}

/* 增强标签对比度 */
.template-selection-step .ant-tag {
  border: 1px solid #e2e8f0;
  background: #f8fafc;
  color: #475569;
  font-weight: 500;
}

/* 场景配置步骤样式 */
.scenario-config-step {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  min-height: calc(100vh - 200px);
  border-radius: 12px;
}

.scenario-config-step .ant-card {
  border: 2px solid #e2e8f0 !important;
  background: #ffffff !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px !important;
}

.scenario-config-step .ant-card .ant-card-head {
  border-bottom: 2px solid #e2e8f0 !important;
  background: #f8fafc !important;
}

.scenario-config-step .ant-card .ant-card-head-title {
  color: #0f172a !important;
  font-weight: 700 !important;
  font-size: 16px !important;
}

.scenario-config-step .ant-form-item-label > label {
  color: #0f172a !important;
  font-weight: 700 !important;
  font-size: 14px !important;
}

.scenario-config-step .ant-input,
.scenario-config-step .ant-input:focus,
.scenario-config-step .ant-input:hover {
  border: 2px solid #cbd5e1 !important;
  background: #ffffff !important;
  color: #1e293b !important;
  font-weight: 500 !important;
}

.scenario-config-step .ant-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15) !important;
}

.scenario-config-step .ant-typography h2 {
  color: #0f172a !important;
  font-weight: 700 !important;
}

.scenario-config-step .ant-typography {
  color: #475569 !important;
  font-weight: 500 !important;
}

/* 场景类型选择卡片 - 统一设计 */
.scenario-type-card {
  border: 1px solid var(--border-light) !important;
  background: var(--bg-primary) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-sm) !important;
}

.scenario-type-card:hover {
  border-color: var(--border-medium) !important;
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-3px) !important;
}

.scenario-type-card.selected {
  border-color: var(--primary-color) !important;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.04) 0%, rgba(88, 86, 214, 0.02) 100%) !important;
  box-shadow: 0 8px 32px rgba(0, 122, 255, 0.15) !important;
}

.scenario-type-card .ant-card-body {
  padding: 24px !important;
}

.scenario-type-card h5 {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  margin-bottom: 8px !important;
  font-size: 16px !important;
  letter-spacing: -0.2px !important;
}

.scenario-type-card .ant-typography {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

/* 统一标签样式 */
.ant-tag {
  border: 1px solid var(--border-light) !important;
  background: var(--bg-secondary) !important;
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  border-radius: var(--radius-sm) !important;
  padding: 6px 12px !important;
  font-size: 13px !important;
  letter-spacing: -0.1px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-tag:hover {
  border-color: var(--border-medium) !important;
  background: var(--bg-tertiary) !important;
}

.ant-tag .anticon-close {
  color: var(--text-tertiary) !important;
  transition: color 0.3s ease !important;
}

.ant-tag .anticon-close:hover {
  color: #ef4444 !important;
}

/* 统一页面标题样式 */
.page-title {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  font-size: 28px !important;
  letter-spacing: -0.7px !important;
  margin-bottom: 8px !important;
}

.page-subtitle {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
  margin-bottom: 32px !important;
}

/* 统一Typography样式 */
.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4,
.ant-typography h5 {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  letter-spacing: -0.3px !important;
}

.ant-typography {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

/* 实体建模步骤样式 */
.entity-modeling-step {
  background: var(--bg-primary);
  min-height: calc(100vh - 200px);
}

.entity-modeling-step .step-header {
  margin-bottom: 32px;
}

.entity-modeling-step .ant-tabs {
  background: transparent;
}

.entity-modeling-step .ant-tabs-tab {
  font-weight: 600 !important;
  color: var(--text-secondary) !important;
  font-size: 16px !important;
}

.entity-modeling-step .ant-tabs-tab-active {
  color: var(--primary-color) !important;
}

.entity-modeling-step .ant-tabs-ink-bar {
  background: var(--primary-color) !important;
}

.entity-modeling-step .ant-table {
  background: var(--bg-primary) !important;
}

.entity-modeling-step .ant-table-thead > tr > th {
  background: var(--bg-secondary) !important;
  border-bottom: 2px solid var(--border-light) !important;
  color: var(--text-primary) !important;
  font-weight: 700 !important;
}

.entity-modeling-step .ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--border-light) !important;
  color: var(--text-primary) !important;
}

.entity-modeling-step .ant-table-tbody > tr:hover > td {
  background: var(--bg-secondary) !important;
}

.entity-modeling-step .step-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-modeling-step .ant-modal .ant-modal-header {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-light) !important;
}

.entity-modeling-step .ant-modal .ant-modal-title {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
}

/* 操作列样式优化 */
.entity-modeling-step .ant-table-tbody > tr > td:last-child {
  white-space: nowrap;
  text-align: center;
}

.entity-modeling-step .ant-table-tbody .ant-space-item .ant-btn {
  padding: 4px 8px !important;
  height: auto !important;
  line-height: 1.2 !important;
}

.entity-modeling-step .ant-table-tbody .ant-btn-link {
  color: var(--primary-color) !important;
}

.entity-modeling-step .ant-table-tbody .ant-btn-link:hover {
  color: var(--primary-light) !important;
}

.entity-modeling-step .ant-table-tbody .ant-btn-dangerous {
  color: #ff4d4f !important;
}

.entity-modeling-step .ant-table-tbody .ant-btn-dangerous:hover {
  color: #ff7875 !important;
}

/* 工作流设计步骤样式 */
.workflow-design-step {
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.workflow-design-step .ant-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 122, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.workflow-design-step .ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.workflow-design-step .ant-statistic-title {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.workflow-design-step .ant-statistic-content {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
}

.workflow-design-step .ant-tabs-tab {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  margin-right: 8px !important;
  transition: all 0.3s ease !important;
}

.workflow-design-step .ant-tabs-tab:hover {
  color: var(--primary-color) !important;
  background: rgba(0, 122, 255, 0.05) !important;
}

.workflow-design-step .ant-tabs-tab-active {
  color: var(--primary-color) !important;
  background: rgba(0, 122, 255, 0.1) !important;
  font-weight: 600 !important;
}

.workflow-design-step .ant-table {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
}

.workflow-design-step .ant-table-thead > tr > th {
  background: rgba(248, 249, 250, 0.8) !important;
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  border-bottom: 1px solid rgba(0, 122, 255, 0.1) !important;
}

.workflow-design-step .ant-table-tbody > tr {
  transition: all 0.3s ease !important;
}

.workflow-design-step .ant-table-tbody > tr:hover {
  background: rgba(0, 122, 255, 0.02) !important;
}

.workflow-design-step .ant-table-tbody > tr > td:last-child {
  white-space: nowrap;
  text-align: center;
}

.workflow-design-step .ant-table-tbody .ant-space-item .ant-btn {
  padding: 4px 8px !important;
  height: auto !important;
  line-height: 1.2 !important;
}

.workflow-design-step .ant-table-tbody .ant-btn-link {
  color: var(--primary-color) !important;
}

.workflow-design-step .ant-table-tbody .ant-btn-link:hover {
  color: var(--primary-light) !important;
}

.workflow-design-step .ant-table-tbody .ant-btn-dangerous {
  color: #ff4d4f !important;
}

.workflow-design-step .ant-table-tbody .ant-btn-dangerous:hover {
  color: #ff7875 !important;
}

.workflow-design-step .ant-modal .ant-modal-title {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
}

.workflow-design-step .ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  padding: 2px 8px !important;
}

.workflow-design-step .ant-badge-status-text {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .config-main {
    flex-direction: column;
  }
  
  .steps-sidebar {
    width: 100%;
    padding: 24px 0;
  }
  
  .steps-list {
    flex-direction: row;
    overflow-x: auto;
    gap: 8px;
    padding-bottom: 16px;
  }
  
  .step-item {
    min-width: 200px;
    flex-shrink: 0;
  }
  
  .step-connector {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .content-container {
    padding: 24px;
  }
}
