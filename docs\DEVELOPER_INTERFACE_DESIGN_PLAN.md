# AILF 开发者界面设计规划

## 📋 概述

基于API文档设计的开发者配置界面，实现八步骤配置流程。

## ️ 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ Header: AILF 开发者配置面板 [退出] [返回主页]              │
├─────────────────────────────────────────────────────────┤
│ Progress Steps: [1认证] [2场景] [3模板] [4实体] [5工作流]  │
│                [6表单] [7API] [8角色] [9生成]             │
├─────────────────────────────────────────────────────────┤
│                  Step Content Area                      │
├─────────────────────────────────────────────────────────┤
│ Footer: [上一步] [保存] [下一步]                         │
└─────────────────────────────────────────────────────────┘
```

## 📝 配置步骤界面

### 步骤1: 开发者认证
- 密码输入框
- 记住登录选项
- 错误提示

### 步骤2: 场景配置
- 场景类型选择器（卡片布局）
- 场景名称输入
- 业务领域配置
- 目标用户多选

### 步骤3: 模板选择
- 模板分类筛选
- 模板卡片展示
- 模板详情查看
- 跳过模板选项

### 步骤4: 实体建模
- 实体关系画布（React Flow）
- 实体编辑面板
- 字段类型选择器
- 关系连线工具

### 步骤5: 工作流设计
- 工作流画布（React Flow）
- 步骤类型面板
- 步骤配置编辑器
- 条件分支配置

### 步骤6: 表单配置
- 表单构建器
- 字段拖拽排序
- 布局配置
- 表单预览

### 步骤7: API路由管理
- API路由列表
- 路由编辑器
- 参数配置
- API测试工具

### 步骤8: 角色权限配置
- 角色管理面板
- 权限矩阵表格
- 角色级别配置
- 权限分配界面

### 步骤9: 代码生成部署
- 生成进度显示
- 实时日志输出
- 生成结果预览
- 系统激活控制

## 📱 核心组件

### 主要组件
1. `StepNavigator` - 步骤导航器
2. `EntityCanvas` - 实体关系画布
3. `WorkflowCanvas` - 工作流设计画布
4. `FormBuilder` - 表单构建器
5. `APITester` - API测试工具
6. `PermissionMatrix` - 权限矩阵
7. `CodePreview` - 代码预览器
8. `ProgressIndicator` - 进度指示器

### 通用组件
1. `ConfigPanel` - 配置面板
2. `PropertyEditor` - 属性编辑器
3. `DataTable` - 数据表格
4. `SearchFilter` - 搜索筛选器
5. `ConfirmDialog` - 确认对话框

## 🎨 设计规范

### 主题
- 浅色玻璃主题
- Apple风格设计
- 圆角: 8px/12px/20px/24px
- 玻璃效果: 毛玻璃背景模糊
- 动画: 0.3s cubic-bezier(0.4, 0, 0.2, 1)

### 颜色
- 背景: #f8f9fa, rgba(255,255,255,0.8), rgba(255,255,255,0.6)
- 文字: #1d1d1f, #6e6e73, #8e8e93
- 强调: #007aff, #34c759, #ff9500, #ff3b30
- 玻璃: rgba(255,255,255,0.25) + blur(20px)

### 玻璃效果特性
- 背景模糊: blur(20px)
- 半透明背景: rgba(255,255,255,0.25)
- 边框高光: rgba(255,255,255,0.3)
- 渐变背景: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)
- 微妙纹理和光泽动画

## 📁 文件结构规划

```
frontend/src/
├── pages/
│   └── ConfigPage.tsx                    # 开发者配置主页面
│
├── components/
│   └── developer/                        # 开发者界面组件目录
│       ├── DeveloperConfigMain.tsx       # 主配置组件
│       │
│       ├── steps/                        # 配置步骤组件
│       │   ├── AuthenticationStep.tsx    # 步骤1: 开发者认证
│       │   ├── ScenarioConfigStep.tsx    # 步骤2: 场景配置
│       │   ├── TemplateSelectionStep.tsx # 步骤3: 模板选择
│       │   ├── EntityModelingStep.tsx    # 步骤4: 实体建模
│       │   ├── WorkflowDesignStep.tsx    # 步骤5: 工作流设计
│       │   ├── FormConfigStep.tsx        # 步骤6: 表单配置
│       │   ├── APIRouteStep.tsx          # 步骤7: API路由管理
│       │   ├── RolePermissionStep.tsx    # 步骤8: 角色权限配置
│       │   └── CodeGenerationStep.tsx    # 步骤9: 代码生成部署
│       │
│       ├── canvas/                       # 可视化画布组件
│       │   ├── EntityCanvas.tsx          # 实体关系画布
│       │   ├── WorkflowCanvas.tsx        # 工作流设计画布
│       │   ├── EntityNode.tsx            # 实体节点组件
│       │   ├── WorkflowNode.tsx          # 工作流节点组件
│       │   └── CanvasToolbar.tsx         # 画布工具栏
│       │
│       ├── builders/                     # 构建器组件
│       │   ├── FormBuilder.tsx           # 表单构建器
│       │   ├── EntityBuilder.tsx         # 实体构建器
│       │   ├── RouteBuilder.tsx          # 路由构建器
│       │   ├── FieldEditor.tsx           # 字段编辑器
│       │   └── ValidationEditor.tsx      # 验证规则编辑器
│       │
│       ├── panels/                       # 配置面板组件
│       │   ├── ScenarioPanel.tsx         # 场景配置面板
│       │   ├── TemplatePanel.tsx         # 模板选择面板
│       │   ├── EntityPanel.tsx           # 实体配置面板
│       │   ├── WorkflowPanel.tsx         # 工作流配置面板
│       │   ├── FormPanel.tsx             # 表单配置面板
│       │   ├── RoutePanel.tsx            # 路由配置面板
│       │   └── RolePanel.tsx             # 角色配置面板
│       │
│       ├── common/                       # 通用组件
│       │   ├── StepNavigator.tsx         # 步骤导航器
│       │   ├── ConfigPanel.tsx           # 配置面板
│       │   ├── PropertyEditor.tsx        # 属性编辑器
│       │   ├── ProgressIndicator.tsx     # 进度指示器
│       │   ├── DataTable.tsx             # 数据表格
│       │   ├── SearchFilter.tsx          # 搜索筛选器
│       │   ├── ConfirmDialog.tsx         # 确认对话框
│       │   ├── LoadingSpinner.tsx        # 加载动画
│       │   └── ErrorBoundary.tsx         # 错误边界
│       │
│       └── tools/                        # 工具组件
│           ├── APITester.tsx             # API测试工具
│           ├── CodePreview.tsx           # 代码预览器
│           ├── PermissionMatrix.tsx      # 权限矩阵
│           ├── LogViewer.tsx             # 日志查看器
│           └── FileExplorer.tsx          # 文件浏览器
│
├── hooks/                                # 自定义Hooks
│   ├── developer/                        # 开发者相关Hooks
│   │   ├── useDeveloperConfig.ts         # 开发者配置状态管理
│   │   ├── useStepNavigation.ts          # 步骤导航逻辑
│   │   ├── useEntityCanvas.ts            # 实体画布逻辑
│   │   ├── useWorkflowCanvas.ts          # 工作流画布逻辑
│   │   ├── useFormBuilder.ts             # 表单构建器逻辑
│   │   ├── useAPITester.ts               # API测试逻辑
│   │   └── useCodeGeneration.ts          # 代码生成逻辑
│   │
│   └── common/                           # 通用Hooks
│       ├── useAPIClient.ts               # API客户端
│       ├── useLocalStorage.ts            # 本地存储
│       ├── useDebounce.ts                # 防抖处理
│       └── useValidation.ts              # 表单验证
│
├── services/                             # API服务
│   ├── developer/                        # 开发者相关API
│   │   ├── authAPI.ts                    # 认证API
│   │   ├── scenarioAPI.ts                # 场景管理API
│   │   ├── templateAPI.ts                # 模板管理API
│   │   ├── entityAPI.ts                  # 实体管理API
│   │   ├── workflowAPI.ts                # 工作流管理API
│   │   ├── formAPI.ts                    # 表单管理API
│   │   ├── routeAPI.ts                   # 路由管理API
│   │   ├── roleAPI.ts                    # 角色管理API
│   │   ├── permissionAPI.ts              # 权限管理API
│   │   └── codeGenAPI.ts                 # 代码生成API
│   │
│   └── common/                           # 通用服务
│       ├── apiClient.ts                  # 统一API客户端
│       ├── errorHandler.ts               # 错误处理
│       └── requestInterceptor.ts         # 请求拦截器
│
├── types/                                # TypeScript类型定义
│   ├── developer/                        # 开发者相关类型
│   │   ├── auth.ts                       # 认证相关类型
│   │   ├── scenario.ts                   # 场景相关类型
│   │   ├── template.ts                   # 模板相关类型
│   │   ├── entity.ts                     # 实体相关类型
│   │   ├── workflow.ts                   # 工作流相关类型
│   │   ├── form.ts                       # 表单相关类型
│   │   ├── route.ts                      # 路由相关类型
│   │   ├── role.ts                       # 角色相关类型
│   │   ├── permission.ts                 # 权限相关类型
│   │   └── codeGen.ts                    # 代码生成相关类型
│   │
│   └── common/                           # 通用类型
│       ├── api.ts                        # API响应类型
│       ├── ui.ts                         # UI组件类型
│       └── canvas.ts                     # 画布相关类型
│
├── utils/                                # 工具函数
│   ├── developer/                        # 开发者相关工具
│   │   ├── configValidator.ts            # 配置验证工具
│   │   ├── codeGenerator.ts              # 代码生成工具
│   │   ├── templateProcessor.ts          # 模板处理工具
│   │   └── entityHelper.ts               # 实体辅助工具
│   │
│   └── common/                           # 通用工具
│       ├── formatters.ts                 # 格式化工具
│       ├── validators.ts                 # 验证工具
│       ├── constants.ts                  # 常量定义
│       └── helpers.ts                    # 辅助函数
│
├── styles/                               # 样式文件
│   ├── developer/                        # 开发者界面样式
│   │   ├── developer-config.css          # 主配置样式
│   │   ├── steps.css                     # 步骤样式
│   │   ├── canvas.css                    # 画布样式
│   │   ├── builders.css                  # 构建器样式
│   │   ├── panels.css                    # 面板样式
│   │   └── tools.css                     # 工具样式
│   │
│   └── common/                           # 通用样式
│       ├── components.css                # 组件样式
│       ├── variables.css                 # CSS变量
│       └── animations.css                # 动画样式
│
└── assets/                               # 静态资源
    ├── icons/                            # 图标文件
    │   ├── steps/                        # 步骤图标
    │   ├── entities/                     # 实体图标
    │   ├── workflows/                    # 工作流图标
    │   └── tools/                        # 工具图标
    │
    └── images/                           # 图片文件
        ├── templates/                    # 模板预览图
        └── backgrounds/                  # 背景图片
```


