# 代码生成模块 API 文档

## 📋 概述

代码生成模块提供基于配置自动生成完整系统代码的功能，包括数据库结构、API代码、前端界面等。

## 🚀 API 端点列表

### 基础代码生成
1. 根据实体配置生成数据库表结构
2. 根据API配置生成API路由代码
3. 根据权限配置生成权限控制代码

### 扩展代码生成
4. 根据实体配置生成数据库表和模型代码
5. 根据工作流配置生成工作流执行代码
6. 根据表单配置生成前端表单组件
7. 生成完整前端页面代码

### 完整系统生成
8. 一键生成完整系统代码
9. 激活当前场景，使其生效
10. 获取代码生成状态
11. 获取代码生成日志

---

## API 详细文档

### 1. 根据实体配置生成数据库表结构

**POST** `/api/generate-database`

#### 描述
根据已配置的实体定义生成对应的数据库表结构和索引。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "entities": ["string"],
  "options": {
    "drop_existing": boolean,
    "create_indexes": boolean,
    "add_timestamps": boolean,
    "add_soft_delete": boolean
  }
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entities | array | 否 | 指定实体ID列表，为空则生成所有实体 |
| options.drop_existing | boolean | 否 | 是否删除已存在的表，默认false |
| options.create_indexes | boolean | 否 | 是否创建索引，默认true |
| options.add_timestamps | boolean | 否 | 是否添加时间戳字段，默认true |
| options.add_soft_delete | boolean | 否 | 是否添加软删除字段，默认false |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/generate-database" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "entities": ["entity_product", "entity_order"],
    "options": {
      "drop_existing": false,
      "create_indexes": true,
      "add_timestamps": true,
      "add_soft_delete": false
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "数据库表结构生成成功",
  "data": {
    "generated_tables": [
      {
        "entity_id": "entity_product",
        "table_name": "products",
        "columns": [
          {
            "name": "id",
            "type": "INTEGER",
            "primary_key": true,
            "auto_increment": true
          },
          {
            "name": "name",
            "type": "VARCHAR(100)",
            "nullable": false
          },
          {
            "name": "price",
            "type": "DECIMAL(10,2)",
            "nullable": false
          },
          {
            "name": "category",
            "type": "VARCHAR(50)",
            "nullable": false
          },
          {
            "name": "created_at",
            "type": "DATETIME",
            "nullable": false,
            "default": "CURRENT_TIMESTAMP"
          },
          {
            "name": "updated_at",
            "type": "DATETIME",
            "nullable": false,
            "default": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
          }
        ],
        "indexes": [
          {
            "name": "idx_products_category",
            "columns": ["category"]
          },
          {
            "name": "idx_products_price",
            "columns": ["price"]
          }
        ]
      }
    ],
    "sql_scripts": [
      "CREATE TABLE products (...);",
      "CREATE INDEX idx_products_category ON products (category);",
      "CREATE INDEX idx_products_price ON products (price);"
    ],
    "summary": {
      "tables_created": 2,
      "indexes_created": 4,
      "total_columns": 12
    }
  }
}
```

---

### 2. 根据API配置生成API路由代码

**POST** `/api/generate-apis`

#### 描述
根据已注册的API配置生成对应的路由处理代码。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "apis": ["string"],
  "options": {
    "include_validation": boolean,
    "include_documentation": boolean,
    "include_tests": boolean,
    "framework": "string"
  }
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/generate-apis" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "apis": ["product_list_api", "product_create_api"],
    "options": {
      "include_validation": true,
      "include_documentation": true,
      "include_tests": false,
      "framework": "fastapi"
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "API代码生成成功",
  "data": {
    "generated_files": [
      {
        "file_path": "app/api/v1/products.py",
        "file_type": "route",
        "content_preview": "@router.get(\"/products\")\nasync def get_products(...):",
        "lines": 156
      },
      {
        "file_path": "app/schemas/product.py",
        "file_type": "schema",
        "content_preview": "class ProductCreate(BaseModel):",
        "lines": 45
      }
    ],
    "summary": {
      "files_generated": 4,
      "total_lines": 567,
      "apis_implemented": 2,
      "validation_rules": 8
    }
  }
}
```

---

### 3. 根据权限配置生成权限控制代码

**POST** `/api/generate-permissions`

#### 描述
根据权限配置生成权限控制相关的代码文件。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "roles": ["string"],
  "permissions": ["string"],
  "options": {
    "include_middleware": boolean,
    "include_decorators": boolean,
    "framework": "string"
  }
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/generate-permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "roles": ["admin", "user"],
    "permissions": ["products:read", "products:write"],
    "options": {
      "include_middleware": true,
      "include_decorators": true,
      "framework": "fastapi"
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "权限控制代码生成成功",
  "data": {
    "generated_files": [
      {
        "file_path": "app/auth/permissions.py",
        "file_type": "permission_checker",
        "content_preview": "def check_permission(user, permission):",
        "lines": 89
      },
      {
        "file_path": "app/auth/middleware.py",
        "file_type": "middleware",
        "content_preview": "class PermissionMiddleware:",
        "lines": 45
      }
    ],
    "summary": {
      "files_generated": 3,
      "total_lines": 234,
      "roles_implemented": 2,
      "permissions_implemented": 2
    }
  }
}
```

---

### 4. 一键生成完整系统代码

**POST** `/api/generate-complete`

#### 描述
基于当前场景配置一键生成完整的系统代码，包括前后端所有组件。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "options": {
    "include_frontend": boolean,
    "include_backend": boolean,
    "include_database": boolean,
    "include_tests": boolean,
    "include_documentation": boolean,
    "deployment_config": boolean
  },
  "target_directory": "string"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/generate-complete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "options": {
      "include_frontend": true,
      "include_backend": true,
      "include_database": true,
      "include_tests": true,
      "include_documentation": true,
      "deployment_config": true
    },
    "target_directory": "/generated/ecommerce_system"
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "完整系统代码生成成功",
  "data": {
    "generation_id": "gen_1705123456789",
    "target_directory": "/generated/ecommerce_system",
    "components": {
      "database": {
        "tables_created": 5,
        "indexes_created": 12,
        "migration_files": 3
      },
      "backend": {
        "api_files": 8,
        "model_files": 5,
        "service_files": 6,
        "total_lines": 2847
      },
      "frontend": {
        "page_files": 12,
        "component_files": 25,
        "form_files": 8,
        "total_lines": 4521
      },
      "tests": {
        "unit_tests": 45,
        "integration_tests": 12,
        "coverage": "85%"
      },
      "documentation": {
        "api_docs": 1,
        "user_guide": 1,
        "deployment_guide": 1
      }
    },
    "file_structure": {
      "backend/": {
        "app/": {
          "api/": ["products.py", "orders.py", "users.py"],
          "models/": ["product.py", "order.py", "user.py"],
          "services/": ["product_service.py", "order_service.py"],
          "schemas/": ["product.py", "order.py"]
        },
        "tests/": ["test_products.py", "test_orders.py"],
        "requirements.txt": null,
        "main.py": null
      },
      "frontend/": {
        "src/": {
          "pages/": ["ProductList.tsx", "OrderManagement.tsx"],
          "components/": ["ProductForm.tsx", "OrderForm.tsx"],
          "services/": ["api.ts", "auth.ts"]
        },
        "package.json": null
      },
      "database/": {
        "migrations/": ["001_create_products.sql", "002_create_orders.sql"],
        "seeds/": ["sample_data.sql"]
      },
      "docs/": {
        "api.md": null,
        "user_guide.md": null,
        "deployment.md": null
      }
    },
    "next_steps": [
      "1. 安装依赖：cd backend && pip install -r requirements.txt",
      "2. 运行数据库迁移：python manage.py migrate",
      "3. 启动后端服务：uvicorn main:app --reload",
      "4. 安装前端依赖：cd frontend && npm install",
      "5. 启动前端服务：npm start"
    ],
    "generated_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

### 4. 激活当前场景，使其生效

**POST** `/api/activate`

#### 描述
激活当前场景配置，使生成的系统正式生效并可以使用。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "generation_id": "string",
  "options": {
    "start_services": boolean,
    "run_migrations": boolean,
    "load_sample_data": boolean
  }
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/activate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "generation_id": "gen_1705123456789",
    "options": {
      "start_services": true,
      "run_migrations": true,
      "load_sample_data": false
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "场景激活成功",
  "data": {
    "activation": {
      "generation_id": "gen_1705123456789",
      "scenario_id": "scenario_1705123456789",
      "status": "active",
      "activated_at": "2024-01-20T12:30:00.000Z"
    },
    "services": {
      "backend_api": {
        "status": "running",
        "url": "http://localhost:5000",
        "health_check": "healthy"
      },
      "frontend_app": {
        "status": "running",
        "url": "http://localhost:3000",
        "build_status": "success"
      },
      "database": {
        "status": "connected",
        "migrations_applied": 3,
        "tables_ready": 5
      }
    },
    "endpoints": {
      "api_base": "http://localhost:5000/api",
      "frontend": "http://localhost:3000",
      "api_docs": "http://localhost:5000/docs",
      "admin_panel": "http://localhost:3000/admin"
    },
    "credentials": {
      "admin_user": "admin",
      "admin_password": "generated_password_123",
      "api_key": "ailf_api_key_456"
    }
  }
}
```

---

### 5. 获取代码生成状态

**GET** `/api/generate/status`

#### 描述
获取当前代码生成任务的状态和进度信息。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| generation_id | string | 否 | 特定生成任务ID |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/generate/status?generation_id=gen_1705123456789" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取生成状态成功",
  "data": {
    "current_generation": {
      "id": "gen_1705123456789",
      "status": "completed",
      "progress": 100,
      "started_at": "2024-01-20T12:00:00.000Z",
      "completed_at": "2024-01-20T12:05:30.000Z",
      "duration": 330000,
      "current_step": "activation",
      "total_steps": 8
    },
    "recent_generations": [
      {
        "id": "gen_1705123456789",
        "scenario_name": "电商管理系统",
        "status": "completed",
        "created_at": "2024-01-20T12:00:00.000Z",
        "files_generated": 67,
        "lines_generated": 7368
      },
      {
        "id": "gen_1705123456788",
        "scenario_name": "餐厅管理系统",
        "status": "failed",
        "created_at": "2024-01-20T10:30:00.000Z",
        "error": "Database connection failed"
      }
    ],
    "statistics": {
      "total_generations": 15,
      "successful_generations": 13,
      "failed_generations": 2,
      "avg_generation_time": 245000,
      "total_files_generated": 892,
      "total_lines_generated": 98456
    }
  }
}
```

---

### 6. 获取代码生成日志

**GET** `/api/generate/logs`

#### 描述
获取代码生成过程的详细日志信息，用于调试和监控。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| generation_id | string | 否 | 特定生成任务ID |
| level | string | 否 | 日志级别筛选 (debug/info/warning/error) |
| limit | integer | 否 | 返回日志条数限制，默认100 |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/generate/logs?generation_id=gen_1705123456789&level=info&limit=50" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取生成日志成功",
  "data": {
    "generation_id": "gen_1705123456789",
    "logs": [
      {
        "timestamp": "2024-01-20T12:00:00.000Z",
        "level": "info",
        "component": "database_generator",
        "message": "开始生成数据库表结构",
        "details": {
          "entities": ["product", "order", "user"],
          "total_tables": 5
        }
      },
      {
        "timestamp": "2024-01-20T12:00:15.000Z",
        "level": "info",
        "component": "database_generator",
        "message": "数据库表 'products' 创建成功",
        "details": {
          "table": "products",
          "columns": 8,
          "indexes": 3
        }
      },
      {
        "timestamp": "2024-01-20T12:01:30.000Z",
        "level": "info",
        "component": "api_generator",
        "message": "开始生成API路由代码",
        "details": {
          "apis": 15,
          "framework": "fastapi"
        }
      },
      {
        "timestamp": "2024-01-20T12:02:45.000Z",
        "level": "warning",
        "component": "frontend_generator",
        "message": "表单字段类型不支持，使用默认类型",
        "details": {
          "field": "custom_field",
          "unsupported_type": "custom_type",
          "fallback_type": "text"
        }
      },
      {
        "timestamp": "2024-01-20T12:05:30.000Z",
        "level": "info",
        "component": "activation",
        "message": "系统激活完成",
        "details": {
          "services_started": 3,
          "endpoints_active": 25
        }
      }
    ],
    "summary": {
      "total_logs": 156,
      "by_level": {
        "debug": 45,
        "info": 89,
        "warning": 18,
        "error": 4
      },
      "by_component": {
        "database_generator": 34,
        "api_generator": 45,
        "frontend_generator": 52,
        "activation": 25
      }
    }
  }
}
```

---

## 📝 生成状态说明

| 状态 | 描述 |
|------|------|
| pending | 等待开始 |
| running | 正在生成 |
| completed | 生成完成 |
| failed | 生成失败 |
| cancelled | 已取消 |

---

## 📝 生成组件说明

| 组件 | 描述 | 输出文件 |
|------|------|----------|
| database | 数据库结构 | SQL脚本、迁移文件 |
| backend | 后端API | Python/FastAPI代码 |
| frontend | 前端界面 | React/TypeScript代码 |
| tests | 测试代码 | 单元测试、集成测试 |
| documentation | 文档 | API文档、用户指南 |
| deployment | 部署配置 | Docker、配置文件 |

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| generation_not_found | 404 | 生成任务不存在 | 检查生成任务ID |
| generation_in_progress | 409 | 生成任务正在进行 | 等待当前任务完成 |
| invalid_configuration | 400 | 配置无效 | 检查场景配置完整性 |
| generation_failed | 500 | 生成失败 | 查看详细错误日志 |
| insufficient_resources | 503 | 资源不足 | 检查磁盘空间和内存 |
| activation_failed | 500 | 激活失败 | 检查服务依赖和配置 |
