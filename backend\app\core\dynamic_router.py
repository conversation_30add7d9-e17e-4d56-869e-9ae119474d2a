"""
动态路由管理器
支持运行时动态添加和删除API路由
"""
import json
import time
from typing import Dict, Any, Callable, Optional, List
from fastapi import FastAPI, Request, Response, HTTPException, Depends
from fastapi.routing import APIRoute
from sqlalchemy.orm import Session

from app.core.database import get_db_session
from app.models.api_route import APIRouteDBModel, RouteStatus, HandlerType
from app.core.auth import verify_developer_token


class DynamicRouteHandler:
    """动态路由处理器"""
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.dynamic_routes: Dict[str, APIRoute] = {}
        
    async def handle_entity_crud(self, route_config: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """处理实体CRUD操作"""
        handler_config = route_config.get("handler_config", {})
        entity = handler_config.get("entity", "unknown")
        operation = handler_config.get("operation", "list")
        
        # 模拟CRUD操作
        if operation == "list":
            return {
                "code": 200,
                "message": f"获取{entity}列表成功",
                "data": {
                    "items": [],
                    "total": 0,
                    "page": 1,
                    "limit": 20
                }
            }
        elif operation == "create":
            return {
                "code": 201,
                "message": f"创建{entity}成功",
                "data": {
                    "id": f"{entity}_001",
                    "created_at": "2024-01-20T10:00:00.000Z"
                }
            }
        elif operation == "get":
            return {
                "code": 200,
                "message": f"获取{entity}详情成功",
                "data": {
                    "id": f"{entity}_001",
                    "name": f"示例{entity}"
                }
            }
        elif operation == "update":
            return {
                "code": 200,
                "message": f"更新{entity}成功",
                "data": {
                    "id": f"{entity}_001",
                    "updated_at": "2024-01-20T10:00:00.000Z"
                }
            }
        elif operation == "delete":
            return {
                "code": 200,
                "message": f"删除{entity}成功",
                "data": {}
            }
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {operation}")
    
    async def handle_custom_function(self, route_config: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """处理自定义函数"""
        handler_config = route_config.get("handler_config", {})
        function_name = handler_config.get("function", "default")
        
        return {
            "code": 200,
            "message": f"执行自定义函数 {function_name} 成功",
            "data": {
                "function": function_name,
                "executed_at": "2024-01-20T10:00:00.000Z",
                "result": "success"
            }
        }
    
    async def handle_static_data(self, route_config: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """处理静态数据返回"""
        handler_config = route_config.get("handler_config", {})
        static_data = handler_config.get("data", {})
        
        return {
            "code": 200,
            "message": "返回静态数据成功",
            "data": static_data
        }
    
    async def handle_proxy(self, route_config: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """处理代理转发"""
        handler_config = route_config.get("handler_config", {})
        target_url = handler_config.get("target_url", "")
        
        return {
            "code": 200,
            "message": f"代理转发到 {target_url} 成功",
            "data": {
                "proxy_target": target_url,
                "original_path": str(request.url.path),
                "method": request.method
            }
        }
    
    async def handle_workflow(self, route_config: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """处理工作流触发"""
        handler_config = route_config.get("handler_config", {})
        workflow_id = handler_config.get("workflow_id", "")
        
        return {
            "code": 200,
            "message": f"触发工作流 {workflow_id} 成功",
            "data": {
                "workflow_id": workflow_id,
                "instance_id": f"instance_{int(time.time())}",
                "status": "running"
            }
        }
    
    async def dynamic_route_handler(self, route_id: str, request: Request) -> Response:
        """动态路由处理函数"""
        try:
            # 从数据库获取路由配置
            with get_db_session() as db:
                route_config = db.query(APIRouteDBModel).filter(
                    APIRouteDBModel.id == route_id,
                    APIRouteDBModel.status == RouteStatus.ACTIVE
                ).first()
                
                if not route_config:
                    raise HTTPException(status_code=404, detail="路由不存在或已停用")
                
                # 更新调用统计
                route_config.call_count += 1
                db.commit()
                
                # 根据处理器类型处理请求
                handler_type = route_config.handler_type
                route_data = {
                    "id": route_config.id,
                    "api_id": route_config.api_id,
                    "handler_config": route_config.handler_config
                }
                
                if handler_type == HandlerType.ENTITY_CRUD:
                    result = await self.handle_entity_crud(route_data, request)
                elif handler_type == HandlerType.CUSTOM_FUNCTION:
                    result = await self.handle_custom_function(route_data, request)
                elif handler_type == HandlerType.STATIC_DATA:
                    result = await self.handle_static_data(route_data, request)
                elif handler_type == HandlerType.PROXY:
                    result = await self.handle_proxy(route_data, request)
                elif handler_type == HandlerType.WORKFLOW:
                    result = await self.handle_workflow(route_data, request)
                else:
                    raise HTTPException(status_code=500, detail=f"不支持的处理器类型: {handler_type}")
                
                return Response(
                    content=json.dumps(result, ensure_ascii=False),
                    media_type="application/json"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"处理动态路由失败: {str(e)}")
    
    def add_dynamic_route(self, route_config: Dict[str, Any]) -> bool:
        """动态添加路由到FastAPI应用"""
        try:
            route_id = route_config["id"]
            endpoint = route_config["endpoint"]
            method = route_config["method"].upper()
            
            # 创建路由处理函数
            async def route_handler(request: Request):
                return await self.dynamic_route_handler(route_id, request)
            
            # 添加路由到FastAPI应用
            if method == "GET":
                self.app.get(endpoint)(route_handler)
            elif method == "POST":
                self.app.post(endpoint)(route_handler)
            elif method == "PUT":
                self.app.put(endpoint)(route_handler)
            elif method == "DELETE":
                self.app.delete(endpoint)(route_handler)
            elif method == "PATCH":
                self.app.patch(endpoint)(route_handler)
            else:
                return False
            
            # 记录动态路由
            route_key = f"{method}:{endpoint}"
            self.dynamic_routes[route_key] = {
                "route_id": route_id,
                "endpoint": endpoint,
                "method": method,
                "handler": route_handler
            }
            
            return True
            
        except Exception as e:
            print(f"添加动态路由失败: {e}")
            return False
    
    def remove_dynamic_route(self, endpoint: str, method: str) -> bool:
        """从FastAPI应用中移除动态路由"""
        try:
            route_key = f"{method.upper()}:{endpoint}"
            
            if route_key in self.dynamic_routes:
                # 从路由表中移除
                routes_to_remove = []
                for route in self.app.routes:
                    if hasattr(route, 'path') and hasattr(route, 'methods'):
                        if route.path == endpoint and method.upper() in route.methods:
                            routes_to_remove.append(route)
                
                for route in routes_to_remove:
                    self.app.routes.remove(route)
                
                # 从记录中移除
                del self.dynamic_routes[route_key]
                return True
            
            return False
            
        except Exception as e:
            print(f"移除动态路由失败: {e}")
            return False
    
    def get_dynamic_routes(self) -> List[Dict[str, Any]]:
        """获取所有动态路由"""
        return [
            {
                "route_key": key,
                "route_id": info["route_id"],
                "endpoint": info["endpoint"],
                "method": info["method"]
            }
            for key, info in self.dynamic_routes.items()
        ]


# 全局动态路由管理器实例
dynamic_router: Optional[DynamicRouteHandler] = None


def init_dynamic_router(app: FastAPI):
    """初始化动态路由管理器"""
    global dynamic_router
    dynamic_router = DynamicRouteHandler(app)
    return dynamic_router


def get_dynamic_router() -> DynamicRouteHandler:
    """获取动态路由管理器实例"""
    if dynamic_router is None:
        raise RuntimeError("动态路由管理器未初始化")
    return dynamic_router
