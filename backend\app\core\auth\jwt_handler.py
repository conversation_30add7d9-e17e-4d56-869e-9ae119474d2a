"""
JWT认证处理器
实现JWT token的生成、验证和解析功能
"""
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from fastapi import HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.config import settings


class JWTHandler:
    """JWT处理器类"""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.expire_hours = settings.JWT_EXPIRE_HOURS
    
    def create_access_token(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建访问令牌
        
        Args:
            data: 要编码到token中的数据
            
        Returns:
            包含token信息的字典
        """
        # 创建token数据
        to_encode = data.copy()
        
        # 计算时间
        now = datetime.now(timezone.utc)
        expire = now + timedelta(hours=self.expire_hours)
        
        # 添加标准JWT声明
        to_encode.update({
            "iat": now,  # issued at
            "exp": expire,  # expiration time
            "user_type": "developer"
        })
        
        # 生成token
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        
        return {
            "token": encoded_jwt,
            "expires_in": self.expire_hours * 3600,  # 转换为秒
            "token_type": "Bearer",
            "issued_at": now.isoformat(),
            "expires_at": expire.isoformat()
        }
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """
        验证token
        
        Args:
            token: 要验证的JWT token
            
        Returns:
            验证结果字典
        """
        try:
            # 解码token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 获取时间信息
            issued_at = datetime.fromtimestamp(payload.get("iat"), tz=timezone.utc)
            expires_at = datetime.fromtimestamp(payload.get("exp"), tz=timezone.utc)
            now = datetime.now(timezone.utc)
            
            # 计算剩余时间
            remaining_seconds = int((expires_at - now).total_seconds())
            
            return {
                "valid": True,
                "user_type": payload.get("user_type", "developer"),
                "issued_at": issued_at.isoformat(),
                "expires_at": expires_at.isoformat(),
                "remaining_time": max(0, remaining_seconds),
                "payload": payload
            }
            
        except JWTError as e:
            # 判断错误类型
            error_msg = str(e).lower()
            if "expired" in error_msg:
                error_type = "token_expired"
                details = "令牌已过期"
            elif "invalid" in error_msg or "decode" in error_msg:
                error_type = "token_invalid"
                details = "令牌格式无效"
            else:
                error_type = "token_invalid"
                details = "令牌验证失败"
            
            return {
                "valid": False,
                "error": error_type,
                "details": details
            }
    
    def decode_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        解码token（不验证过期时间）
        
        Args:
            token: 要解码的JWT token
            
        Returns:
            解码后的payload，失败返回None
        """
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                options={"verify_exp": False}  # 不验证过期时间
            )
            return payload
        except JWTError:
            return None


# 全局JWT处理器实例
jwt_handler = JWTHandler()

# HTTP Bearer认证方案
security = HTTPBearer()


def create_access_token(data: Dict[str, Any]) -> str:
    """
    创建访问令牌（兼容接口）

    Args:
        data: 要编码到token中的数据

    Returns:
        JWT token字符串
    """
    result = jwt_handler.create_access_token(data)
    return result["token"]


def verify_token(token: str) -> Dict[str, Any]:
    """
    验证token（兼容接口）

    Args:
        token: 要验证的JWT token

    Returns:
        token payload

    Raises:
        JWTError: 当token无效或过期时
    """
    from jose import JWTError

    result = jwt_handler.verify_token(token)
    if not result["valid"]:
        if result.get("error") == "token_expired":
            raise JWTError("Token has expired")
        else:
            raise JWTError("Invalid token")

    return result["payload"]


def verify_developer_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    验证开发者token的依赖函数

    Args:
        credentials: HTTP Bearer认证凭据

    Returns:
        验证后的token payload

    Raises:
        HTTPException: 当token无效或过期时
    """
    try:
        # 验证token
        result = jwt_handler.verify_token(credentials.credentials)

        if not result["valid"]:
            raise HTTPException(
                status_code=401,
                detail={
                    "code": 401,
                    "message": "认证失败",
                    "data": {
                        "error": result.get("error", "token_invalid"),
                        "details": result.get("details", "令牌无效")
                    }
                }
            )

        # 检查用户类型
        if result.get("user_type") != "developer":
            raise HTTPException(
                status_code=403,
                detail={
                    "code": 403,
                    "message": "权限不足",
                    "data": {
                        "error": "insufficient_permissions",
                        "details": "需要开发者权限"
                    }
                }
            )

        return result["payload"]

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail={
                "code": 401,
                "message": "认证失败",
                "data": {
                    "error": "token_verification_failed",
                    "details": f"令牌验证失败: {str(e)}"
                }
            }
        )
