"""
测试工作流匹配功能
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from app.services.workflow_service import workflow_service

def test_workflow_data():
    """测试工作流数据获取"""
    print("🔍 测试工作流数据获取...")
    
    # 获取工作流配置信息
    config_result = workflow_service.get_amis_configuration_info()
    print(f"配置信息获取结果: {config_result.get('success', False)}")
    
    if config_result.get('success'):
        workflows_data = config_result['data']['workflows']
        print(f"工作流数量: {workflows_data['total']}")
        
        # 查找火车票相关的工作流
        train_workflows = []
        for workflow in workflows_data['items']:
            name = workflow.get('name', '') or ''
            business_scenario = workflow.get('business_scenario', '') or ''
            if '火车票' in name or 'train' in business_scenario:
                train_workflows.append(workflow)
                print(f"\n找到火车票工作流:")
                print(f"  ID: {workflow.get('id')}")
                print(f"  名称: {workflow.get('name')}")
                print(f"  业务场景: {workflow.get('business_scenario')}")
                print(f"  用户意图: {workflow.get('user_intents')}")
                print(f"  触发关键词: {workflow.get('trigger_keywords')}")
        
        return train_workflows
    else:
        print(f"❌ 获取配置信息失败: {config_result}")
        return []

def test_workflow_matching():
    """测试工作流匹配逻辑"""
    print("\n🔍 测试工作流匹配逻辑...")
    
    # 获取工作流数据
    workflows_result = workflow_service.get_workflows_for_amis()
    if not workflows_result.get('success'):
        print(f"❌ 获取工作流失败: {workflows_result}")
        return
    
    workflows = workflows_result['data']['workflows']
    print(f"可用工作流数量: {len(workflows)}")
    
    # 测试命令
    test_commands = [
        "我要买火车票",
        "帮我订火车票", 
        "查询火车票",
        "预订车票"
    ]
    
    # 导入匹配函数
    from app.services.ai_amis_service import AIAmisService
    ai_amis_service = AIAmisService()
    
    for command in test_commands:
        print(f"\n🎯 测试命令: '{command}'")
        
        # 模拟意图数据
        intent = {
            "intent_type": "workflow_execute",
            "entity": "火车票",
            "action": "购买",
            "confidence": 0.95
        }
        
        # 测试匹配
        matched = ai_amis_service._match_workflow_by_intent(command, intent, workflows)
        
        if matched:
            print(f"✅ 匹配成功: {matched.get('label', matched.get('name', '未知'))}")
        else:
            print(f"❌ 匹配失败")

def main():
    print("=" * 60)
    print("🧪 工作流匹配测试工具")
    print("=" * 60)
    
    # 1. 测试工作流数据
    train_workflows = test_workflow_data()
    
    # 2. 测试工作流匹配
    test_workflow_matching()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
