# 权限控制模块 API 文档

## 📋 概述

权限控制模块提供基于角色的访问控制（RBAC）功能，包括权限检查、权限配置和权限矩阵管理。

## 🔒 API 端点列表

### 权限检查
1. 检查用户是否有访问特定API的权限
2. 获取指定用户可以访问的API列表

### 权限配置管理
3. 更新角色对特定API的访问权限
4. 批量更新多个角色的API权限
5. 获取完整的权限矩阵
6. 更新权限矩阵

---

## API 详细文档

### 1. 检查用户是否有访问特定API的权限

**POST** `/api/permissions/check`

#### 描述
检查指定用户是否有权限访问特定的API端点和操作。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "user_id": "string",
  "resource": "string",
  "action": "string",
  "context": {}
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | string | 是 | 用户唯一标识符 |
| resource | string | 是 | 资源名称（如：products, orders） |
| action | string | 是 | 操作类型（如：read, create, update, delete） |
| context | object | 否 | 上下文信息（如：记录ID、部门等） |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/permissions/check" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "user_id": "user_001",
    "resource": "products",
    "action": "update",
    "context": {
      "product_id": "product_123",
      "department": "sales"
    }
  }'
```

#### 响应格式

**有权限响应 (200 OK):**
```json
{
  "code": 200,
  "message": "权限检查完成",
  "data": {
    "allowed": true,
    "user": {
      "id": "user_001",
      "name": "张三",
      "roles": ["sales_manager"]
    },
    "permission": {
      "resource": "products",
      "action": "update",
      "granted_by": "role:sales_manager",
      "permission_name": "products:update"
    },
    "context": {
      "product_id": "product_123",
      "department": "sales"
    },
    "checked_at": "2024-01-20T11:00:00.000Z"
  }
}
```

**无权限响应 (403 Forbidden):**
```json
{
  "code": 403,
  "message": "权限不足",
  "data": {
    "allowed": false,
    "user": {
      "id": "user_002",
      "name": "李四",
      "roles": ["customer_service"]
    },
    "permission": {
      "resource": "products",
      "action": "update",
      "required_permission": "products:update"
    },
    "reason": "用户角色 'customer_service' 没有 'products:update' 权限",
    "suggestions": [
      "联系管理员申请权限",
      "使用具有相应权限的账户"
    ],
    "checked_at": "2024-01-20T11:00:00.000Z"
  }
}
```

---

### 2. 获取指定用户可以访问的API列表

**GET** `/api/permissions/user-apis`

#### 描述
获取指定用户基于其角色可以访问的所有API端点列表。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | string | 是 | 用户唯一标识符 |
| resource | string | 否 | 筛选特定资源的API |
| method | string | 否 | 筛选特定HTTP方法的API |
| include_details | boolean | 否 | 是否包含API详细信息，默认false |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/permissions/user-apis?user_id=user_001&include_details=true" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取用户API权限成功",
  "data": {
    "user": {
      "id": "user_001",
      "name": "张三",
      "roles": [
        {
          "id": "role_1705123456789",
          "name": "销售经理",
          "code": "sales_manager",
          "level": 6
        }
      ]
    },
    "accessible_apis": [
      {
        "api_id": "product_list_api",
        "name": "商品列表API",
        "endpoint": "/api/products",
        "method": "GET",
        "resource": "products",
        "action": "read",
        "permission": "products:read",
        "granted_by": "role:sales_manager"
      },
      {
        "api_id": "product_create_api",
        "name": "创建商品API",
        "endpoint": "/api/products",
        "method": "POST",
        "resource": "products",
        "action": "create",
        "permission": "products:create",
        "granted_by": "role:sales_manager"
      },
      {
        "api_id": "order_management_api",
        "name": "订单管理API",
        "endpoint": "/api/orders/*",
        "method": "*",
        "resource": "orders",
        "action": "*",
        "permission": "orders:*",
        "granted_by": "role:sales_manager"
      }
    ],
    "summary": {
      "total_apis": 15,
      "by_resource": {
        "products": 4,
        "orders": 6,
        "customers": 3,
        "reports": 2
      },
      "by_method": {
        "GET": 8,
        "POST": 4,
        "PUT": 2,
        "DELETE": 1
      },
      "permissions": [
        "products:read",
        "products:create",
        "products:update",
        "orders:*",
        "customers:*",
        "reports:sales"
      ]
    }
  }
}
```

---

### 3. 更新角色对特定API的访问权限

**POST** `/api/permissions/role-api`

#### 描述
更新指定角色对特定API的访问权限，支持授予或撤销权限。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "role_id": "string",
  "api_id": "string",
  "permission": "string",
  "action": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |
| api_id | string | 是 | API唯一标识符 |
| permission | string | 是 | 权限名称（如：products:read） |
| action | string | 是 | 操作类型（grant/revoke） |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/permissions/role-api" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "role_id": "role_1705123456790",
    "api_id": "product_delete_api",
    "permission": "products:delete",
    "action": "grant"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "权限更新成功",
  "data": {
    "role": {
      "id": "role_1705123456790",
      "name": "客服专员",
      "code": "customer_service"
    },
    "api": {
      "id": "product_delete_api",
      "name": "删除商品API",
      "endpoint": "/api/products/{id}",
      "method": "DELETE"
    },
    "permission": {
      "name": "products:delete",
      "action": "grant",
      "granted_at": "2024-01-20T11:30:00.000Z",
      "granted_by": "admin_user_001"
    }
  }
}
```

---

### 4. 批量更新多个角色的API权限

**POST** `/api/permissions/batch-update`

#### 描述
批量更新多个角色的API权限，支持复杂的权限配置操作。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "updates": [
    {
      "role_id": "string",
      "api_id": "string",
      "permission": "string",
      "action": "string"
    }
  ],
  "dry_run": boolean
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| updates | array | 是 | 权限更新列表 |
| dry_run | boolean | 否 | 是否为试运行模式，默认false |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/permissions/batch-update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "updates": [
      {
        "role_id": "role_1705123456790",
        "api_id": "product_update_api",
        "permission": "products:update",
        "action": "grant"
      },
      {
        "role_id": "role_1705123456790",
        "api_id": "product_delete_api",
        "permission": "products:delete",
        "action": "revoke"
      },
      {
        "role_id": "role_1705123456791",
        "api_id": "analytics_api",
        "permission": "analytics:read",
        "action": "grant"
      }
    ],
    "dry_run": false
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "批量权限更新成功",
  "data": {
    "results": [
      {
        "role_id": "role_1705123456790",
        "api_id": "product_update_api",
        "permission": "products:update",
        "action": "grant",
        "status": "success",
        "message": "权限授予成功"
      },
      {
        "role_id": "role_1705123456790",
        "api_id": "product_delete_api",
        "permission": "products:delete",
        "action": "revoke",
        "status": "success",
        "message": "权限撤销成功"
      },
      {
        "role_id": "role_1705123456791",
        "api_id": "analytics_api",
        "permission": "analytics:read",
        "action": "grant",
        "status": "failed",
        "message": "角色级别不足，无法授予此权限",
        "error": "insufficient_role_level"
      }
    ],
    "summary": {
      "total": 3,
      "successful": 2,
      "failed": 1,
      "granted": 2,
      "revoked": 1
    }
  }
}
```

---

### 5. 获取完整的权限矩阵

**GET** `/api/permissions/matrix`

#### 描述
获取系统的完整权限矩阵，显示所有角色对所有API的权限配置。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| format | string | 否 | 返回格式（matrix/list），默认matrix |
| include_inactive | boolean | 否 | 是否包含非活跃角色，默认false |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/permissions/matrix?format=matrix" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取权限矩阵成功",
  "data": {
    "matrix": {
      "roles": [
        {
          "id": "role_1705123456789",
          "name": "销售经理",
          "code": "sales_manager",
          "level": 6
        },
        {
          "id": "role_1705123456790",
          "name": "客服专员",
          "code": "customer_service",
          "level": 3
        }
      ],
      "apis": [
        {
          "id": "product_list_api",
          "name": "商品列表API",
          "endpoint": "/api/products",
          "method": "GET",
          "permission": "products:read"
        },
        {
          "id": "product_create_api",
          "name": "创建商品API",
          "endpoint": "/api/products",
          "method": "POST",
          "permission": "products:create"
        }
      ],
      "permissions": {
        "role_1705123456789": {
          "product_list_api": true,
          "product_create_api": true,
          "product_update_api": true,
          "product_delete_api": false
        },
        "role_1705123456790": {
          "product_list_api": true,
          "product_create_api": false,
          "product_update_api": false,
          "product_delete_api": false
        }
      }
    },
    "statistics": {
      "total_roles": 2,
      "total_apis": 25,
      "total_permissions": 50,
      "granted_permissions": 32,
      "denied_permissions": 18,
      "coverage": 64.0
    }
  }
}
```

---

### 6. 更新权限矩阵

**POST** `/api/permissions/matrix`

#### 描述
批量更新权限矩阵，支持一次性配置多个角色和API的权限关系。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "matrix": {
    "role_id": {
      "api_id": boolean
    }
  },
  "merge_mode": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| matrix | object | 是 | 权限矩阵对象 |
| merge_mode | string | 否 | 合并模式（replace/merge），默认merge |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/permissions/matrix" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "matrix": {
      "role_1705123456790": {
        "product_list_api": true,
        "product_create_api": false,
        "product_update_api": true,
        "product_delete_api": false,
        "order_list_api": true,
        "order_create_api": false
      },
      "role_1705123456791": {
        "analytics_api": true,
        "reports_api": true,
        "settings_api": false
      }
    },
    "merge_mode": "merge"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "权限矩阵更新成功",
  "data": {
    "updated_permissions": {
      "role_1705123456790": {
        "granted": ["product_list_api", "product_update_api", "order_list_api"],
        "revoked": ["product_create_api", "product_delete_api", "order_create_api"]
      },
      "role_1705123456791": {
        "granted": ["analytics_api", "reports_api"],
        "revoked": ["settings_api"]
      }
    },
    "summary": {
      "roles_updated": 2,
      "permissions_granted": 5,
      "permissions_revoked": 4,
      "total_changes": 9
    },
    "updated_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

## 📝 权限检查流程

### 权限验证步骤
1. **用户身份验证** - 验证用户令牌有效性
2. **角色获取** - 获取用户关联的所有角色
3. **权限查询** - 查询角色拥有的权限列表
4. **权限匹配** - 匹配请求的资源和操作
5. **上下文验证** - 验证上下文条件（如部门、记录所有者等）
6. **结果返回** - 返回权限检查结果

### 权限继承规则
- 用户可以拥有多个角色
- 权限采用"或"逻辑，任一角色有权限即可访问
- 高级别角色自动继承低级别角色的基础权限
- 特殊权限需要显式授予

---

## 📝 权限表达式

### 基本格式
```
resource:action
```

### 通配符支持
- `*:*` - 所有资源的所有操作
- `products:*` - 商品资源的所有操作
- `*:read` - 所有资源的读取操作

### 条件权限
```json
{
  "permission": "orders:update",
  "conditions": {
    "owner": "self",
    "status": ["pending", "processing"]
  }
}
```

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| user_not_found | 404 | 用户不存在 | 检查用户ID是否正确 |
| role_not_found | 404 | 角色不存在 | 检查角色ID是否正确 |
| api_not_found | 404 | API不存在 | 检查API ID是否正确 |
| permission_denied | 403 | 权限不足 | 用户没有执行此操作的权限 |
| invalid_permission | 400 | 无效的权限格式 | 检查权限名称格式 |
| insufficient_role_level | 403 | 角色级别不足 | 只能管理级别不高于自己的角色 |
