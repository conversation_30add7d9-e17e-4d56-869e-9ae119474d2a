/**
 * 工作流节点组件 - Apple风格设计
 */

import { Node } from 'butterfly-dag';

class WorkflowNode extends Node {
  public options: any;

  constructor(opts: any) {
    super(opts);
    this.options = opts;
  }

  draw = (opts: any) => {
    const container = document.createElement('div');
    const nodeType = opts.nodeType || this.options.nodeType || 'default';

    // 获取节点类型配置
    const nodeConfig = this.getNodeConfig(nodeType);

    container.className = `workflow-node workflow-node-${nodeType}`;
    container.style.cssText = `
      width: 160px;
      min-height: 80px;
      background: ${nodeConfig.background};
      border: 2px solid ${nodeConfig.borderColor};
      border-radius: 12px;
      padding: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
    `;

    // 节点图标
    const iconElement = document.createElement('div');
    iconElement.className = 'node-icon';
    iconElement.style.cssText = `
      width: 24px;
      height: 24px;
      background: ${nodeConfig.iconBackground};
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: white;
      margin-bottom: 8px;
    `;
    iconElement.textContent = nodeConfig.icon;

    // 节点标题
    const titleElement = document.createElement('div');
    titleElement.className = 'node-title';
    titleElement.style.cssText = `
      font-size: 14px;
      font-weight: 600;
      color: #1d1d1f;
      margin-bottom: 4px;
      line-height: 1.2;
    `;
    titleElement.textContent = opts.label || this.options.label || '未命名节点';

    // 节点描述
    const descElement = document.createElement('div');
    descElement.className = 'node-description';
    descElement.style.cssText = `
      font-size: 12px;
      color: #86868b;
      line-height: 1.3;
      max-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
    `;
    descElement.textContent = nodeConfig.defaultDescription;

    // 状态指示器
    const statusElement = document.createElement('div');
    statusElement.className = 'node-status';
    statusElement.style.cssText = `
      position: absolute;
      top: 8px;
      right: 8px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: ${this.getStatusColor('inactive')};
    `;

    container.appendChild(iconElement);
    container.appendChild(titleElement);
    container.appendChild(descElement);
    container.appendChild(statusElement);

    // 添加悬停效果
    container.addEventListener('mouseenter', () => {
      container.style.transform = 'translateY(-2px)';
      container.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
    });

    container.addEventListener('mouseleave', () => {
      container.style.transform = 'translateY(0)';
      container.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
    });

    return container;
  };

  // 获取节点类型配置
  getNodeConfig(nodeType: string) {
    const configs: Record<string, any> = {
      start: {
        icon: '▶',
        background: 'linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%)',
        borderColor: '#52c41a',
        iconBackground: '#52c41a',
        defaultDescription: '工作流开始节点'
      },
      api_call: {
        icon: '🔗',
        background: 'linear-gradient(135deg, #e6f4ff 0%, #f0f8ff 100%)',
        borderColor: '#1890ff',
        iconBackground: '#1890ff',
        defaultDescription: 'API接口调用'
      },
      user_input: {
        icon: '📝',
        background: 'linear-gradient(135deg, #f0e6ff 0%, #f8f0ff 100%)',
        borderColor: '#722ed1',
        iconBackground: '#722ed1',
        defaultDescription: '用户输入表单'
      },
      condition: {
        icon: '❓',
        background: 'linear-gradient(135deg, #fff2e6 0%, #fff8f0 100%)',
        borderColor: '#fa8c16',
        iconBackground: '#fa8c16',
        defaultDescription: '条件判断分支'
      },
      notification: {
        icon: '📢',
        background: 'linear-gradient(135deg, #ffe6f0 0%, #fff0f6 100%)',
        borderColor: '#eb2f96',
        iconBackground: '#eb2f96',
        defaultDescription: '消息通知'
      },
      end: {
        icon: '⏹',
        background: 'linear-gradient(135deg, #ffe6e6 0%, #fff0f0 100%)',
        borderColor: '#f5222d',
        iconBackground: '#f5222d',
        defaultDescription: '工作流结束'
      },
      default: {
        icon: '⚪',
        background: 'linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%)',
        borderColor: '#d9d9d9',
        iconBackground: '#8c8c8c',
        defaultDescription: '默认节点'
      }
    };

    return configs[nodeType] || configs.default;
  }

  // 获取状态颜色
  getStatusColor(status?: string) {
    const colors: Record<string, string> = {
      active: '#52c41a',
      inactive: '#d9d9d9',
      error: '#f5222d',
      warning: '#fa8c16'
    };
    return colors[status || 'inactive'];
  }

  // 获取锚点
  getEndpoint() {
    return [
      {
        id: 'left',
        orientation: [-1, 0],
        pos: [0, 0.5],
        Class: 'endpoint'
      },
      {
        id: 'right',
        orientation: [1, 0],
        pos: [0, 0.5],
        Class: 'endpoint'
      }
    ];
  }
}

export default WorkflowNode;
