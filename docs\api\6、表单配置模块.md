# 表单配置模块 API 文档

## 📋 概述

表单配置模块提供动态表单的定义、渲染和数据处理功能，支持基于实体自动生成表单界面。

## 📝 API 端点列表

### 表单定义管理
1. 创建动态表单配置
2. 获取所有表单配置
3. 获取特定表单配置
4. 更新表单配置
5. 删除表单配置

### 表单渲染管理
6. 获取表单渲染配置（amis schema）
7. 提交表单数据
8. 获取表单数据用于编辑
9. 验证表单配置

### 表单字段管理
10. 获取表单字段列表
11. 添加表单字段
12. 更新表单字段
13. 删除表单字段

---

## API 详细文档

### 1. 创建动态表单配置

**POST** `/api/forms`

#### 描述
创建新的动态表单配置，包括字段布局、验证规则和权限设置。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "entity": "string",
  "description": "string",
  "layout": {
    "type": "string",
    "columns": number,
    "spacing": number
  },
  "sections": [
    {
      "id": "string",
      "title": "string",
      "collapsible": boolean,
      "fields": [
        {
          "id": "string",
          "entityField": "string",
          "displayType": "string",
          "label": "string",
          "placeholder": "string",
          "required": boolean,
          "readonly": boolean,
          "hidden": boolean,
          "validation": {
            "rules": ["string"],
            "messages": {}
          }
        }
      ]
    }
  ],
  "permissions": [
    {
      "role": "string",
      "actions": ["string"]
    }
  ]
}
```

#### 显示类型说明

| 类型 | 描述 | 适用字段类型 |
|------|------|-------------|
| input | 普通输入框 | text, number, email, phone |
| textarea | 多行文本 | text |
| select | 下拉选择 | select |
| radio | 单选按钮 | select |
| checkbox | 复选框 | multiselect, boolean |
| switch | 开关 | boolean |
| date-picker | 日期选择 | date, datetime |
| upload | 文件上传 | image, file |
| rich-text | 富文本编辑器 | text |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/forms" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "商品录入表单",
    "entity": "product",
    "description": "用于录入和编辑商品信息的表单",
    "layout": {
      "type": "grid",
      "columns": 2,
      "spacing": 16
    },
    "sections": [
      {
        "id": "section_basic",
        "title": "基本信息",
        "collapsible": false,
        "fields": [
          {
            "id": "field_name",
            "entityField": "name",
            "displayType": "input",
            "label": "商品名称",
            "placeholder": "请输入商品名称",
            "required": true,
            "validation": {
              "rules": ["required", "max:100"],
              "messages": {
                "required": "商品名称不能为空",
                "max": "商品名称不能超过100个字符"
              }
            }
          },
          {
            "id": "field_category",
            "entityField": "category",
            "displayType": "select",
            "label": "商品分类",
            "required": true,
            "validation": {
              "rules": ["required"],
              "messages": {
                "required": "请选择商品分类"
              }
            }
          }
        ]
      },
      {
        "id": "section_pricing",
        "title": "价格信息",
        "collapsible": true,
        "fields": [
          {
            "id": "field_price",
            "entityField": "price",
            "displayType": "input",
            "label": "商品价格",
            "placeholder": "0.00",
            "required": true,
            "validation": {
              "rules": ["required", "numeric", "min:0.01"],
              "messages": {
                "required": "价格不能为空",
                "numeric": "价格必须是数字",
                "min": "价格必须大于0"
              }
            }
          }
        ]
      }
    ],
    "permissions": [
      {
        "role": "admin",
        "actions": ["create", "read", "update", "delete"]
      },
      {
        "role": "staff",
        "actions": ["create", "read", "update"]
      }
    ]
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "表单配置创建成功",
  "data": {
    "form": {
      "id": "form_1705123456789",
      "name": "商品录入表单",
      "entity": "product",
      "description": "用于录入和编辑商品信息的表单",
      "layout": {
        "type": "grid",
        "columns": 2,
        "spacing": 16
      },
      "sections": [
        {
          "id": "section_basic",
          "title": "基本信息",
          "collapsible": false,
          "field_count": 2
        }
      ],
      "field_count": 3,
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

---

### 2. 获取所有表单配置

**GET** `/api/forms`

#### 描述
获取系统中所有已定义的表单配置列表。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| entity | string | 否 | 筛选特定实体的表单 |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/forms?entity=product" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取表单配置列表成功",
  "data": {
    "forms": [
      {
        "id": "form_1705123456789",
        "name": "商品录入表单",
        "entity": "product",
        "description": "用于录入和编辑商品信息的表单",
        "field_count": 3,
        "section_count": 2,
        "created_at": "2024-01-20T10:00:00.000Z",
        "updated_at": "2024-01-20T10:00:00.000Z"
      }
    ],
    "total": 1
  }
}
```

---

### 3. 获取特定表单配置

**GET** `/api/forms/{form_id}`

#### 描述
获取指定表单的详细配置信息。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/forms/form_1705123456789" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取表单配置成功",
  "data": {
    "form": {
      "id": "form_1705123456789",
      "name": "商品录入表单",
      "entity": "product",
      "description": "用于录入和编辑商品信息的表单",
      "layout": {
        "type": "grid",
        "columns": 2,
        "spacing": 16
      },
      "sections": [
        {
          "id": "section_basic",
          "title": "基本信息",
          "collapsible": false,
          "fields": [
            {
              "id": "field_name",
              "entityField": "name",
              "displayType": "input",
              "label": "商品名称",
              "required": true
            }
          ]
        }
      ],
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

---

### 4. 更新表单配置

**PUT** `/api/forms/{form_id}`

#### 描述
更新指定表单的配置信息。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "description": "string",
  "layout": {},
  "sections": []
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/forms/form_1705123456789" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "商品录入表单（增强版）",
    "description": "增强版商品录入表单，支持更多字段"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "表单配置更新成功",
  "data": {
    "form": {
      "id": "form_1705123456789",
      "name": "商品录入表单（增强版）",
      "description": "增强版商品录入表单，支持更多字段",
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 5. 删除表单配置

**DELETE** `/api/forms/{form_id}`

#### 描述
删除指定的表单配置。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/forms/form_1705123456789" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "表单配置删除成功",
  "data": {
    "form_id": "form_1705123456789",
    "deleted_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

### 6. 获取表单渲染配置（amis schema）

**GET** `/api/forms/{form_id}/render`

#### 描述
获取表单的amis渲染配置，用于前端动态渲染表单界面。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| mode | string | 否 | 渲染模式 (create/edit/view) |
| record_id | string | 否 | 编辑模式下的记录ID |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/forms/form_1705123456789/render?mode=create" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取表单渲染配置成功",
  "data": {
    "schema": {
      "type": "form",
      "title": "商品录入表单",
      "mode": "horizontal",
      "api": {
        "method": "POST",
        "url": "/api/entities/product/data"
      },
      "body": [
        {
          "type": "fieldset",
          "title": "基本信息",
          "collapsible": false,
          "body": [
            {
              "type": "grid",
              "columns": [
                {
                  "body": [
                    {
                      "type": "input-text",
                      "name": "name",
                      "label": "商品名称",
                      "placeholder": "请输入商品名称",
                      "required": true,
                      "validations": {
                        "isLength": {
                          "args": [1, 100],
                          "message": "商品名称长度必须在1-100个字符之间"
                        }
                      }
                    }
                  ]
                },
                {
                  "body": [
                    {
                      "type": "select",
                      "name": "category",
                      "label": "商品分类",
                      "required": true,
                      "options": [
                        {"label": "电子产品", "value": "electronics"},
                        {"label": "服装", "value": "clothing"},
                        {"label": "食品", "value": "food"}
                      ],
                      "validations": {
                        "isRequired": {
                          "message": "请选择商品分类"
                        }
                      }
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "type": "fieldset",
          "title": "价格信息",
          "collapsible": true,
          "body": [
            {
              "type": "input-number",
              "name": "price",
              "label": "商品价格",
              "placeholder": "0.00",
              "required": true,
              "precision": 2,
              "min": 0.01,
              "validations": {
                "isNumeric": {
                  "message": "价格必须是数字"
                },
                "minimum": {
                  "args": [0.01],
                  "message": "价格必须大于0"
                }
              }
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "submit",
          "label": "保存",
          "primary": true
        },
        {
          "type": "reset",
          "label": "重置"
        }
      ]
    },
    "metadata": {
      "form_id": "form_1705123456789",
      "entity": "product",
      "mode": "create",
      "version": "1.0.0"
    }
  }
}
```

---

### 3. 提交表单数据

**POST** `/api/forms/{form_id}/submit`

#### 描述
提交表单数据，进行验证并保存到对应的实体中。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "data": {},
  "mode": "string",
  "record_id": "string"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/forms/form_1705123456789/submit" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "data": {
      "name": "iPhone 15 Pro",
      "category": "electronics",
      "price": 7999.00,
      "stock": 100
    },
    "mode": "create"
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "表单数据提交成功",
  "data": {
    "record": {
      "id": "record_1705123456790",
      "name": "iPhone 15 Pro",
      "category": "electronics",
      "price": 7999.00,
      "stock": 100,
      "created_at": "2024-01-20T11:00:00.000Z"
    },
    "form_id": "form_1705123456789",
    "mode": "create"
  }
}
```

**验证错误响应 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "表单数据验证失败",
  "data": {
    "error": "validation_error",
    "details": {
      "name": ["商品名称不能为空"],
      "price": ["价格必须大于0"],
      "category": ["请选择商品分类"]
    }
  }
}
```

---

### 4. 获取表单数据用于编辑

**GET** `/api/forms/{form_id}/data/{record_id}`

#### 描述
获取指定记录的数据，用于表单编辑模式的数据回填。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |
| record_id | string | 是 | 记录唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/forms/form_1705123456789/data/record_1705123456790" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取表单数据成功",
  "data": {
    "record": {
      "id": "record_1705123456790",
      "name": "iPhone 15 Pro",
      "category": "electronics",
      "price": 7999.00,
      "stock": 100,
      "status": "active",
      "created_at": "2024-01-20T11:00:00.000Z",
      "updated_at": "2024-01-20T11:00:00.000Z"
    },
    "form_metadata": {
      "form_id": "form_1705123456789",
      "entity": "product",
      "editable_fields": ["name", "category", "price", "stock"],
      "readonly_fields": ["id", "created_at", "updated_at"]
    }
  }
}
```

---

### 5. 验证表单配置

**POST** `/api/forms/validate`

#### 描述
验证表单配置的正确性，包括字段映射、验证规则等。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "form_config": {},
  "entity_id": "string"
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/forms/validate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "form_config": {
      "name": "测试表单",
      "entity": "product",
      "sections": [
        {
          "title": "基本信息",
          "fields": [
            {
              "entityField": "name",
              "displayType": "input",
              "required": true
            }
          ]
        }
      ]
    },
    "entity_id": "entity_1705123456789"
  }'
```

#### 响应格式

**验证通过 (200 OK):**
```json
{
  "code": 200,
  "message": "表单配置验证通过",
  "data": {
    "valid": true,
    "warnings": [
      "字段 'description' 在实体中存在但未在表单中配置"
    ],
    "suggestions": [
      "建议为必填字段添加验证规则",
      "建议为数字字段设置合理的取值范围"
    ]
  }
}
```

**验证失败 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "表单配置验证失败",
  "data": {
    "valid": false,
    "errors": [
      "字段 'invalid_field' 在实体中不存在",
      "显示类型 'invalid_type' 不支持",
      "必填字段 'name' 缺少验证规则"
    ],
    "warnings": [],
    "suggestions": []
  }
}
```

---

### 10. 获取表单字段列表

**GET** `/api/forms/{form_id}/fields`

#### 描述
获取指定表单的所有字段列表。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/forms/form_1705123456789/fields" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取表单字段成功",
  "data": {
    "fields": [
      {
        "id": "field_name",
        "entityField": "name",
        "displayType": "input",
        "label": "商品名称",
        "required": true,
        "section": "section_basic"
      }
    ],
    "total": 3
  }
}
```

---

### 11. 添加表单字段

**POST** `/api/forms/{form_id}/fields`

#### 描述
为指定表单添加新的字段。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| form_id | string | 是 | 表单唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "entityField": "string",
  "displayType": "string",
  "label": "string",
  "section": "string",
  "required": boolean,
  "validation": {}
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/forms/form_1705123456789/fields" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "entityField": "brand",
    "displayType": "select",
    "label": "品牌",
    "section": "section_basic",
    "required": false,
    "validation": {
      "rules": ["string"],
      "messages": {}
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "表单字段添加成功",
  "data": {
    "field": {
      "id": "field_brand",
      "entityField": "brand",
      "displayType": "select",
      "label": "品牌",
      "section": "section_basic",
      "required": false,
      "created_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 12. 更新表单字段

**PUT** `/api/forms/fields/{field_id}`

#### 描述
更新指定的表单字段配置。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| field_id | string | 是 | 字段唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "label": "string",
  "displayType": "string",
  "required": boolean,
  "validation": {}
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/forms/fields/field_brand" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "label": "商品品牌",
    "displayType": "select",
    "required": true
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "表单字段更新成功",
  "data": {
    "field": {
      "id": "field_brand",
      "label": "商品品牌",
      "displayType": "select",
      "required": true,
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 13. 删除表单字段

**DELETE** `/api/forms/fields/{field_id}`

#### 描述
删除指定的表单字段。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| field_id | string | 是 | 字段唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/forms/fields/field_brand" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "表单字段删除成功",
  "data": {
    "field_id": "field_brand",
    "deleted_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

## 📝 表单布局类型

| 类型 | 描述 | 适用场景 |
|------|------|----------|
| vertical | 垂直布局 | 字段较少的简单表单 |
| horizontal | 水平布局 | 标签和输入框水平排列 |
| grid | 网格布局 | 多列显示，充分利用空间 |
| inline | 内联布局 | 字段在同一行显示 |
| tabs | 标签页布局 | 字段较多需要分组显示 |

---

## 📝 验证规则说明

### 通用验证规则
- `required` - 必填验证
- `min:value` - 最小值验证
- `max:value` - 最大值验证
- `minLength:value` - 最小长度验证
- `maxLength:value` - 最大长度验证

### 特定类型验证规则
- `email` - 邮箱格式验证
- `phone` - 电话号码格式验证
- `numeric` - 数字格式验证
- `url` - URL格式验证
- `date` - 日期格式验证

---

## 📝 权限操作说明

| 操作 | 描述 |
|------|------|
| create | 创建新记录 |
| read | 查看记录 |
| update | 更新记录 |
| delete | 删除记录 |
| export | 导出数据 |
| import | 导入数据 |

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| form_not_found | 404 | 表单不存在 | 检查表单ID是否正确 |
| entity_not_found | 404 | 关联实体不存在 | 检查实体配置 |
| field_mapping_error | 400 | 字段映射错误 | 检查字段名称是否正确 |
| validation_failed | 400 | 数据验证失败 | 检查提交的数据格式 |
| permission_denied | 403 | 权限不足 | 检查用户角色权限 |
| record_not_found | 404 | 记录不存在 | 检查记录ID是否正确 |
