/* AILF - 动画效果 */

/* 苹果风格的微妙动画 */
@keyframes subtleGlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* 增强的背景光晕动画 */
@keyframes enhancedSubtleGlow {
  0%, 100% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
    filter: blur(0px) brightness(1);
  }
  16.67% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.02) rotate(0.5deg);
    filter: blur(0.5px) brightness(1.02);
  }
  33.33% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.04) rotate(1deg);
    filter: blur(1px) brightness(1.04);
  }
  50% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1.06) rotate(0.5deg);
    filter: blur(1.5px) brightness(1.06);
  }
  66.67% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.04) rotate(-0.5deg);
    filter: blur(1px) brightness(1.04);
  }
  83.33% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.02) rotate(-1deg);
    filter: blur(0.5px) brightness(1.02);
  }
}

/* 文本呼吸动画 */
@keyframes enhancedTextBreathe {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.01);
    filter: brightness(1.1);
  }
}

/* 文本光晕动画 */
@keyframes subtleTextGlow {
  0%, 100% {
    text-shadow: 
      0 0 2px #ffffff,
      0 0 4px #ffffff,
      1px 1px 0px #ffffff;
  }
  50% {
    text-shadow: 
      0 0 4px #ffffff,
      0 0 8px #ffffff,
      0 0 12px rgba(255, 255, 255, 0.5),
      1px 1px 0px #ffffff;
  }
}

/* 波纹动画 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滑入动画 */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* 摇摆动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

/* 闪烁动画 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 心跳动画 */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* 呼吸动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 光束扫描动画 */
@keyframes lightSweep {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 粒子漂浮动画 */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 1;
  }
}

/* 波浪动画 */
@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  75% {
    transform: rotate(-5deg);
  }
}

/* 渐变移动动画 */
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 光晕扩散动画 */
@keyframes glowExpand {
  0% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
}

/* 彩虹渐变动画 */
@keyframes rainbow {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

/* 模糊聚焦动画 */
@keyframes focusBlur {
  0%, 100% {
    filter: blur(0px);
  }
  50% {
    filter: blur(2px);
  }
}

/* 透明度渐变动画 */
@keyframes opacityPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.9;
  }
}
