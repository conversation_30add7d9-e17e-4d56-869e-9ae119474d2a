# 角色管理模块 API 文档

## 📋 概述

角色管理模块提供用户角色的定义、管理和权限分配功能，支持基于角色的访问控制（RBAC）。

## 👥 API 端点列表

### 角色定义管理
1. 创建新的用户角色
2. 获取所有角色列表
3. 获取特定角色详情
4. 更新角色信息
5. 删除角色

### 角色权限管理
6. 获取角色权限列表
7. 为角色分配权限
8. 移除角色权限

---

## API 详细文档

### 1. 创建新的用户角色

**POST** `/api/roles`

#### 描述
创建新的用户角色，包括角色基本信息和权限级别设置。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "code": "string",
  "level": number,
  "description": "string",
  "status": "string",
  "permissions": ["string"],
  "metadata": {}
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 角色名称 |
| code | string | 是 | 角色代码（唯一标识） |
| level | number | 是 | 角色级别（1-10，数字越大权限越高） |
| description | string | 否 | 角色描述 |
| status | string | 否 | 角色状态（active/inactive），默认active |
| permissions | array | 否 | 初始权限列表 |
| metadata | object | 否 | 角色元数据 |

#### 角色级别说明

| 级别 | 描述 | 典型角色 |
|------|------|----------|
| 1-2 | 基础用户 | 访客、普通用户 |
| 3-4 | 业务用户 | 客服、销售 |
| 5-6 | 高级用户 | 主管、经理 |
| 7-8 | 管理用户 | 部门经理、系统管理员 |
| 9-10 | 超级用户 | 超级管理员、系统所有者 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/roles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "销售经理",
    "code": "sales_manager",
    "level": 6,
    "description": "负责销售团队管理和业务决策的角色",
    "status": "active",
    "permissions": [
      "products:read",
      "products:create",
      "products:update",
      "orders:*",
      "customers:*",
      "reports:sales"
    ],
    "metadata": {
      "department": "sales",
      "can_approve_discount": true,
      "max_discount_rate": 0.2
    }
  }'
```

#### 响应格式

**成功响应 (201 Created):**
```json
{
  "code": 201,
  "message": "角色创建成功",
  "data": {
    "role": {
      "id": "role_1705123456789",
      "name": "销售经理",
      "code": "sales_manager",
      "level": 6,
      "description": "负责销售团队管理和业务决策的角色",
      "status": "active",
      "user_count": 0,
      "permissions": [
        "products:read",
        "products:create",
        "products:update",
        "orders:*",
        "customers:*",
        "reports:sales"
      ],
      "metadata": {
        "department": "sales",
        "can_approve_discount": true,
        "max_discount_rate": 0.2
      },
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

**冲突响应 (409 Conflict):**
```json
{
  "code": 409,
  "message": "角色代码已存在",
  "data": {
    "error": "role_code_exists",
    "details": "角色代码 'sales_manager' 已被使用",
    "existing_role": {
      "id": "role_existing",
      "name": "现有销售经理"
    }
  }
}
```

---

### 2. 获取所有角色列表

**GET** `/api/roles`

#### 描述
获取系统中所有已定义的用户角色列表，支持筛选和排序。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token>
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| status | string | 否 | 筛选角色状态 (active/inactive) |
| level_min | integer | 否 | 最小角色级别 |
| level_max | integer | 否 | 最大角色级别 |
| department | string | 否 | 筛选部门 |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |
| sort | string | 否 | 排序字段 (name/level/created_at) |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/roles?status=active&level_min=3&sort=level" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取角色列表成功",
  "data": {
    "roles": [
      {
        "id": "role_1705123456789",
        "name": "销售经理",
        "code": "sales_manager",
        "level": 6,
        "description": "负责销售团队管理和业务决策的角色",
        "status": "active",
        "user_count": 5,
        "permission_count": 6,
        "created_at": "2024-01-20T10:00:00.000Z",
        "updated_at": "2024-01-20T10:00:00.000Z"
      },
      {
        "id": "role_1705123456790",
        "name": "客服专员",
        "code": "customer_service",
        "level": 3,
        "description": "处理客户咨询和售后服务的角色",
        "status": "active",
        "user_count": 12,
        "permission_count": 4,
        "created_at": "2024-01-20T09:30:00.000Z",
        "updated_at": "2024-01-20T09:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 8,
      "pages": 1,
      "has_next": false,
      "has_prev": false
    },
    "summary": {
      "total_roles": 8,
      "active_roles": 7,
      "inactive_roles": 1,
      "total_users": 45,
      "avg_permissions_per_role": 5.2
    }
  }
}
```

---

### 3. 获取特定角色详情

**GET** `/api/roles/{role_id}`

#### 描述
获取指定角色的详细信息，包括权限列表和关联用户信息。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| include_users | boolean | 否 | 是否包含关联用户信息，默认false |
| include_permissions | boolean | 否 | 是否包含详细权限信息，默认true |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/roles/role_1705123456789?include_users=true" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取角色详情成功",
  "data": {
    "role": {
      "id": "role_1705123456789",
      "name": "销售经理",
      "code": "sales_manager",
      "level": 6,
      "description": "负责销售团队管理和业务决策的角色",
      "status": "active",
      "permissions": [
        {
          "id": "perm_products_read",
          "name": "products:read",
          "description": "查看商品信息",
          "resource": "products",
          "action": "read"
        },
        {
          "id": "perm_orders_all",
          "name": "orders:*",
          "description": "订单完全权限",
          "resource": "orders",
          "action": "*"
        }
      ],
      "users": [
        {
          "id": "user_001",
          "name": "张三",
          "email": "<EMAIL>",
          "status": "active",
          "assigned_at": "2024-01-15T10:00:00.000Z"
        },
        {
          "id": "user_002",
          "name": "李四",
          "email": "<EMAIL>",
          "status": "active",
          "assigned_at": "2024-01-18T14:30:00.000Z"
        }
      ],
      "metadata": {
        "department": "sales",
        "can_approve_discount": true,
        "max_discount_rate": 0.2
      },
      "statistics": {
        "user_count": 5,
        "permission_count": 6,
        "active_users": 5,
        "last_login": "2024-01-20T11:45:00.000Z"
      },
      "created_at": "2024-01-20T10:00:00.000Z",
      "updated_at": "2024-01-20T10:00:00.000Z"
    }
  }
}
```

---

### 4. 更新角色信息

**PUT** `/api/roles/{role_id}`

#### 描述
更新指定角色的基本信息，包括名称、描述、级别等。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "name": "string",
  "description": "string",
  "level": number,
  "status": "string",
  "metadata": {}
}
```

#### 请求示例
```bash
curl -X PUT "http://localhost:5000/api/roles/role_1705123456789" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "高级销售经理",
    "description": "负责销售团队管理、业务决策和战略规划的高级角色",
    "level": 7,
    "status": "active",
    "metadata": {
      "department": "sales",
      "can_approve_discount": true,
      "max_discount_rate": 0.3,
      "can_access_analytics": true
    }
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "角色更新成功",
  "data": {
    "role": {
      "id": "role_1705123456789",
      "name": "高级销售经理",
      "code": "sales_manager",
      "level": 7,
      "description": "负责销售团队管理、业务决策和战略规划的高级角色",
      "status": "active",
      "user_count": 5,
      "metadata": {
        "department": "sales",
        "can_approve_discount": true,
        "max_discount_rate": 0.3,
        "can_access_analytics": true
      },
      "updated_at": "2024-01-20T12:00:00.000Z"
    }
  }
}
```

---

### 5. 删除角色

**DELETE** `/api/roles/{role_id}`

#### 描述
删除指定的用户角色。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| force | boolean | 否 | 是否强制删除，默认false |
| reassign_to | string | 否 | 重新分配用户到的角色ID |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/roles/role_1705123456789?force=false&reassign_to=role_default" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "角色删除成功",
  "data": {
    "role_id": "role_1705123456789",
    "name": "销售经理",
    "deleted_at": "2024-01-20T12:00:00.000Z",
    "affected_users": 5,
    "reassigned_to": "role_default"
  }
}
```

---

### 6. 获取角色权限列表

**POST** `/api/roles/{role_id}/permissions`

#### 描述
为指定角色分配新的权限，支持批量分配。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "permissions": ["string"],
  "replace": boolean
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| permissions | array | 是 | 权限列表 |
| replace | boolean | 否 | 是否替换现有权限，默认false（追加） |

#### 权限格式说明

权限格式：`resource:action`

- `resource`: 资源名称（如：products, orders, users）
- `action`: 操作类型（如：read, create, update, delete, *）
- `*`: 表示所有操作

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/roles/role_1705123456789/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "permissions": [
      "analytics:read",
      "reports:create",
      "users:read",
      "settings:update"
    ],
    "replace": false
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "权限分配成功",
  "data": {
    "role": {
      "id": "role_1705123456789",
      "name": "高级销售经理",
      "code": "sales_manager"
    },
    "permissions": {
      "added": [
        "analytics:read",
        "reports:create",
        "users:read",
        "settings:update"
      ],
      "existing": [
        "products:read",
        "products:create",
        "products:update",
        "orders:*",
        "customers:*",
        "reports:sales"
      ],
      "total": 10
    }
  }
}
```

---

### 6. 获取角色权限列表

**GET** `/api/roles/{role_id}/permissions`

#### 描述
获取指定角色的所有权限列表，包括权限详细信息。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| resource | string | 否 | 筛选特定资源的权限 |
| action | string | 否 | 筛选特定操作的权限 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/roles/role_1705123456789/permissions?resource=products" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "获取角色权限成功",
  "data": {
    "role": {
      "id": "role_1705123456789",
      "name": "高级销售经理",
      "code": "sales_manager"
    },
    "permissions": [
      {
        "id": "perm_products_read",
        "name": "products:read",
        "description": "查看商品信息",
        "resource": "products",
        "action": "read",
        "granted_at": "2024-01-20T10:00:00.000Z"
      },
      {
        "id": "perm_products_create",
        "name": "products:create",
        "description": "创建商品",
        "resource": "products",
        "action": "create",
        "granted_at": "2024-01-20T10:00:00.000Z"
      },
      {
        "id": "perm_products_update",
        "name": "products:update",
        "description": "更新商品信息",
        "resource": "products",
        "action": "update",
        "granted_at": "2024-01-20T10:00:00.000Z"
      }
    ],
    "summary": {
      "total_permissions": 10,
      "by_resource": {
        "products": 3,
        "orders": 4,
        "customers": 4,
        "reports": 2,
        "analytics": 1,
        "users": 1,
        "settings": 1
      },
      "by_action": {
        "read": 4,
        "create": 3,
        "update": 2,
        "*": 1
      }
    }
  }
}
```

---

### 8. 移除角色权限

**DELETE** `/api/roles/{role_id}/permissions/{permission_id}`

#### 描述
移除指定角色的特定权限。

#### 请求参数

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role_id | string | 是 | 角色唯一标识符 |
| permission_id | string | 是 | 权限唯一标识符 |

**请求头：**
```
Authorization: Bearer <token>
```

#### 请求示例
```bash
curl -X DELETE "http://localhost:5000/api/roles/role_1705123456789/permissions/perm_products_delete" \
  -H "Authorization: Bearer <token>"
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "角色权限移除成功",
  "data": {
    "role": {
      "id": "role_1705123456789",
      "name": "销售经理",
      "code": "sales_manager"
    },
    "permission": {
      "id": "perm_products_delete",
      "name": "products:delete",
      "description": "删除商品"
    },
    "removed_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

## 📝 角色状态说明

| 状态 | 描述 |
|------|------|
| active | 活跃状态，角色正常使用 |
| inactive | 非活跃状态，角色暂时停用 |
| deprecated | 已弃用，计划移除 |
| locked | 已锁定，不允许修改 |

---

## 📝 权限资源说明

| 资源 | 描述 | 常用操作 |
|------|------|----------|
| products | 商品管理 | read, create, update, delete |
| orders | 订单管理 | read, create, update, delete, process |
| customers | 客户管理 | read, create, update, delete |
| users | 用户管理 | read, create, update, delete |
| roles | 角色管理 | read, create, update, delete |
| reports | 报表查看 | read, create, export |
| analytics | 数据分析 | read, dashboard |
| settings | 系统设置 | read, update |

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| role_not_found | 404 | 角色不存在 | 检查角色ID是否正确 |
| role_code_exists | 409 | 角色代码已存在 | 使用不同的角色代码 |
| invalid_role_level | 400 | 无效的角色级别 | 使用1-10之间的数字 |
| role_in_use | 409 | 角色正在使用中 | 先移除关联用户再删除角色 |
| permission_not_found | 404 | 权限不存在 | 检查权限名称是否正确 |
| insufficient_level | 403 | 权限级别不足 | 只能管理级别不高于自己的角色 |
