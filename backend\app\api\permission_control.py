"""
权限控制API路由
实现6个权限控制相关的API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from app.core.auth import verify_developer_token
from app.services.permission_service import PermissionService
from app.schemas.permission_control import (
    PermissionCheckRequest, RoleAPIPermissionRequest, BatchPermissionUpdateRequest,
    PermissionMatrixUpdateRequest
)

router = APIRouter(prefix="/api/permissions", tags=["权限控制"])

# 创建服务实例
permission_service = PermissionService()


@router.post("/check", response_model=dict)
async def check_permission(
    request: PermissionCheckRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    检查用户是否有访问特定API的权限
    
    - **user_id**: 用户唯一标识符
    - **resource**: 资源名称（如：products, orders）
    - **action**: 操作类型（如：read, create, update, delete）
    - **context**: 上下文信息（可选）
    """
    result = permission_service.check_permission(request, current_user.get("user_id"))
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "权限检查失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    # 根据权限检查结果返回不同的HTTP状态码
    if result["allowed"]:
        return {
            "code": 200,
            "message": "权限检查完成",
            "data": result["data"]
        }
    else:
        raise HTTPException(status_code=403, detail={
            "code": 403,
            "message": "权限不足",
            "data": result["data"]
        })


@router.get("/user-apis", response_model=dict)
async def get_user_apis(
    user_id: str = Query(..., description="用户唯一标识符"),
    resource: Optional[str] = Query(None, description="筛选特定资源的API"),
    method: Optional[str] = Query(None, description="筛选特定HTTP方法的API"),
    include_details: bool = Query(False, description="是否包含API详细信息"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取指定用户可以访问的API列表
    
    - **user_id**: 用户唯一标识符
    - **resource**: 筛选特定资源的API（可选）
    - **method**: 筛选特定HTTP方法的API（可选）
    - **include_details**: 是否包含API详细信息（可选）
    """
    result = permission_service.get_user_accessible_apis(user_id, resource, method, include_details)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "获取用户API权限失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    return {
        "code": 200,
        "message": "获取用户API权限成功",
        "data": result["data"]
    }


@router.post("/role-api", response_model=dict)
async def update_role_api_permission(
    request: RoleAPIPermissionRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    更新角色对特定API的访问权限
    
    - **role_id**: 角色唯一标识符
    - **api_id**: API唯一标识符
    - **permission**: 权限名称（如：products:read）
    - **action**: 操作类型（grant授予，revoke撤销）
    """
    result = permission_service.update_role_api_permission(request, current_user.get("user_id"))
    
    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        elif result["error"] == "api_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "API不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        elif result["error"] == "permission_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "权限不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        elif result["error"] in ["permission_already_granted", "permission_not_granted"]:
            raise HTTPException(status_code=400, detail={
                "code": 400,
                "message": "权限状态冲突",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "权限更新失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "权限更新成功",
        "data": result["data"]
    }


@router.post("/batch-update", response_model=dict)
async def batch_update_permissions(
    request: BatchPermissionUpdateRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    批量更新多个角色的API权限
    
    - **updates**: 权限更新列表
    - **dry_run**: 是否为试运行模式（可选）
    """
    result = permission_service.batch_update_permissions(request, current_user.get("user_id"))
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "批量权限更新失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    return {
        "code": 200,
        "message": "批量权限更新成功",
        "data": result["data"]
    }


@router.get("/matrix", response_model=dict)
async def get_permission_matrix(
    format: str = Query("matrix", description="返回格式（matrix/list）"),
    include_inactive: bool = Query(False, description="是否包含非活跃角色"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取系统的完整权限矩阵
    
    - **format**: 返回格式（matrix/list），默认matrix
    - **include_inactive**: 是否包含非活跃角色，默认false
    """
    result = permission_service.get_permission_matrix(format, include_inactive)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "获取权限矩阵失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    return {
        "code": 200,
        "message": "获取权限矩阵成功",
        "data": result["data"]
    }


@router.post("/matrix", response_model=dict)
async def update_permission_matrix(
    request: PermissionMatrixUpdateRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    批量更新权限矩阵
    
    - **matrix**: 权限矩阵对象
    - **merge_mode**: 合并模式（replace/merge），默认merge
    """
    result = permission_service.update_permission_matrix(request, current_user.get("user_id"))
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "权限矩阵更新失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    return {
        "code": 200,
        "message": "权限矩阵更新成功",
        "data": result["data"]
    }
