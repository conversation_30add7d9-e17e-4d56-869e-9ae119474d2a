"""
表单配置相关数据库模型
包括表单定义、表单分组、表单字段和表单数据
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, JSON, Enum, ForeignKey, Boolean, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class FormStatus(enum.Enum):
    """表单状态枚举"""
    DRAFT = "draft"          # 草稿
    ACTIVE = "active"        # 活跃
    INACTIVE = "inactive"    # 非活跃
    ARCHIVED = "archived"    # 已归档


class LayoutType(enum.Enum):
    """布局类型枚举"""
    GRID = "grid"            # 网格布局
    FLEX = "flex"            # 弹性布局
    TABS = "tabs"            # 标签页布局
    ACCORDION = "accordion"  # 手风琴布局


class DisplayType(enum.Enum):
    """字段显示类型枚举"""
    INPUT = "input"              # 普通输入框
    TEXTAREA = "textarea"        # 多行文本
    SELECT = "select"            # 下拉选择
    RADIO = "radio"              # 单选按钮
    CHECKBOX = "checkbox"        # 复选框
    SWITCH = "switch"            # 开关
    DATE_PICKER = "date-picker"  # 日期选择
    UPLOAD = "upload"            # 文件上传
    RICH_TEXT = "rich-text"      # 富文本编辑器


class FormDBModel(Base):
    """表单配置数据库模型"""
    __tablename__ = "forms"
    
    # 基本信息
    id = Column(String(50), primary_key=True, comment="表单ID")
    name = Column(String(200), nullable=False, comment="表单名称")
    entity = Column(String(100), nullable=False, comment="关联实体")
    description = Column(Text, comment="表单描述")
    
    # 布局配置
    layout_type = Column(Enum(LayoutType), default=LayoutType.GRID, comment="布局类型")
    layout_columns = Column(Integer, default=2, comment="布局列数")
    layout_spacing = Column(Integer, default=16, comment="布局间距")
    layout_config = Column(JSON, comment="布局配置JSON")
    
    # 状态和统计
    status = Column(Enum(FormStatus), default=FormStatus.ACTIVE, comment="表单状态")
    field_count = Column(Integer, default=0, comment="字段数量")
    section_count = Column(Integer, default=0, comment="分组数量")
    
    # 权限配置
    permissions_config = Column(JSON, comment="权限配置JSON")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    sections = relationship("FormSectionDBModel", back_populates="form", cascade="all, delete-orphan")
    fields = relationship("FormFieldDBModel", back_populates="form", cascade="all, delete-orphan")
    data_records = relationship("FormDataDBModel", back_populates="form", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        {"mysql_engine": "InnoDB", "mysql_charset": "utf8mb4"},
    )


class FormSectionDBModel(Base):
    """表单分组数据库模型"""
    __tablename__ = "form_sections"
    
    # 基本信息
    id = Column(String(50), primary_key=True, comment="分组ID")
    form_id = Column(String(50), ForeignKey("forms.id", ondelete="CASCADE"), nullable=False, comment="表单ID")
    section_id = Column(String(100), nullable=False, comment="分组标识")
    title = Column(String(200), nullable=False, comment="分组标题")
    
    # 配置信息
    collapsible = Column(Boolean, default=False, comment="是否可折叠")
    collapsed = Column(Boolean, default=False, comment="默认是否折叠")
    order_index = Column(Integer, default=0, comment="排序索引")
    
    # 统计信息
    field_count = Column(Integer, default=0, comment="字段数量")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    form = relationship("FormDBModel", back_populates="sections")
    fields = relationship("FormFieldDBModel", back_populates="section", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        {"mysql_engine": "InnoDB", "mysql_charset": "utf8mb4"},
    )


class FormFieldDBModel(Base):
    """表单字段数据库模型"""
    __tablename__ = "form_fields"
    
    # 基本信息
    id = Column(String(50), primary_key=True, comment="字段ID")
    form_id = Column(String(50), ForeignKey("forms.id", ondelete="CASCADE"), nullable=False, comment="表单ID")
    section_id = Column(String(50), ForeignKey("form_sections.id", ondelete="CASCADE"), comment="分组ID")
    field_id = Column(String(100), nullable=False, comment="字段标识")
    entity_field = Column(String(100), nullable=False, comment="实体字段名")
    
    # 显示配置
    display_type = Column(Enum(DisplayType), nullable=False, comment="显示类型")
    label = Column(String(200), nullable=False, comment="字段标签")
    placeholder = Column(String(500), comment="占位符文本")
    
    # 状态配置
    required = Column(Boolean, default=False, comment="是否必填")
    readonly = Column(Boolean, default=False, comment="是否只读")
    hidden = Column(Boolean, default=False, comment="是否隐藏")
    
    # 验证配置
    validation_rules = Column(JSON, comment="验证规则JSON")
    validation_messages = Column(JSON, comment="验证消息JSON")
    
    # 选项配置（用于select、radio、checkbox等）
    options_config = Column(JSON, comment="选项配置JSON")
    
    # 布局配置
    order_index = Column(Integer, default=0, comment="排序索引")
    grid_span = Column(Integer, default=1, comment="网格跨度")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    form = relationship("FormDBModel", back_populates="fields")
    section = relationship("FormSectionDBModel", back_populates="fields")
    
    # 索引
    __table_args__ = (
        {"mysql_engine": "InnoDB", "mysql_charset": "utf8mb4"},
    )


class FormDataDBModel(Base):
    """表单数据数据库模型"""
    __tablename__ = "form_data"
    
    # 基本信息
    id = Column(String(50), primary_key=True, comment="数据ID")
    form_id = Column(String(50), ForeignKey("forms.id", ondelete="CASCADE"), nullable=False, comment="表单ID")
    entity_id = Column(String(50), comment="实体记录ID")
    
    # 数据内容
    form_data = Column(JSON, nullable=False, comment="表单数据JSON")
    
    # 提交信息
    submitted_by = Column(String(100), comment="提交用户ID")
    submitted_at = Column(DateTime, default=func.now(), comment="提交时间")
    
    # 状态信息
    status = Column(String(20), default="submitted", comment="数据状态")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    form = relationship("FormDBModel", back_populates="data_records")
    
    # 索引
    __table_args__ = (
        {"mysql_engine": "InnoDB", "mysql_charset": "utf8mb4"},
    )


# 创建索引
def create_form_indexes():
    """创建表单相关索引"""
    from sqlalchemy import Index
    
    # forms表索引
    Index('idx_forms_entity', FormDBModel.entity)
    Index('idx_forms_status', FormDBModel.status)
    Index('idx_forms_created_at', FormDBModel.created_at)
    
    # form_sections表索引
    Index('idx_form_sections_form_id', FormSectionDBModel.form_id)
    Index('idx_form_sections_order', FormSectionDBModel.form_id, FormSectionDBModel.order_index)
    
    # form_fields表索引
    Index('idx_form_fields_form_id', FormFieldDBModel.form_id)
    Index('idx_form_fields_section_id', FormFieldDBModel.section_id)
    Index('idx_form_fields_entity_field', FormFieldDBModel.entity_field)
    Index('idx_form_fields_order', FormFieldDBModel.form_id, FormFieldDBModel.order_index)
    
    # form_data表索引
    Index('idx_form_data_form_id', FormDataDBModel.form_id)
    Index('idx_form_data_entity_id', FormDataDBModel.entity_id)
    Index('idx_form_data_submitted_by', FormDataDBModel.submitted_by)
    Index('idx_form_data_submitted_at', FormDataDBModel.submitted_at)
    Index('idx_form_data_status', FormDataDBModel.status)
