"""
模板管理模块API路由
严格按照API文档实现模板管理的2个接口
"""
from fastapi import APIRouter, HTTPException, Header, Query
from fastapi.responses import JSONResponse
from typing import Optional
from app.schemas.template import (
    TemplateListResponse,
    TemplateDetailResponse,
    TemplateErrorResponse,
    TemplateErrorData
)
from app.services.template_service import template_service

router = APIRouter(prefix="/templates", tags=["模板管理模块"])


@router.get(
    "",
    response_model=TemplateListResponse,
    summary="获取所有可用模板",
    description="获取系统中所有可用的业务场景模板列表"
)
async def get_templates_list(
    category: Optional[str] = Query(None, description="模板分类筛选"),
    include_preview: bool = Query(False, description="是否包含预览信息，默认false"),
    authorization: Optional[str] = Header(None)
):
    """
    获取所有可用模板接口
    
    - **category**: 模板分类筛选（可选）
    - **include_preview**: 是否包含预览信息，默认false
    
    返回模板列表和分类统计信息
    """
    # 验证分类参数
    valid_categories = [
        "business", "healthcare", "education", "hospitality", 
        "finance", "logistics", "government", "custom"
    ]
    
    if category and category not in valid_categories:
        raise HTTPException(
            status_code=400,
            detail={
                "code": 400,
                "message": "无效的分类参数",
                "data": {
                    "error": "invalid_category",
                    "details": f"分类参数必须是以下值之一: {', '.join(valid_categories)}"
                }
            }
        )
    
    # 调用模板服务
    result = template_service.get_templates_list(
        category=category,
        include_preview=include_preview
    )
    
    if result["success"]:
        # 查询成功
        return JSONResponse(
            status_code=200,
            content={
                "code": 200,
                "message": "获取模板列表成功",
                "data": {
                    "templates": [template.model_dump() for template in result["data"]["templates"]],
                    "total": result["data"]["total"],
                    "categories": [category.model_dump() for category in result["data"]["categories"]]
                }
            }
        )
    else:
        # 查询失败
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "获取模板列表失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            }
        )


@router.get(
    "/{template_key}",
    response_model=TemplateDetailResponse,
    summary="获取特定模板详细配置",
    description="获取指定模板的详细配置信息，包括完整的实体、工作流、表单等配置"
)
async def get_template_detail(
    template_key: str,
    authorization: Optional[str] = Header(None)
):
    """
    获取特定模板详细配置接口
    
    - **template_key**: 模板唯一标识符
    
    返回指定模板的详细配置信息
    """
    # 调用模板服务
    result = template_service.get_template_detail(template_key)
    
    if result["success"]:
        # 查询成功
        template_detail = result["data"]["template"]
        
        return JSONResponse(
            status_code=200,
            content={
                "code": 200,
                "message": "获取模板详情成功",
                "data": {
                    "template": template_detail.model_dump()
                }
            }
        )
    else:
        # 查询失败
        if result["error"] == "template_not_found":
            status_code = 404
            message = "模板不存在"
        else:
            status_code = 500
            message = "获取模板详情失败"
        
        raise HTTPException(
            status_code=status_code,
            detail={
                "code": status_code,
                "message": message,
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            }
        )
