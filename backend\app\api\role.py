"""
角色管理API路由
实现8个角色管理相关的API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from app.core.auth import verify_developer_token
from app.services.role_service import RoleService
from app.schemas.role import (
    RoleCreateRequest, RoleUpdateRequest, RolePermissionsRequest
)

router = APIRouter(prefix="/api/roles", tags=["角色管理"])

# 创建服务实例
role_service = RoleService()


@router.post("", response_model=dict, status_code=201)
async def create_role(
    request: RoleCreateRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    创建新的用户角色
    
    - **name**: 角色名称
    - **code**: 角色代码（唯一标识）
    - **level**: 角色级别（1-10）
    - **description**: 角色描述
    - **status**: 角色状态（active/inactive）
    - **permissions**: 初始权限列表
    - **metadata**: 角色元数据
    """
    result = role_service.create_role(request, current_user.get("user_id"))
    
    if not result["success"]:
        if result["error"] == "role_code_exists":
            raise HTTPException(status_code=409, detail={
                "code": 409,
                "message": "角色代码已存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"],
                    "existing_role": result.get("existing_role")
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "创建角色失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 201,
        "message": "角色创建成功",
        "data": result["data"]
    }


@router.get("", response_model=dict)
async def get_roles(
    status: Optional[str] = Query(None, description="筛选角色状态 (active/inactive)"),
    level_min: Optional[int] = Query(None, ge=1, le=10, description="最小角色级别"),
    level_max: Optional[int] = Query(None, ge=1, le=10, description="最大角色级别"),
    department: Optional[str] = Query(None, description="筛选部门"),
    page: int = Query(1, ge=1, description="页码，默认1"),
    limit: int = Query(20, ge=1, le=100, description="每页数量，默认20"),
    sort: str = Query("created_at", description="排序字段 (name/level/created_at)"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取系统中所有已定义的用户角色列表
    
    支持按状态、级别、部门筛选和排序
    """
    result = role_service.get_roles_list(status, level_min, level_max, department, page, limit, sort)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail={
            "code": 500,
            "message": "获取角色列表失败",
            "data": {
                "error": result["error"],
                "details": result["details"]
            }
        })
    
    return {
        "code": 200,
        "message": "获取角色列表成功",
        "data": result["data"]
    }


@router.get("/{role_id}", response_model=dict)
async def get_role_detail(
    role_id: str,
    include_users: bool = Query(False, description="是否包含关联用户信息"),
    include_permissions: bool = Query(True, description="是否包含详细权限信息"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取指定角色的详细信息
    
    - **role_id**: 角色唯一标识符
    - **include_users**: 是否包含关联用户信息
    - **include_permissions**: 是否包含详细权限信息
    """
    result = role_service.get_role_detail(role_id, include_users, include_permissions)
    
    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "获取角色详情失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "获取角色详情成功",
        "data": result["data"]
    }


@router.put("/{role_id}", response_model=dict)
async def update_role(
    role_id: str,
    request: RoleUpdateRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    更新指定角色的基本信息
    
    - **role_id**: 角色唯一标识符
    - **name**: 角色名称（可选）
    - **description**: 角色描述（可选）
    - **level**: 角色级别（可选）
    - **status**: 角色状态（可选）
    - **metadata**: 角色元数据（可选）
    """
    result = role_service.update_role(role_id, request, current_user.get("user_id"))
    
    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "更新角色失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "角色更新成功",
        "data": result["data"]
    }


@router.delete("/{role_id}", response_model=dict)
async def delete_role(
    role_id: str,
    force: bool = Query(False, description="是否强制删除"),
    reassign_to: Optional[str] = Query(None, description="重新分配用户到的角色ID"),
    current_user: dict = Depends(verify_developer_token)
):
    """
    删除指定的用户角色
    
    - **role_id**: 角色唯一标识符
    - **force**: 是否强制删除
    - **reassign_to**: 重新分配用户到的角色ID
    """
    result = role_service.delete_role(role_id, force, reassign_to, current_user.get("user_id"))
    
    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        elif result["error"] == "role_in_use":
            raise HTTPException(status_code=409, detail={
                "code": 409,
                "message": "角色正在使用中",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "删除角色失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "角色删除成功",
        "data": result["data"]
    }


@router.post("/{role_id}/permissions", response_model=dict)
async def assign_permissions(
    role_id: str,
    request: RolePermissionsRequest,
    current_user: dict = Depends(verify_developer_token)
):
    """
    为指定角色分配新的权限
    
    - **role_id**: 角色唯一标识符
    - **permissions**: 权限列表
    - **replace**: 是否替换现有权限
    """
    result = role_service.assign_permissions(role_id, request, current_user.get("user_id"))
    
    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "权限分配失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
    
    return {
        "code": 200,
        "message": "权限分配成功",
        "data": result["data"]
    }


@router.get("/{role_id}/permissions", response_model=dict)
async def get_role_permissions(
    role_id: str,
    resource: Optional[str] = Query(None, description="筛选特定资源的权限"),
    action: Optional[str] = Query(None, description="筛选特定操作的权限"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取指定角色的所有权限列表

    - **role_id**: 角色唯一标识符
    - **resource**: 筛选特定资源的权限
    - **action**: 筛选特定操作的权限
    """
    result = role_service.get_role_permissions(role_id, resource, action)

    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "获取角色权限失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })

    return {
        "code": 200,
        "message": "获取角色权限成功",
        "data": result["data"]
    }


@router.delete("/{role_id}/permissions/{permission_id}", response_model=dict)
async def remove_permission(
    role_id: str,
    permission_id: str,
    current_user: dict = Depends(verify_developer_token)
):
    """
    移除指定角色的特定权限

    - **role_id**: 角色唯一标识符
    - **permission_id**: 权限唯一标识符
    """
    result = role_service.remove_permission(role_id, permission_id, current_user.get("user_id"))

    if not result["success"]:
        if result["error"] == "role_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "角色不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        elif result["error"] == "permission_not_found":
            raise HTTPException(status_code=404, detail={
                "code": 404,
                "message": "权限不存在",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        elif result["error"] == "permission_not_assigned":
            raise HTTPException(status_code=400, detail={
                "code": 400,
                "message": "权限未分配给该角色",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })
        else:
            raise HTTPException(status_code=500, detail={
                "code": 500,
                "message": "移除权限失败",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            })

    return {
        "code": 200,
        "message": "角色权限移除成功",
        "data": result["data"]
    }
