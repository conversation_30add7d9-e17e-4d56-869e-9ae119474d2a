/* 工作流画布样式 - Apple风格设计 */

.workflow-canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f7;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 工具栏样式 */
.canvas-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 10;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-title {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-right: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 画布区域 */
.canvas-area {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #f5f5f7 0%, #fafafa 100%);
  overflow: hidden;
}

/* 节点样式 */
.workflow-node {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;
  position: relative;
}

.workflow-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

.workflow-node.selected {
  border-color: #007aff !important;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2) !important;
}

/* 节点类型特定样式 */
.workflow-node-start {
  border-left: 4px solid #52c41a;
}

.workflow-node-api_call {
  border-left: 4px solid #1890ff;
}

.workflow-node-user_input {
  border-left: 4px solid #722ed1;
}

.workflow-node-condition {
  border-left: 4px solid #fa8c16;
}

.workflow-node-notification {
  border-left: 4px solid #eb2f96;
}

.workflow-node-end {
  border-left: 4px solid #f5222d;
}

/* 节点内部元素 */
.node-icon {
  transition: all 0.3s ease;
}

.node-title {
  font-weight: 600;
  color: #1d1d1f;
}

.node-description {
  color: #86868b;
  font-size: 12px;
  line-height: 1.4;
}

.node-status {
  transition: all 0.3s ease;
}

/* 端点样式 */
.butterfly-endpoint {
  width: 12px !important;
  height: 12px !important;
  border: 2px solid #007aff !important;
  background: white !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
}

.butterfly-endpoint:hover {
  width: 16px !important;
  height: 16px !important;
  background: #007aff !important;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2) !important;
}

.butterfly-endpoint.connected {
  background: #007aff !important;
}

/* 连线样式 */
.butterfly-edge {
  stroke: #007aff;
  stroke-width: 2;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  transition: all 0.3s ease;
}

.butterfly-edge:hover {
  stroke: #0051d5;
  stroke-width: 3;
}

.butterfly-edge.selected {
  stroke: #0051d5;
  stroke-width: 3;
  filter: drop-shadow(0 0 6px rgba(0, 122, 255, 0.4));
}

/* 箭头样式 */
.butterfly-arrow {
  fill: #007aff;
  stroke: #007aff;
  transition: all 0.3s ease;
}

/* 标签样式 */
.edge-label-group {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 抽屉样式 */
.drawer-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 编辑器样式 */
.node-editor-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
}

.editor-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .workflow-node {
    width: 140px !important;
    min-height: 70px !important;
    padding: 10px !important;
  }
}

/* 动画效果 */
@keyframes nodeAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.workflow-node {
  animation: nodeAppear 0.3s ease-out;
}

/* 选择状态 */
.workflow-node.butterfly-node-selected {
  border-color: #007aff !important;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2) !important;
}

/* 拖拽状态 */
.workflow-node.butterfly-node-dragging {
  opacity: 0.8;
  transform: rotate(2deg);
}

/* 连接状态 */
.workflow-node.butterfly-node-connecting {
  border-color: #52c41a !important;
  box-shadow: 0 0 0 3px rgba(82, 196, 26, 0.2) !important;
}
