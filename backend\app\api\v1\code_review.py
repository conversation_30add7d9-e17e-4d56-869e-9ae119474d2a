"""
代码审查API路由
提供代码审查、批准、拒绝等功能
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional

from app.schemas.code_review import (
    ReviewRequest, ReviewResponse, ApprovalRequest, RejectionRequest,
    ApprovalResponse, ReviewStatusResponse
)
from app.services.code_review_service import code_review_service
from app.core.auth.jwt_handler import verify_developer_token

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["代码审查"])


@router.post("/code-review", response_model=ReviewResponse)
async def review_generated_code(
    request: ReviewRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    审查生成的代码
    
    对AI生成的代码进行安全检查、质量评估等全面审查
    """
    try:
        result = await code_review_service.review_generated_code(request.generation_id)
        
        if result["success"]:
            return ReviewResponse(
                success=True,
                data=result["data"]
            )
        else:
            return ReviewResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"代码审查失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "代码审查失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/code-review/approve", response_model=ApprovalResponse)
async def approve_code(
    request: ApprovalRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    批准代码
    
    批准通过审查的代码，将其移动到生产环境
    """
    try:
        result = await code_review_service.approve_code(
            review_id=request.review_id,
            approver=request.approver,
            comments=request.comments
        )
        
        if result["success"]:
            return ApprovalResponse(
                success=True,
                data=result["data"]
            )
        else:
            return ApprovalResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"代码批准失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "代码批准失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/code-review/reject", response_model=ApprovalResponse)
async def reject_code(
    request: RejectionRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    拒绝代码
    
    拒绝未通过审查的代码，记录拒绝原因
    """
    try:
        result = await code_review_service.reject_code(
            review_id=request.review_id,
            reviewer=request.reviewer,
            reason=request.reason
        )
        
        if result["success"]:
            return ApprovalResponse(
                success=True,
                data=result["data"]
            )
        else:
            return ApprovalResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"代码拒绝失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "代码拒绝失败",
                "data": {"error": str(e)}
            }
        )


@router.get("/code-review/status", response_model=ReviewStatusResponse)
async def get_review_status(
    review_id: Optional[str] = Query(None, description="特定审查ID"),
    _: dict = Depends(verify_developer_token)
):
    """
    获取代码审查状态
    
    返回当前或指定审查任务的状态和结果信息
    """
    try:
        result = await code_review_service.get_review_status(review_id)
        
        if result["success"]:
            return ReviewStatusResponse(
                success=True,
                current_review=result.get("current_review"),
                recent_reviews=result.get("recent_reviews")
            )
        else:
            return ReviewStatusResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"获取审查状态失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "获取审查状态失败",
                "data": {"error": str(e)}
            }
        )
