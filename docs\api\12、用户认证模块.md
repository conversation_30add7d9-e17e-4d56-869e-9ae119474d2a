# 用户认证模块 API 文档

## 📋 概述

用户认证模块提供面向最终用户的身份验证和权限管理功能，与开发者认证不同，这是基于角色的用户认证系统，支持用户注册、登录、登出和权限查询。

## 👤 API 端点列表

### 用户身份管理
1. 用户注册
2. 用户登录
3. 用户登出
4. 获取当前用户信息

### 权限查询
5. 获取用户权限列表
6. 刷新访问令牌

---

## API 详细文档

### 1. 用户注册

**POST** `/api/user/register`

#### 描述
创建新的用户账户，自动分配默认角色，支持邮箱验证。

#### 请求参数

**Content-Type:** `application/json`

**请求体：**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "phone": "string",
  "real_name": "string",
  "role_id": "integer"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名，3-20字符，只能包含字母、数字和下划线 |
| email | string | 是 | 邮箱地址，必须是有效的邮箱格式 |
| password | string | 是 | 密码，6-20字符 |
| phone | string | 否 | 手机号码 |
| real_name | string | 否 | 真实姓名 |
| role_id | integer | 否 | 角色ID，默认为普通用户角色 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/user/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "real_name": "新用户"
  }'
```

#### 响应格式

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 123,
    "username": "newuser",
    "email": "<EMAIL>",
    "role": {
      "id": 2,
      "name": "普通用户",
      "level": 1
    },
    "created_at": "2024-01-20T10:00:00.000Z"
  }
}
```

**错误响应 (409):**
```json
{
  "code": 409,
  "message": "注册失败",
  "data": {
    "error": "username_exists",
    "details": "用户名已存在"
  }
}
```

---

### 2. 用户登录

**POST** `/api/user/login`

#### 描述
用户身份验证，支持用户名或邮箱登录，返回JWT访问令牌和用户权限信息。

#### 请求参数

**Content-Type:** `application/json`

**请求体：**
```json
{
  "username": "string",
  "password": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名或邮箱地址 |
| password | string | 是 | 用户密码 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "password123"
  }'
```

#### 响应格式

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": 123,
      "username": "newuser",
      "email": "<EMAIL>",
      "real_name": "新用户",
      "role": {
        "id": 2,
        "name": "普通用户",
        "level": 1,
        "permissions": [
          "user:read",
          "user:update_self",
          "data:query"
        ]
      }
    },
    "permissions": {
      "apis": [
        {
          "endpoint": "/api/user/me",
          "method": "GET",
          "description": "获取个人信息",
          "resource": "user",
          "action": "read"
        },
        {
          "endpoint": "/api/command",
          "method": "POST",
          "description": "AI命令处理",
          "resource": "ai",
          "action": "command"
        }
      ],
      "features": [
        "profile_management",
        "ai_interaction",
        "data_query"
      ]
    }
  }
}
```

**错误响应 (401):**
```json
{
  "code": 401,
  "message": "登录失败",
  "data": {
    "error": "invalid_credentials",
    "details": "用户名或密码错误"
  }
}
```

---

### 3. 用户登出

**POST** `/api/user/logout`

#### 描述
用户登出，使当前访问令牌失效。

#### 请求参数

**请求头：**
```
Authorization: Bearer <access_token>
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/user/logout" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### 响应格式

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "登出成功",
  "data": {
    "logged_out_at": "2024-01-20T12:00:00.000Z"
  }
}
```

---

### 4. 获取当前用户信息

**GET** `/api/user/me`

#### 描述
获取当前登录用户的详细信息。

#### 请求参数

**请求头：**
```
Authorization: Bearer <access_token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/user/me" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### 响应格式

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 123,
    "username": "newuser",
    "email": "<EMAIL>",
    "real_name": "新用户",
    "phone": "13800138000",
    "role": {
      "id": 2,
      "name": "普通用户",
      "level": 1
    },
    "created_at": "2024-01-20T10:00:00.000Z",
    "last_login": "2024-01-20T11:30:00.000Z"
  }
}
```

---

### 5. 获取用户权限列表

**GET** `/api/user/permissions`

#### 描述
获取当前用户的详细权限信息，包括可访问的API列表和功能权限。

#### 请求参数

**请求头：**
```
Authorization: Bearer <access_token>
```

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/user/permissions" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### 响应格式

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "role": {
      "id": 2,
      "name": "普通用户",
      "level": 1
    },
    "permissions": [
      "user:read",
      "user:update_self",
      "data:query"
    ],
    "accessible_apis": [
      {
        "endpoint": "/api/user/me",
        "method": "GET",
        "description": "获取个人信息",
        "resource": "user",
        "action": "read"
      },
      {
        "endpoint": "/api/command",
        "method": "POST",
        "description": "AI命令处理",
        "resource": "ai",
        "action": "command"
      }
    ],
    "restricted_apis": [
      {
        "endpoint": "/api/admin/users",
        "method": "GET",
        "reason": "需要管理员权限"
      }
    ]
  }
}
```

---

### 6. 刷新访问令牌

**POST** `/api/user/refresh-token`

#### 描述
使用当前有效的访问令牌获取新的访问令牌。

#### 请求参数

**请求头：**
```
Authorization: Bearer <access_token>
```

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/user/refresh-token" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### 响应格式

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600
  }
}
```

---

## 📝 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权访问 | 需要登录或Token已过期 |
| 403 | 权限不足 | 当前角色无权限访问该资源 |
| 409 | 资源冲突 | 用户名或邮箱已存在 |
| 422 | 参数验证失败 | 检查参数格式和约束条件 |
| 500 | 服务器内部错误 | 联系系统管理员 |

## 🔐 角色权限说明

### 角色级别定义
- **超级管理员 (level: 9)** - 所有API访问权限，用户管理权限，系统配置权限
- **管理员 (level: 5)** - 大部分API访问权限，用户查看权限，数据管理权限  
- **普通用户 (level: 1)** - 基础API访问权限，个人信息管理权限，数据查询权限
- **访客 (level: 0)** - 只读权限，基础查询权限

### 权限继承规则
- 高级别角色自动继承低级别角色的基础权限
- 权限采用"或"逻辑，任一角色有权限即可访问
- 特殊权限需要显式授予

## 🛡️ 安全考虑

### 密码安全
- 密码使用bcrypt加密存储
- 最小长度6位，建议包含数字和字母
- 支持密码强度验证

### Token安全
- JWT Token包含用户ID、角色信息
- Token有效期1小时，支持刷新
- 登出时Token加入黑名单

### 权限验证
- 每个API请求都进行权限验证
- 基于角色的访问控制(RBAC)
- 动态权限检查

### 防护措施
- 登录失败次数限制
- IP访问频率限制
- SQL注入防护
- XSS攻击防护
