/**
 * AILF Main Page - 主页面组件
 * 遵循单一文件规则，包含原有的主界面逻辑
 */

import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../App.css';
import '../styles/apple-style.css';
import axios from 'axios';
// 移除amis相关导入，使用原生通知
import copy from 'copy-to-clipboard';
import GeneratedInterface from '../components/GeneratedInterface';
import SiriAnimation from '../components/SiriAnimation';
import { useVoice } from '../hooks/useVoice';

// AILF 配置
const AILF_CONFIG = {
  apiBaseUrl: 'http://localhost:9000',
  amisTheme: 'cxd'
};

const MainPage: React.FC = () => {
  const navigate = useNavigate();

  // 控制body样式以启用呼吸效果
  useEffect(() => {
    // 确保主页面有正确的样式
    document.body.className = 'main-page-body';
    document.body.style.background = '';
    document.body.style.animation = '';

    return () => {
      // 清理body类名，让其他页面可以设置自己的样式
      document.body.className = '';
      document.body.style.background = '';
      document.body.style.animation = '';
    };
  }, []);

  // 状态管理
  const [amisSchema, setAmisSchema] = useState<any>(null);
  const [amisKey, setAmisKey] = useState(0);
  const [responseText, setResponseText] = useState('');
  const [errorText, setErrorText] = useState('');
  const [isSystemConfigured] = useState(true); // 系统配置状态 - 暂时设为true以便测试

  // 语音相关状态
  const {
    isListening,
    transcript,
    isSpeaking,
    volume,
    startListening,
    stopListening,
    speak
  } = useVoice();

  // Amis 环境配置
  const amisEnv = {
    fetcher: async (api: any) => {
      console.log('[AILF] Amis fetcher called:', api);
      try {
        const response = await axios({
          method: api.method || 'get',
          url: api.url,
          data: api.data,
          headers: api.headers
        });
        return {
          status: response.status,
          msg: response.statusText,
          data: response.data
        };
      } catch (error: any) {
        console.error('[AILF] Amis fetcher error:', error);
        return {
          status: error.response?.status || 500,
          msg: error.message,
          data: null
        };
      }
    },
    copy: (text: string) => {
      copy(text);
      console.log('已复制到剪贴板');
    },
    notify: (type: string, msg: string) => {
      console.log(`${type}: ${msg}`);
    }
  };

  // 处理语音命令
  const handleVoiceCommand = useCallback(async (command: string) => {
    if (!command.trim()) return;

    console.log('[AILF] 处理语音命令:', command);
    setErrorText('');
    setResponseText('正在处理您的请求...');

    try {
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // 直接使用稳定的command端点，避免场景配置问题
      const response = await axios.post(`${AILF_CONFIG.apiBaseUrl}/api/command`, {
        command: command,
        context: {
          user_id: "user_123", // TODO: 从认证系统获取真实用户ID
          session_id: `session_${Date.now()}`
        }
      }, {
        timeout: 60000,
        headers
      });

      if (response.data && response.data.data && response.data.data.schema) {
        console.log('[AILF] 收到API响应:', response.data);
        setAmisKey(prev => prev + 1);
        setAmisSchema(response.data.data.schema);
        setResponseText('');

        // 语音反馈
        if (response.data.data.response_text) {
          speak(response.data.data.response_text);
        } else if (response.data.message) {
          speak(response.data.message);
        } else {
          speak('界面已生成完成');
        }
      } else {
        const errorMsg = response.data?.message || '未能生成有效的界面配置';
        setErrorText(errorMsg);
        speak(errorMsg);
      }
    } catch (error: any) {
      console.error('[AILF] API调用失败:', error);
      let errorMessage = '抱歉，处理您的请求时出现了问题';
      
      if (error.code === 'ECONNREFUSED') {
        errorMessage = '无法连接到后端服务，请确保后端服务正在运行';
      } else if (error.response?.status === 404) {
        errorMessage = 'API端点不存在，请检查后端配置';
      } else if (error.response?.status >= 500) {
        errorMessage = '服务器内部错误，请稍后重试';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }
      
      setErrorText(errorMessage);
      setResponseText('');
      speak(errorMessage);
    }
  }, [speak]);

  // 监听语音识别结果
  useEffect(() => {
    if (transcript && !isListening) {
      handleVoiceCommand(transcript);
    }
  }, [transcript, isListening, handleVoiceCommand]);

  // 测试语音命令函数
  const handleTestVoiceCommand = useCallback(async () => {
    const testCommands = [
      "创建一个用户管理表格",
      "生成一个登录表单",
      "做一个数据统计图表",
      "创建一个产品列表页面"
    ];

    const randomCommand = testCommands[Math.floor(Math.random() * testCommands.length)];

    console.log(`🧪 [测试] 模拟语音命令: ${randomCommand}`);

    // 显示测试提示
    const testNotification = document.createElement('div');
    testNotification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 122, 255, 0.95);
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      font-size: 14px;
      z-index: 1000;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      max-width: 300px;
    `;
    testNotification.innerHTML = `
      <div style="font-weight: 600; margin-bottom: 8px;">🧪 测试语音命令</div>
      <div style="font-size: 12px; opacity: 0.9;">命令: "${randomCommand}"</div>
      <div style="font-size: 12px; opacity: 0.7; margin-top: 4px;">正在发送到后端...</div>
    `;

    document.body.appendChild(testNotification);

    try {
      // 调用实际的语音命令处理函数
      await handleVoiceCommand(randomCommand);

      // 更新通知为成功状态
      testNotification.style.background = 'rgba(52, 199, 89, 0.95)';
      testNotification.innerHTML = `
        <div style="font-weight: 600; margin-bottom: 8px;">✅ 测试成功</div>
        <div style="font-size: 12px; opacity: 0.9;">命令: "${randomCommand}"</div>
        <div style="font-size: 12px; opacity: 0.7; margin-top: 4px;">后端连接正常，界面已生成</div>
      `;

    } catch (error: any) {
      console.error('🧪 [测试] 语音命令测试失败:', error);

      // 更新通知为错误状态
      testNotification.style.background = 'rgba(255, 59, 48, 0.95)';
      testNotification.innerHTML = `
        <div style="font-weight: 600; margin-bottom: 8px;">❌ 测试失败</div>
        <div style="font-size: 12px; opacity: 0.9;">命令: "${randomCommand}"</div>
        <div style="font-size: 12px; opacity: 0.7; margin-top: 4px;">错误: ${error.message}</div>
      `;
    }

    // 5秒后移除通知
    setTimeout(() => {
      if (document.body.contains(testNotification)) {
        document.body.removeChild(testNotification);
      }
    }, 5000);
  }, [handleVoiceCommand]);

  // 诊断连接函数
  const handleDiagnoseConnection = useCallback(async () => {
    console.log('🔍 [诊断] 开始连接诊断');

    const diagnosticNotification = document.createElement('div');
    diagnosticNotification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 20px;
      background: rgba(255, 149, 0, 0.95);
      color: white;
      padding: 20px;
      border-radius: 12px;
      font-size: 14px;
      z-index: 1000;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      max-width: 400px;
      min-width: 350px;
    `;

    let diagnosticResults = {
      backendPing: '⏳ 检查中...',
      apiCommand: '⏳ 检查中...',
      apiInteract: '⏳ 检查中...',
      voiceSupport: '⏳ 检查中...',
      micPermission: '⏳ 检查中...'
    };

    const updateNotification = () => {
      diagnosticNotification.innerHTML = `
        <div style="font-weight: 600; margin-bottom: 12px;">🔍 连接诊断报告</div>
        <div style="font-size: 12px; line-height: 1.6;">
          <div>后端服务: ${diagnosticResults.backendPing}</div>
          <div>/api/command: ${diagnosticResults.apiCommand}</div>
          <div>/api/interact: ${diagnosticResults.apiInteract}</div>
          <div>语音识别: ${diagnosticResults.voiceSupport}</div>
          <div>麦克风权限: ${diagnosticResults.micPermission}</div>
        </div>
      `;
    };

    document.body.appendChild(diagnosticNotification);
    updateNotification();

    try {
      // 1. 检查后端服务
      try {
        const pingResponse = await fetch(`${AILF_CONFIG.apiBaseUrl}/`, {
          method: 'GET'
        });
        diagnosticResults.backendPing = pingResponse.ok ? '✅ 正常' : '❌ 异常';
      } catch {
        diagnosticResults.backendPing = '❌ 无法连接';
      }
      updateNotification();

      // 2. 检查 /api/command 端点
      try {
        const commandResponse = await fetch(`${AILF_CONFIG.apiBaseUrl}/api/command`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            command: "测试连接",
            context: {
              user_id: "diagnostic_user",
              session_id: `diagnostic_${Date.now()}`
            }
          })
        });
        diagnosticResults.apiCommand = commandResponse.ok ? '✅ 正常' : `❌ ${commandResponse.status}`;
      } catch (error: any) {
        diagnosticResults.apiCommand = `❌ ${error.message}`;
      }
      updateNotification();

      // 3. 检查 /api/interact 端点
      try {
        const interactResponse = await fetch(`${AILF_CONFIG.apiBaseUrl}/api/interact`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            user_input: "测试",
            user_id: "test",
            scenario_id: "1",
            input_type: "voice"
          })
        });
        diagnosticResults.apiInteract = interactResponse.ok ? '✅ 正常' : `❌ ${interactResponse.status}`;
      } catch (error: any) {
        diagnosticResults.apiInteract = `❌ ${error.message}`;
      }
      updateNotification();

      // 4. 检查语音识别支持
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      diagnosticResults.voiceSupport = SpeechRecognition ? '✅ 支持' : '❌ 不支持';
      updateNotification();

      // 5. 检查麦克风权限
      try {
        const permissionResult = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        diagnosticResults.micPermission = permissionResult.state === 'granted' ? '✅ 已授权' :
                                         permissionResult.state === 'denied' ? '❌ 被拒绝' : '⚠️ 需要授权';
      } catch {
        diagnosticResults.micPermission = '❓ 无法检查';
      }
      updateNotification();

      console.log('🔍 [诊断] 诊断完成:', diagnosticResults);

    } catch (error) {
      console.error('🔍 [诊断] 诊断过程出错:', error);
    }

    // 10秒后移除通知
    setTimeout(() => {
      if (document.body.contains(diagnosticNotification)) {
        document.body.removeChild(diagnosticNotification);
      }
    }, 10000);
  }, []);

  // 切换语音监听状态
  const toggleListening = useCallback(() => {
    if (isListening) {
      stopListening();
    } else {
      setErrorText('');
      setResponseText('');
      startListening();
    }
  }, [isListening, startListening, stopListening]);

  // 关闭生成的界面
  const closeGeneratedInterface = useCallback(() => {
    setAmisSchema(null);
    setResponseText('');
    setErrorText('');
  }, []);

  // 如果系统未配置，显示配置提示
  if (!isSystemConfigured) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        color: 'white',
        fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        textAlign: 'center'
      }}>
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '20px',
          padding: '40px',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          maxWidth: '500px'
        }}>
          <div style={{
            fontSize: '64px',
            marginBottom: '20px'
          }}>⚠️</div>
          <h1 style={{
            fontSize: '2.5rem',
            marginBottom: '20px',
            fontWeight: '300'
          }}>系统未配置</h1>
          <p style={{
            fontSize: '1.2rem',
            marginBottom: '30px',
            opacity: 0.9,
            lineHeight: '1.6'
          }}>
            AILF系统需要进行初始配置才能使用。<br/>
            请联系管理员进行系统配置。
          </p>
          <button
            onClick={() => navigate('/config')}
            style={{
              background: 'linear-gradient(135deg, #FF6B35, #F7931E)',
              border: 'none',
              borderRadius: '25px',
              color: 'white',
              padding: '15px 30px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)'
            }}
          >
            🔧 进入开发者配置
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="App main-page">
      <div className="siri-container">
        <div className="app-name">AILF</div>

        <div className="response-area">
          {errorText && <div className="error-message">{errorText}</div>}

          {responseText && <div className="ai-response">{responseText}</div>}

          {transcript && <div className="user-transcript">"{transcript}"</div>}

          {!responseText && !transcript && !errorText && (
            <div className="placeholder-text">
              您好，我是AILF智能助手<br />
              点击麦克风，告诉我您需要什么界面
            </div>
          )}
        </div>

        <div className="mic-button-container">
          <button
            className={`mic-button ${isListening ? 'listening' : ''}`}
            onClick={toggleListening}
            aria-label={isListening ? '停止录音' : '开始录音'}
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2s2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
              <path d="M19 10v1c0 3.87-3.13 7-7 7s-7-3.13-7-7v-1h2v1c0 2.76 2.24 5 5 5s5-2.24 5-5v-1h2z"/>
              <path d="M11 19v2h2v-2h-2z"/>
              <path d="M8 21h8v2H8v-2z"/>
            </svg>
          </button>
        </div>
      </div>

      {/* 右下角隐蔽开发者菜单 */}
      <div className="dev-menu-container">
        <button className="dev-menu-button" aria-label="开发者菜单">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </button>
        <div className="dev-menu-dropdown">
          <button
            className="dev-menu-item"
            onClick={() => navigate('/config')}
          >
            开发者配置
          </button>

          <button
            className="dev-menu-item"
            onClick={() => window.open('http://localhost:9000/docs', '_blank')}
          >
            API 文档
          </button>

          <button
            className="dev-menu-item"
            onClick={() => navigate('/about')}
          >
            关于
          </button>

          <button
            className="dev-menu-item"
            onClick={handleTestVoiceCommand}
          >
            🧪 测试语音命令
          </button>

          <button
            className="dev-menu-item"
            onClick={handleDiagnoseConnection}
          >
            🔍 诊断连接
          </button>
        </div>
      </div>

      {/* 底部 SiriWave 动画 - 只在语音交互时显示 */}
      {(isListening || isSpeaking) && (
        <div className="bottom-siri-wave">
          <SiriAnimation
            isActive={true}
            amplitude={isListening ? Math.max(volume * 4, 0.8) : (isSpeaking ? 1.5 : 0.8)}
            style="ios9"
            height={100}
          />
        </div>
      )}

      {/* 生成的界面 */}
      <GeneratedInterface
        schema={amisSchema}
        amisKey={amisKey}
        amisEnv={amisEnv}
        onClose={closeGeneratedInterface}
        onToggleListening={toggleListening}
        isListening={isListening}
      />
    </div>
  );
};

export default MainPage;
