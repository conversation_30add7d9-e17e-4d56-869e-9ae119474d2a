"""
用户认证API路由
提供用户登录、注册、登出等功能
"""
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import bcrypt
from jose import JWTError
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.core.auth.jwt_handler import create_access_token, verify_token
from app.models.user import User
from app.models.role import RoleDBModel
from app.schemas.user_auth import (
    UserRegisterRequest, UserLoginRequest, UserResponse, 
    LoginResponse, PermissionsResponse, UserProfileResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/user", tags=["用户认证"])
security = HTTPBearer()


def hash_password(password: str) -> str:
    """密码哈希"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')


def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前登录用户"""
    try:
        token = credentials.credentials
        payload = verify_token(token)
        user_id = payload.get("user_id")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的token"
            )
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在"
            )
        
        return user
        
    except JWTError as e:
        error_msg = str(e).lower()
        if "expired" in error_msg:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token已过期"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的token"
            )


@router.post("/register", response_model=UserResponse)
async def register_user(
    request: UserRegisterRequest,
    db: Session = Depends(get_db)
):
    """
    用户注册
    
    创建新用户账户，分配默认角色
    """
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(
            (User.username == request.username) | 
            (User.email == request.email)
        ).first()
        
        if existing_user:
            if existing_user.username == request.username:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="用户名已存在"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="邮箱已存在"
                )
        
        # 获取默认角色（普通用户）
        default_role = db.query(RoleDBModel).filter(RoleDBModel.name == "普通用户").first()
        if not default_role:
            # 如果没有默认角色，使用第一个可用角色
            default_role = db.query(RoleDBModel).filter(RoleDBModel.status == "active").first()
            if not default_role:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="系统角色配置错误，请联系管理员"
                )
        
        # 创建新用户
        hashed_password = hash_password(request.password)
        new_user = User(
            username=request.username,
            email=request.email,
            password_hash=hashed_password,
            phone=request.phone,
            real_name=request.real_name,
            role_id=request.role_id or default_role.id,
            is_active=True
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 获取用户角色信息
        user_role = db.query(RoleDBModel).filter(RoleDBModel.id == new_user.role_id).first()
        
        return UserResponse(
            code=200,
            message="注册成功",
            data={
                "user_id": new_user.id,
                "username": new_user.username,
                "email": new_user.email,
                "role": {
                    "id": user_role.id,
                    "name": user_role.name,
                    "level": user_role.level
                },
                "created_at": new_user.created_at.isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=LoginResponse)
async def login_user(
    request: UserLoginRequest,
    db: Session = Depends(get_db)
):
    """
    用户登录
    
    验证用户凭据，返回访问令牌和权限信息
    """
    try:
        # 查找用户（支持用户名或邮箱登录）
        user = db.query(User).filter(
            (User.username == request.username) | 
            (User.email == request.username)
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 验证密码
        if not verify_password(request.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="账户已被禁用"
            )
        
        # 获取用户角色和权限
        user_role = db.query(RoleDBModel).filter(RoleDBModel.id == user.role_id).first()
        if not user_role:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="用户角色配置错误"
            )
        
        # 生成访问令牌
        token_data = {
            "user_id": user.id,
            "username": user.username,
            "role_id": user_role.id,
            "role_level": user_role.level
        }
        access_token = create_access_token(token_data)
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.commit()
        
        # 获取用户可访问的API列表
        accessible_apis = await get_user_accessible_apis(user_role, db)
        
        return LoginResponse(
            code=200,
            message="登录成功",
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 3600,
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "real_name": user.real_name,
                    "role": {
                        "id": user_role.id,
                        "name": user_role.name,
                        "level": user_role.level,
                        "permissions": self._get_role_permissions(user_role)
                    }
                },
                "permissions": {
                    "apis": accessible_apis,
                    "features": get_user_features(user_role)
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/logout")
async def logout_user(
    current_user: User = Depends(get_current_user)
):
    """
    用户登出
    
    使当前token失效
    """
    try:
        # 这里可以将token加入黑名单
        # 目前简单返回成功响应
        
        return {
            "code": 200,
            "message": "登出成功",
            "data": {
                "logged_out_at": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"用户登出失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败，请稍后重试"
        )


@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户信息
    
    返回当前登录用户的详细信息
    """
    try:
        user_role = db.query(RoleDBModel).filter(RoleDBModel.id == current_user.role_id).first()
        
        return UserProfileResponse(
            code=200,
            message="获取成功",
            data={
                "id": current_user.id,
                "username": current_user.username,
                "email": current_user.email,
                "real_name": current_user.real_name,
                "phone": current_user.phone,
                "role": {
                    "id": user_role.id,
                    "name": user_role.name,
                    "level": user_role.level
                },
                "created_at": current_user.created_at.isoformat(),
                "last_login": current_user.last_login.isoformat() if current_user.last_login else None
            }
        )
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


def _get_role_permissions(role: RoleDBModel) -> List[str]:
    """获取角色权限列表"""
    try:
        # 从角色的权限关联中获取权限
        permissions = []
        for permission in role.permissions:
            permissions.append(permission.name)

        # 如果没有关联权限，根据角色级别返回默认权限
        if not permissions:
            if role.level >= 9:  # 超级管理员
                permissions = ["*:*"]
            elif role.level >= 5:  # 管理员
                permissions = ["user:*", "data:*", "report:read"]
            elif role.level >= 1:  # 普通用户
                permissions = ["user:read", "user:update_self", "data:query"]
            else:  # 访客
                permissions = ["user:read"]

        return permissions

    except Exception:
        return ["user:read"]  # 默认最小权限


async def get_user_accessible_apis(role: RoleDBModel, db: Session) -> List[Dict[str, Any]]:
    """获取用户可访问的API列表"""
    try:
        # 这里应该从数据库查询API权限配置
        # 暂时返回基于角色级别的默认API列表
        
        base_apis = [
            {
                "endpoint": "/api/user/me",
                "method": "GET",
                "description": "获取个人信息",
                "resource": "user",
                "action": "read"
            },
            {
                "endpoint": "/api/user/logout",
                "method": "POST",
                "description": "用户登出",
                "resource": "user",
                "action": "logout"
            }
        ]
        
        if role.level >= 1:  # 普通用户及以上
            base_apis.extend([
                {
                    "endpoint": "/api/command",
                    "method": "POST",
                    "description": "AI命令处理",
                    "resource": "ai",
                    "action": "command"
                },
                {
                    "endpoint": "/api/ai/validate-schema",
                    "method": "POST",
                    "description": "Schema校验",
                    "resource": "ai",
                    "action": "validate"
                }
            ])
        
        if role.level >= 5:  # 管理员及以上
            base_apis.extend([
                {
                    "endpoint": "/api/users",
                    "method": "GET",
                    "description": "用户列表查询",
                    "resource": "user",
                    "action": "list"
                },
                {
                    "endpoint": "/api/roles",
                    "method": "GET",
                    "description": "角色列表查询",
                    "resource": "role",
                    "action": "list"
                }
            ])
        
        return base_apis
        
    except Exception as e:
        logger.error(f"获取用户API权限失败: {str(e)}")
        return []


def get_user_features(role: RoleDBModel) -> List[str]:
    """获取用户可用功能列表"""
    features = ["profile_management"]
    
    if role.level >= 1:
        features.extend(["ai_interaction", "data_query"])
    
    if role.level >= 5:
        features.extend(["user_management", "system_config"])
    
    return features
