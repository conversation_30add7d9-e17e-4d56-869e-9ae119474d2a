import React from 'react';
import ReactDOM from 'react-dom';
import './index.css'; // Global styles
import App from './App'; // The new Siri-like interface component
// import reportWebVitals from './reportWebVitals';

// AMIS global styles - keep these if AMIS components might be rendered by the AI later
import 'amis/lib/themes/cxd.css';
import 'amis/lib/helper.css';
import 'amis/sdk/iconfont.css';

// If you need ToastComponent, AlertComponent globally, they should be set up
// within the App component or a context provider if needed by dynamically loaded AMIS schemas.
// For now, removing direct amis-ui imports from index.tsx as App.tsx is the main UI.
// import { ToastComponent, AlertComponent } from 'amis-ui';


ReactDOM.render(
  // 暂时禁用 StrictMode 以避免 amis 状态管理问题
  // <React.StrictMode>
    <>
      {/* <ToastComponent key="toast" position={'top-right'} theme={'cxd'} locale={'zh-CN'} />
      <AlertComponent key="alert" theme={'cxd'} locale={'zh-CN'} /> */}
      <App />
    </>,
  // </React.StrictMode>
  document.getElementById('root')
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals();
