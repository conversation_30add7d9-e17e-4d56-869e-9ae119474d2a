/* 语音输入组件样式 */
.voice-input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.mic-button {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 10;
}

.mic-button:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

.mic-button.listening {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  animation: pulse 1.5s infinite;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
}

.mic-button svg {
  width: 48px;
  height: 48px;
}

/* 监听指示器 */
.listening-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #fff;
}

.listening-animation {
  display: flex;
  gap: 4px;
  align-items: center;
}

.wave {
  width: 4px;
  height: 20px;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 2px;
  animation: wave 1.2s ease-in-out infinite;
}

.wave:nth-child(2) { animation-delay: 0.1s; }
.wave:nth-child(3) { animation-delay: 0.2s; }

@keyframes wave {
  0%, 100% { height: 20px; }
  50% { height: 40px; }
}

/* 转录文本显示 */
.transcript-display {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 15px 25px;
  border-radius: 25px;
  color: #fff;
  max-width: 400px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Siri动画组件 */
.siri-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  pointer-events: none;
}

.siri-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.siri-animation .wave {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 60px;
  transform-origin: center bottom;
  border-radius: 1px;
  opacity: 0.7;
}

.siri-animation.active .wave {
  animation: siriWave 2s ease-in-out infinite;
}

.wave-1 {
  background: linear-gradient(to top, #667eea, #764ba2);
  transform: translate(-50%, -50%) rotate(-30deg);
  animation-delay: 0s;
}

.wave-2 {
  background: linear-gradient(to top, #f093fb, #f5576c);
  transform: translate(-50%, -50%) rotate(-15deg);
  animation-delay: 0.2s;
}

.wave-3 {
  background: linear-gradient(to top, #4facfe, #00f2fe);
  transform: translate(-50%, -50%) rotate(0deg);
  animation-delay: 0.4s;
}

.wave-4 {
  background: linear-gradient(to top, #43e97b, #38f9d7);
  transform: translate(-50%, -50%) rotate(15deg);
  animation-delay: 0.6s;
}

.wave-5 {
  background: linear-gradient(to top, #fa709a, #fee140);
  transform: translate(-50%, -50%) rotate(30deg);
  animation-delay: 0.8s;
}

@keyframes siriWave {
  0%, 100% {
    height: 60px;
    opacity: 0.7;
  }
  50% {
    height: 120px;
    opacity: 1;
  }
}

.siri-center-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #fff 0%, rgba(255,255,255,0.8) 100%);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
}

/* 生成界面组件样式 */
.amis-render-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(248, 249, 250, 0.98) 0%, 
    rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(40px);
  border-radius: 20px;
  overflow-y: auto;
  z-index: 100;
  animation: slideInFromBottom 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.amis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(248, 249, 250, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(10px);
}

.amis-content {
  flex: 1;
  padding: 30px;
  overflow: auto;
}

.close-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 59, 48, 0.2);
  transform: scale(1.1);
}

.generated-mic-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.generated-mic-button:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.generated-mic-button.listening {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  animation: pulse 1.5s infinite;
}

.listening-text {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #ff6b6b;
  white-space: nowrap;
}

/* 动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 语音不支持提示 */
.voice-unsupported {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  padding: 20px;
}

.voice-unsupported p {
  margin: 10px 0;
}
