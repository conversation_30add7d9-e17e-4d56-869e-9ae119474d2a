/**
 * 模板管理API服务
 */

import { apiClient } from '../common/apiClient';
import type {
  GetTemplatesRequest,
  GetTemplatesResponse,
  GetTemplateDetailResponse,
  TemplateCategory,
} from '../../types/developer/template';

export const templateAPI = {
  /**
   * 获取所有可用模板
   * GET /api/templates
   */
  async getTemplates(params?: GetTemplatesRequest): Promise<GetTemplatesResponse> {
    const queryParams = new URLSearchParams();
    
    if (params?.category) {
      queryParams.append('category', params.category);
    }
    
    if (params?.include_preview !== undefined) {
      queryParams.append('include_preview', params.include_preview.toString());
    }
    
    const url = `/api/templates${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    
    const response = await apiClient.get<GetTemplatesResponse['data']>(url);
    return response;
  },

  /**
   * 获取特定模板详细配置
   * GET /api/templates/{template_key}
   */
  async getTemplateDetail(templateKey: string): Promise<GetTemplateDetailResponse> {
    const response = await apiClient.get<GetTemplateDetailResponse['data']>(
      `/api/templates/${templateKey}`
    );
    return response;
  },

  /**
   * 获取模板分类列表
   */
  getCategories(): Array<{ value: TemplateCategory; label: string; description: string }> {
    return [
      { value: 'business', label: '商业管理', description: '电商、零售、企业管理等' },
      { value: 'healthcare', label: '医疗健康', description: '医院、诊所、健康管理等' },
      { value: 'education', label: '教育培训', description: '学校、培训机构、在线教育等' },
      { value: 'hospitality', label: '服务行业', description: '餐饮、酒店、旅游等' },
      { value: 'finance', label: '金融服务', description: '银行、保险、投资等' },
      { value: 'logistics', label: '物流运输', description: '货运、仓储、配送等' },
      { value: 'government', label: '政府机构', description: '政务服务、公共管理等' },
      { value: 'custom', label: '自定义', description: '其他特殊业务场景' },
    ];
  },

  /**
   * 获取难度等级信息
   */
  getDifficultyInfo(difficulty: string): { label: string; color: string; description: string } {
    const difficultyMap = {
      beginner: {
        label: '初级',
        color: '#52c41a',
        description: '配置简单，功能基础，适合新手',
      },
      intermediate: {
        label: '中级',
        color: '#faad14',
        description: '功能较全，需要一定配置经验',
      },
      advanced: {
        label: '高级',
        color: '#f5222d',
        description: '功能复杂，需要深入理解业务流程',
      },
    };

    return difficultyMap[difficulty as keyof typeof difficultyMap] || {
      label: '未知',
      color: '#d9d9d9',
      description: '未知难度等级',
    };
  },
};

export default templateAPI;
