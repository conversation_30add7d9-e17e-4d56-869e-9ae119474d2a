// 表单字段显示类型
export type FormFieldDisplayType = 
  | 'input' 
  | 'textarea' 
  | 'select' 
  | 'radio' 
  | 'checkbox' 
  | 'switch' 
  | 'date-picker' 
  | 'upload' 
  | 'rich-text';

// 表单布局类型
export type FormLayoutType = 'vertical' | 'horizontal' | 'grid' | 'inline' | 'tabs';

// 表单验证规则
export interface FormValidationRule {
  type: 'required' | 'min' | 'max' | 'minLength' | 'maxLength' | 'email' | 'phone' | 'numeric' | 'url' | 'date' | 'custom';
  value?: any;
  message: string;
}

// 表单字段配置
export interface FormFieldConfig {
  id: string;
  entityField: string;
  displayType: FormFieldDisplayType;
  label: string;
  placeholder?: string;
  required: boolean;
  readonly: boolean;
  hidden: boolean;
  validation: {
    rules: string[];
    messages: { [key: string]: string };
  };
  options?: { label: string; value: string }[]; // 用于select、radio等
  defaultValue?: any;
  helpText?: string;
  width?: number; // 在grid布局中的宽度比例
}

// 表单分组配置
export interface FormSectionConfig {
  id: string;
  title: string;
  description?: string;
  collapsible: boolean;
  collapsed?: boolean;
  fields: FormFieldConfig[];
  order: number;
}

// 表单布局配置
export interface FormLayoutConfig {
  type: FormLayoutType;
  columns?: number; // grid布局的列数
  spacing?: number; // 字段间距
  labelWidth?: number; // 标签宽度
  responsive?: boolean; // 是否响应式
}

// 表单权限配置
export interface FormPermissionConfig {
  role: string;
  actions: ('create' | 'read' | 'update' | 'delete' | 'export' | 'import')[];
}

// 表单配置
export interface FormConfig {
  id: string;
  name: string;
  entity: string;
  description: string;
  layout: FormLayoutConfig;
  sections: FormSectionConfig[];
  permissions: FormPermissionConfig[];
  created_at: string;
  updated_at: string;
}

// 创建表单配置请求
export interface FormConfigCreateRequest {
  name: string;
  entity: string;
  description: string;
  layout: FormLayoutConfig;
  sections: Omit<FormSectionConfig, 'id'>[];
  permissions: FormPermissionConfig[];
}

// 更新表单配置请求
export interface FormConfigUpdateRequest {
  name?: string;
  description?: string;
  layout?: FormLayoutConfig;
  sections?: FormSectionConfig[];
  permissions?: FormPermissionConfig[];
}

// 表单渲染配置（amis schema）
export interface FormRenderConfig {
  schema: any; // amis schema对象
  metadata: {
    form_id: string;
    entity: string;
    mode: 'create' | 'edit' | 'view';
    version: string;
  };
}

// 表单数据提交
export interface FormDataSubmission {
  data: { [key: string]: any };
  mode: 'create' | 'edit';
  record_id?: string;
}

// 表单验证结果
export interface FormValidationResult {
  valid: boolean;
  errors?: { [field: string]: string[] };
  warnings?: string[];
  suggestions?: string[];
}

// 表单字段选项
export interface FormFieldOption {
  label: string;
  value: string;
  disabled?: boolean;
  description?: string;
}

// 表单模板
export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  entity_type: string;
  layout: FormLayoutConfig;
  sections: Omit<FormSectionConfig, 'id'>[];
  tags: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // 预估时间（分钟）
  preview_image?: string;
}

// 表单统计信息
export interface FormStatistics {
  total_forms: number;
  total_submissions: number;
  avg_completion_time: number;
  most_used_fields: string[];
  error_rate: number;
}

// 表单导出配置
export interface FormExportConfig {
  format: 'json' | 'yaml' | 'xml';
  include_data: boolean;
  include_schema: boolean;
  date_range?: {
    start: string;
    end: string;
  };
}
