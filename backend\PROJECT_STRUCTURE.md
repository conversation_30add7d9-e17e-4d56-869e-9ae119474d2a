# AILF 项目结构

## 📁 项目根目录结构

```
backend/
├── 📄 main.py                    # FastAPI应用主入口
├── 📄 requirements.txt           # Python依赖包列表
├── 📄 init_db.py                # 数据库初始化脚本
├── 📄 generate_dev_token.py     # 开发者token生成工具
├── 📄 dev_token.txt             # 开发者认证token
├── 📄 ailf.db                   # SQLite数据库文件
├── 📄 PROJECT_STRUCTURE.md      # 项目结构说明（本文件）
│
├── 📁 app/                      # 应用核心代码
│   ├── 📄 __init__.py
│   ├── 📄 main.py               # FastAPI应用配置
│   ├── 📄 config.py             # 应用配置
│   │
│   ├── 📁 api/                  # API路由
│   │   └── 📁 v1/               # API v1版本
│   │       ├── 📄 auth.py       # 认证相关API
│   │       ├── 📄 scenario.py   # 场景管理API
│   │       ├── 📄 generate.py   # 代码生成API
│   │       ├── 📄 code_review.py # 代码审查API
│   │       └── 📄 activate.py   # 代码激活API
│   │
│   ├── 📁 core/                 # 核心功能
│   │   ├── 📄 security.py       # 安全认证
│   │   └── 📄 database.py       # 数据库连接
│   │
│   ├── 📁 models/               # 数据模型
│   │   ├── 📄 user.py           # 用户模型
│   │   ├── 📄 scenario.py       # 场景模型
│   │   ├── 📄 generation.py     # 代码生成模型
│   │   └── 📄 code_review.py    # 代码审查模型
│   │
│   ├── 📁 schemas/              # Pydantic数据模式
│   │   ├── 📄 user.py           # 用户数据模式
│   │   ├── 📄 scenario.py       # 场景数据模式
│   │   ├── 📄 generation.py     # 生成数据模式
│   │   └── 📄 code_review.py    # 审查数据模式
│   │
│   ├── 📁 services/             # 业务逻辑服务
│   │   ├── 📄 ai_service.py     # AI集成服务
│   │   ├── 📄 scenario_service.py # 场景管理服务
│   │   ├── 📄 generation_service.py # 代码生成服务
│   │   ├── 📄 code_review_service.py # 代码审查服务
│   │   └── 📄 activation_service.py # 代码激活服务
│   │
│   └── 📁 templates/            # 代码生成模板
│       ├── 📄 fastapi_template.py # FastAPI模板
│       ├── 📄 amis_template.py  # Amis前端模板
│       └── 📄 database_template.py # 数据库模板
│
├── 📁 tests/                    # 测试文件目录
│   ├── 📄 README.md             # 测试说明文档
│   ├── 📄 run_tests.py          # 测试运行器
│   │
│   ├── 📄 test_ai_integration.py # AI集成测试
│   ├── 📄 test_ai_apis.py       # AI API测试
│   ├── 📄 test_generation_api.py # 代码生成API测试
│   ├── 📄 test_complete_generation.py # 完整生成流程测试
│   ├── 📄 test_code_review.py   # 代码审查测试
│   ├── 📄 test_approve_activate.py # 审查批准和激活测试
│   └── 📄 ... 其他测试文件
│
├── 📁 generated/                # AI生成的代码
│   ├── 📁 gen_1753422199291/    # 生成项目示例
│   │   ├── 📄 generation_result.json # 生成结果
│   │   ├── 📄 routes.py         # 生成的API路由
│   │   ├── 📄 schemas.py        # 生成的数据模式
│   │   ├── 📄 pages.json        # 生成的前端页面
│   │   ├── 📄 test_api.py       # 生成的测试代码
│   │   └── 📄 README.md         # 生成的文档
│   └── 📁 ... 其他生成项目
│
├── 📁 approved_code/            # 审查批准的代码
│   └── 📁 gen_1753422199291/    # 批准的项目
│
├── 📁 code_review/              # 代码审查结果
│   └── 📄 review_*.json         # 审查报告文件
│
├── 📁 data/                     # 数据文件
│   └── 📁 scenarios/            # 场景配置数据
│
├── 📁 scripts/                  # 工具脚本
│   └── 📄 optimize_database.py  # 数据库优化脚本
│
└── 📁 activation_logs/          # 激活日志
    └── 📄 activation_*.log      # 激活日志文件
```

## 🚀 核心功能模块

### 1. AI集成模块 (`app/services/ai_service.py`)
- 阿里云百炼API集成
- qwen-coder-plus模型调用
- 自然语言处理和代码生成

### 2. 场景管理模块 (`app/api/v1/scenario.py`)
- 业务场景配置
- 实体定义和关系管理
- 工作流程设计

### 3. 代码生成模块 (`app/services/generation_service.py`)
- 完整系统代码生成
- 前端、后端、数据库一体化生成
- 测试代码和文档自动生成

### 4. 代码审查模块 (`app/services/code_review_service.py`)
- 多维度代码质量检查
- 安全性、性能、合规性审查
- 智能评分和建议

### 5. 代码激活模块 (`app/services/activation_service.py`)
- 生成代码的部署和激活
- 数据库迁移和服务启动
- API端点验证

## 📋 使用说明

### 启动应用
```bash
cd backend
python main.py
```

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python tests/test_ai_integration.py
python tests/run_tests.py test_ai_integration
```

### 初始化数据库
```bash
python init_db.py
```

### 生成开发者token
```bash
python generate_dev_token.py
```

## 🔧 开发环境要求

- Python 3.8+
- FastAPI
- SQLAlchemy
- 阿里云百炼API密钥
- SQLite数据库

## 📊 项目状态

✅ **完全就绪的功能**：
- AI集成和自然语言处理
- 场景配置和管理
- 完整代码生成流程
- 智能代码审查
- 审查批准和激活

✅ **测试覆盖**：
- 所有核心功能都有对应测试
- AI功能测试通过
- 代码生成测试通过
- 审查流程测试通过

🎯 **技术亮点**：
- AI驱动的端到端代码生成
- 多维度智能代码审查
- 完整的开发者工作流支持
- 高质量的生成代码

这是一个完整的AI驱动代码生成平台，可以从自然语言描述生成完整的业务系统！
