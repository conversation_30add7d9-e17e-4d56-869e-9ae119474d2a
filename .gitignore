# AILF项目 .gitignore

/backend_old

/siriwave-master
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
deepeye_env/

# Node.js dependencies
node_modules/
/.pnp
.pnp.js

# pnpm
pnpm-debug.log*
.pnpm-debug.log*

# Package manager lock files (keep pnpm-lock.yaml)
package-lock.json
yarn.lock

# React build
/frontend/build/
/build

# Testing
/coverage

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log
logs/

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Database
*.db
*.sqlite
*.sqlite3

# Secrets and API keys
secrets/
*.key
*.pem
*.p12
config/secrets.json

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.old

# AILF specific
backend/data/
backend/uploads/
frontend/public/uploads/

# Test files and outputs

/butterfly-master

# Cache
.cache/
*.cache
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover