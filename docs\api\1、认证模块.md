# 认证模块 API 文档

## 📋 概述

认证模块提供开发者身份验证和令牌管理功能，确保只有授权的开发者才能访问AILF配置系统。

## 🔐 API 端点列表

### 1. 开发者认证
### 2. 验证开发者令牌

---

## API 详细文档

### 1. 开发者认证

**POST** `/api/auth/developer`

#### 描述
开发者使用密码进行身份认证，获取访问令牌。

#### 请求参数

**Content-Type:** `application/json`

**请求体：**
```json
{
  "password": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| password | string | 是 | 开发者密码 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/auth/developer" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "AILF_DEV_2024_SECURE"
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "认证成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "token_type": "Bearer",
    "issued_at": "2024-01-20T10:00:00.000Z",
    "expires_at": "2024-01-20T11:00:00.000Z"
  }
}
```

**错误响应 (401 Unauthorized):**
```json
{
  "code": 401,
  "message": "密码错误",
  "data": {
    "error": "invalid_password",
    "details": "提供的密码不正确"
  }
}
```

**错误响应 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "error": "validation_error",
    "details": {
      "password": ["密码不能为空"]
    }
  }
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| token | string | JWT访问令牌 |
| expires_in | integer | 令牌有效期（秒） |
| token_type | string | 令牌类型，固定为"Bearer" |
| issued_at | string | 令牌签发时间（ISO 8601格式） |
| expires_at | string | 令牌过期时间（ISO 8601格式） |

---

### 2. 验证开发者令牌

**POST** `/api/auth/developer/verify-token`

#### 描述
验证开发者令牌的有效性，用于检查令牌是否过期或被撤销。

#### 请求参数

**Content-Type:** `application/json`

**请求头：**
```
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "token": "string"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| token | string | 是 | 需要验证的JWT令牌 |

#### 请求示例
```bash
curl -X POST "http://localhost:5000/api/auth/developer/verify-token" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -d '{
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

#### 响应格式

**成功响应 (200 OK):**
```json
{
  "code": 200,
  "message": "令牌有效",
  "data": {
    "valid": true,
    "user_type": "developer",
    "issued_at": "2024-01-20T10:00:00.000Z",
    "expires_at": "2024-01-20T11:00:00.000Z",
    "remaining_time": 2847
  }
}
```

**错误响应 (401 Unauthorized):**
```json
{
  "code": 401,
  "message": "令牌无效",
  "data": {
    "valid": false,
    "error": "token_expired",
    "details": "令牌已过期"
  }
}
```

**错误响应 (400 Bad Request):**
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "error": "validation_error",
    "details": {
      "token": ["令牌格式不正确"]
    }
  }
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| valid | boolean | 令牌是否有效 |
| user_type | string | 用户类型，固定为"developer" |
| issued_at | string | 令牌签发时间（ISO 8601格式） |
| expires_at | string | 令牌过期时间（ISO 8601格式） |
| remaining_time | integer | 剩余有效时间（秒） |

---

## 🔒 安全说明

### JWT 令牌规范
- **算法**: HS256
- **有效期**: 1小时（3600秒）
- **刷新**: 令牌过期后需要重新认证
- **存储**: 建议存储在内存中，避免持久化

### 密码安全
- 开发者密码应定期更换
- 密码传输使用HTTPS加密
- 服务端不存储明文密码

### 令牌使用
- 所有需要认证的API都需要在请求头中携带令牌
- 令牌格式: `Authorization: Bearer <token>`
- 令牌过期后会返回401状态码

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| invalid_password | 401 | 密码错误 | 检查密码是否正确 |
| validation_error | 400 | 参数验证失败 | 检查请求参数格式 |
| token_expired | 401 | 令牌已过期 | 重新进行认证获取新令牌 |
| token_invalid | 401 | 令牌格式无效 | 检查令牌格式是否正确 |
| missing_token | 401 | 缺少认证令牌 | 在请求头中添加Authorization |

---

## 🔄 使用流程

1. **获取令牌**
   ```
   POST /api/auth/developer
   → 返回JWT令牌
   ```

2. **使用令牌访问API**
   ```
   在所有需要认证的API请求头中添加:
   Authorization: Bearer <token>
   ```

3. **验证令牌（可选）**
   ```
   POST /api/auth/developer/verify-token
   → 检查令牌是否有效
   ```

4. **令牌过期处理**
   ```
   收到401错误 → 重新认证获取新令牌
   ```

---

## 💡 最佳实践

1. **令牌管理**
   - 在应用启动时进行认证
   - 将令牌存储在全局状态管理中
   - 设置令牌过期前的自动刷新机制

2. **错误处理**
   - 统一处理401错误，自动跳转到认证页面
   - 提供友好的错误提示信息
   - 记录认证失败的日志

3. **安全考虑**
   - 使用HTTPS传输敏感信息
   - 不在URL中传递令牌
   - 定期更换开发者密码
