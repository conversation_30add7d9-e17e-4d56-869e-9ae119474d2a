/**
 * 开发者认证步骤组件
 */

import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, Alert, Space, Typography, Card } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone, LockOutlined, UserOutlined } from '@ant-design/icons';
import { useDeveloperAuth } from '../../../hooks/developer/useDeveloperAuth';
import type { AuthFormData } from '../../../types/developer/auth';

const { Title, Text } = Typography;

interface AuthenticationStepProps {
  onSuccess: () => void;
}

const AuthenticationStep: React.FC<AuthenticationStepProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const { authState, authenticate, clearError } = useDeveloperAuth();

  const handleSubmit = async (values: AuthFormData) => {
    clearError();
    const result = await authenticate(values);

    if (result.success) {
      onSuccess();
    }
  };

  const handleFormChange = () => {
    if (authState.error) {
      clearError();
    }
  };

  return (
    <div className="authentication-step">
      <div style={{ maxWidth: 400, margin: '0 auto', padding: '40px 20px' }}>
        <Card>
          <div style={{ textAlign: 'center', marginBottom: 32 }}>
            <UserOutlined style={{ fontSize: 48, color: '#007aff', marginBottom: 16 }} />
            <Title level={2} style={{ margin: 0, color: '#1d1d1f' }}>
              开发者认证
            </Title>
            <Text type="secondary" style={{ fontSize: 14 }}>
              请输入开发者密码以继续配置
            </Text>
          </div>

          {authState.error && (
            <Alert
              message="认证失败"
              description={authState.error}
              type="error"
              showIcon
              closable
              onClose={clearError}
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            onValuesChange={handleFormChange}
            autoComplete="off"
          >
            <Form.Item
              name="password"
              label="开发者密码"
              rules={[
                { required: true, message: '请输入开发者密码' },
                { min: 6, message: '密码长度至少6位' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入开发者密码"
                size="large"
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                style={{ borderRadius: 8 }}
              />
            </Form.Item>

            <Form.Item name="rememberMe" valuePropName="checked">
              <Checkbox>
                记住登录状态
              </Checkbox>
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                loading={authState.isLoading}
                block
                style={{
                  borderRadius: 8,
                  height: 48,
                  fontSize: 16,
                  fontWeight: 500,
                }}
              >
                {authState.isLoading ? '认证中...' : '开始配置'}
              </Button>
            </Form.Item>
          </Form>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Space direction="vertical" size={4}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                认证成功后将进入配置流程
              </Text>
              <Text type="secondary" style={{ fontSize: 12 }}>
                请确保您有开发者权限
              </Text>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AuthenticationStep;
