/* AILF - 文本样式 */

/* AILF标题样式 - 清晰金属质感 */
.ailf-title {
  font-size: 4rem;
  font-weight: 200; /* 稍微增加字重，提升清晰度 */
  color: transparent;
  background: linear-gradient(135deg,
    #c0c0c0 0%,
    #ffffff 25%,
    #e8e8e8 50%,
    #ffffff 75%,
    #c0c0c0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  text-align: center;
  letter-spacing: 0.2em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  /* 简化阴影，保持清晰度 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  /* 确保文字渲染清晰 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  animation: float 6s ease-in-out infinite;
  position: relative;
  z-index: 20;
  margin-bottom: 2rem;
}

/* 占位文本样式 - 微软雅黑清晰白色 */
.placeholder-text {
  font-size: 1.8rem !important;
  font-weight: 400 !important; /* 中等粗细 强制应用 */
  color: #ffffff !important; /* 纯白色 强制应用 */
  text-align: center !important;
  line-height: 1.5 !important;
  font-family: 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', Arial, sans-serif !important;
  /* 移除描边效果，保持文字清晰 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important; /* 简单的阴影增强对比度 */
  /* 确保文字渲染清晰 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  animation: enhancedTextBreathe 6s ease-in-out infinite;
}

/* 副标题样式 */
.subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  animation: fadeIn 2s ease-out;
}

/* 描述文本样式 */
.description {
  font-size: 1rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  animation: fadeIn 2.5s ease-out;
}

/* 强调文本样式 */
.emphasis {
  font-weight: 600;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* 错误文本样式 */
.error-text {
  color: #ff4757;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
  animation: shake 0.5s ease-in-out;
}

/* 成功文本样式 */
.success-text {
  color: #2ed573;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(46, 213, 115, 0.5);
  animation: pulse 1s ease-in-out;
}

/* 警告文本样式 */
.warning-text {
  color: #ffa500;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
  animation: blink 1s ease-in-out infinite;
}

/* 信息文本样式 */
.info-text {
  color: #54a0ff;
  font-weight: 400;
  text-shadow: 0 0 8px rgba(84, 160, 255, 0.4);
}

/* 小标题样式 */
.small-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 代码文本样式 */
.code-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  color: #00ff88;
  font-size: 0.9rem;
}

/* 链接样式 */
.link-text {
  color: #00d4ff;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.link-text:hover {
  color: #ffffff;
  text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
  text-decoration: underline;
}

/* 引用文本样式 */
.quote-text {
  font-style: italic;
  color: rgba(255, 255, 255, 0.6);
  border-left: 3px solid #00d4ff;
  padding-left: 1rem;
  margin: 1rem 0;
}

/* 标签样式 */
.tag-text {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  margin: 0.2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 高亮文本样式 */
.highlight-text {
  background: linear-gradient(135deg, rgba(255, 255, 0, 0.3), rgba(255, 165, 0, 0.3));
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  color: #ffffff;
  font-weight: 600;
}

/* 渐变文本样式 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
  animation: rainbow 3s linear infinite;
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2px solid rgba(255, 255, 255, 0.75);
  white-space: nowrap;
  margin: 0 auto;
  animation: 
    typing 3.5s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: rgba(255, 255, 255, 0.75); }
}

/* 响应式文本 */
@media (max-width: 768px) {
  .ailf-title {
    font-size: 3rem;
    letter-spacing: 0.1em;
  }
  
  .placeholder-text {
    font-size: 1.5rem !important;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .description {
    font-size: 0.9rem;
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .ailf-title {
    font-size: 2.5rem;
    letter-spacing: 0.05em;
  }
  
  .placeholder-text {
    font-size: 1.3rem !important;
  }
  
  .subtitle {
    font-size: 0.9rem;
  }
  
  .description {
    font-size: 0.8rem;
    padding: 0 1.5rem;
  }
}
