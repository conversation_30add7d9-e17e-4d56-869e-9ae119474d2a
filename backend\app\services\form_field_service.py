"""
表单字段管理服务
处理表单字段的增删改查
"""
import time
from typing import Dict, Any, List
from datetime import datetime
from sqlalchemy.orm import Session


def format_datetime(dt: datetime) -> str:
    """格式化datetime为ISO 8601格式，符合API文档要求"""
    if dt is None:
        return ""
    # 格式化为 "2024-01-20T10:00:00.000Z"
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

from app.core.database import get_db_session
from app.models.form import (
    FormDBModel, FormSectionDBModel, FormFieldDBModel,
    DisplayType
)
from app.schemas.form import (
    FormFieldCreateRequest, FormFieldUpdateRequest, FormField
)


class FormFieldService:
    """表单字段服务类"""
    
    def _generate_id(self, prefix: str) -> str:
        """生成唯一ID"""
        import random
        timestamp = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)
        return f"{prefix}_{timestamp}_{random_suffix}"
    
    def get_form_fields(self, form_id: str) -> Dict[str, Any]:
        """获取表单字段列表"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()
                
                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }
                
                # 查询字段
                db_fields = db.query(FormFieldDBModel).filter(
                    FormFieldDBModel.form_id == form_id
                ).order_by(FormFieldDBModel.order_index).all()
                
                # 构建响应
                fields = []
                for db_field in db_fields:
                    field = FormField(
                        id=db_field.field_id,
                        entityField=db_field.entity_field,
                        displayType=db_field.display_type.value,
                        label=db_field.label,
                        placeholder=db_field.placeholder,
                        required=db_field.required,
                        readonly=db_field.readonly,
                        hidden=db_field.hidden,
                        validation={
                            "rules": db_field.validation_rules or [],
                            "messages": db_field.validation_messages or {}
                        } if db_field.validation_rules or db_field.validation_messages else None,
                        options=db_field.options_config or None,
                        gridSpan=db_field.grid_span,
                        orderIndex=db_field.order_index,
                        created_at=format_datetime(db_field.created_at),
                        updated_at=format_datetime(db_field.updated_at)
                    )
                    fields.append(field)
                
                return {
                    "success": True,
                    "data": {
                        "fields": [field.dict() for field in fields],
                        "total": len(fields)
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"获取表单字段列表失败: {str(e)}"
            }
    
    def add_form_field(self, form_id: str, request: FormFieldCreateRequest) -> Dict[str, Any]:
        """添加表单字段"""
        try:
            with get_db_session() as db:
                # 查询表单
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()
                
                if not db_form:
                    return {
                        "success": False,
                        "error": "form_not_found",
                        "details": "表单配置不存在"
                    }
                
                # 检查字段ID是否已存在
                existing_field = db.query(FormFieldDBModel).filter(
                    FormFieldDBModel.form_id == form_id,
                    FormFieldDBModel.field_id == request.field_id
                ).first()
                
                if existing_field:
                    return {
                        "success": False,
                        "error": "field_id_exists",
                        "details": f"字段ID {request.field_id} 已存在"
                    }
                
                # 查询分组（如果指定）
                section_db_id = None
                if request.section_id:
                    db_section = db.query(FormSectionDBModel).filter(
                        FormSectionDBModel.form_id == form_id,
                        FormSectionDBModel.section_id == request.section_id
                    ).first()
                    
                    if not db_section:
                        return {
                            "success": False,
                            "error": "section_not_found",
                            "details": f"分组 {request.section_id} 不存在"
                        }
                    section_db_id = db_section.id
                
                # 获取下一个排序索引
                max_order = db.query(FormFieldDBModel.order_index).filter(
                    FormFieldDBModel.form_id == form_id
                ).order_by(FormFieldDBModel.order_index.desc()).first()
                next_order = (max_order[0] if max_order else 0) + 1
                
                # 创建字段
                field_id = self._generate_id("field")
                db_field = FormFieldDBModel(
                    id=field_id,
                    form_id=form_id,
                    section_id=section_db_id,
                    field_id=request.field_id,
                    entity_field=request.entityField,
                    display_type=DisplayType(request.displayType),
                    label=request.label,
                    placeholder=request.placeholder,
                    required=request.required,
                    readonly=request.readonly,
                    hidden=request.hidden,
                    validation_rules=request.validation.rules if request.validation else [],
                    validation_messages=request.validation.messages if request.validation else {},
                    options_config=[opt.dict() for opt in (request.options or [])],
                    order_index=next_order,
                    grid_span=request.gridSpan
                )
                
                db.add(db_field)
                
                # 更新表单字段计数
                db_form.field_count += 1
                
                # 更新分组字段计数
                if section_db_id:
                    db_section = db.query(FormSectionDBModel).filter(FormSectionDBModel.id == section_db_id).first()
                    if db_section:
                        db_section.field_count += 1
                
                db.commit()
                
                # 构建响应
                field = FormField(
                    id=db_field.field_id,
                    entityField=db_field.entity_field,
                    displayType=db_field.display_type.value,
                    label=db_field.label,
                    placeholder=db_field.placeholder,
                    required=db_field.required,
                    readonly=db_field.readonly,
                    hidden=db_field.hidden,
                    validation={
                        "rules": db_field.validation_rules or [],
                        "messages": db_field.validation_messages or {}
                    } if db_field.validation_rules or db_field.validation_messages else None,
                    options=db_field.options_config or None,
                    gridSpan=db_field.grid_span,
                    orderIndex=db_field.order_index,
                    created_at=format_datetime(db_field.created_at),
                    updated_at=format_datetime(db_field.updated_at)
                )
                
                return {
                    "success": True,
                    "data": {"field": field.dict()}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"添加表单字段失败: {str(e)}"
            }
    
    def update_form_field(self, form_id: str, field_id: str, request: FormFieldUpdateRequest) -> Dict[str, Any]:
        """更新表单字段"""
        try:
            with get_db_session() as db:
                # 查询字段
                db_field = db.query(FormFieldDBModel).filter(
                    FormFieldDBModel.form_id == form_id,
                    FormFieldDBModel.field_id == field_id
                ).first()
                
                if not db_field:
                    return {
                        "success": False,
                        "error": "field_not_found",
                        "details": "表单字段不存在"
                    }
                
                # 更新字段
                if request.label is not None:
                    db_field.label = request.label
                if request.placeholder is not None:
                    db_field.placeholder = request.placeholder
                if request.required is not None:
                    db_field.required = request.required
                if request.readonly is not None:
                    db_field.readonly = request.readonly
                if request.hidden is not None:
                    db_field.hidden = request.hidden
                if request.validation is not None:
                    db_field.validation_rules = request.validation.rules
                    db_field.validation_messages = request.validation.messages
                if request.options is not None:
                    db_field.options_config = [opt.dict() for opt in request.options]
                if request.gridSpan is not None:
                    db_field.grid_span = request.gridSpan
                
                db_field.updated_at = datetime.now()
                db.commit()
                
                # 构建响应
                field = FormField(
                    id=db_field.field_id,
                    entityField=db_field.entity_field,
                    displayType=db_field.display_type.value,
                    label=db_field.label,
                    placeholder=db_field.placeholder,
                    required=db_field.required,
                    readonly=db_field.readonly,
                    hidden=db_field.hidden,
                    validation={
                        "rules": db_field.validation_rules or [],
                        "messages": db_field.validation_messages or {}
                    } if db_field.validation_rules or db_field.validation_messages else None,
                    options=db_field.options_config or None,
                    gridSpan=db_field.grid_span,
                    orderIndex=db_field.order_index,
                    created_at=format_datetime(db_field.created_at),
                    updated_at=format_datetime(db_field.updated_at)
                )
                
                return {
                    "success": True,
                    "data": {"field": field.dict()}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新表单字段失败: {str(e)}"
            }
    
    def delete_form_field(self, form_id: str, field_id: str) -> Dict[str, Any]:
        """删除表单字段"""
        try:
            with get_db_session() as db:
                # 查询字段
                db_field = db.query(FormFieldDBModel).filter(
                    FormFieldDBModel.form_id == form_id,
                    FormFieldDBModel.field_id == field_id
                ).first()
                
                if not db_field:
                    return {
                        "success": False,
                        "error": "field_not_found",
                        "details": "表单字段不存在"
                    }
                
                field_label = db_field.label
                section_id = db_field.section_id
                
                # 删除字段
                db.delete(db_field)
                
                # 更新表单字段计数
                db_form = db.query(FormDBModel).filter(FormDBModel.id == form_id).first()
                if db_form:
                    db_form.field_count -= 1
                
                # 更新分组字段计数
                if section_id:
                    db_section = db.query(FormSectionDBModel).filter(FormSectionDBModel.id == section_id).first()
                    if db_section:
                        db_section.field_count -= 1
                
                db.commit()
                
                return {
                    "success": True,
                    "data": {"name": field_label}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除表单字段失败: {str(e)}"
            }


# 全局表单字段服务实例
form_field_service = FormFieldService()
