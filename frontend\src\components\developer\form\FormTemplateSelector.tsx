/**
 * 表单模板选择器组件
 */

import React, { useState } from 'react';
import {
  Modal,
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Tag,
  Form,
  Input,
  Select,
  message,
  Empty,
  Divider
} from 'antd';
import {
  TableOutlined,
  StarOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  FormOutlined
} from '@ant-design/icons';
import type { FormTemplate } from '../../../types/developer/form';
import type { Entity } from '../../../types/developer/entity';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface FormTemplateSelectorProps {
  templates: FormTemplate[];
  entities: Entity[];
  visible: boolean;
  onConfirm: (template: FormTemplate, formData: any) => void;
  onCancel: () => void;
}

const FormTemplateSelector: React.FC<FormTemplateSelectorProps> = ({
  templates,
  entities,
  visible,
  onConfirm,
  onCancel
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate | null>(null);
  const [step, setStep] = useState<'select' | 'config'>('select');
  const [form] = Form.useForm();

  // 重置状态
  const handleReset = () => {
    setSelectedTemplate(null);
    setStep('select');
    form.resetFields();
  };

  // 选择模板
  const handleSelectTemplate = (template: FormTemplate) => {
    setSelectedTemplate(template);
    setStep('config');
    
    // 设置默认值
    form.setFieldsValue({
      name: `${template.name} - 副本`,
      entity: template.entity_type,
      description: template.description
    });
  };

  // 确认创建
  const handleConfirm = async () => {
    if (!selectedTemplate) return;

    try {
      const values = await form.validateFields();
      onConfirm(selectedTemplate, values);
      handleReset();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 取消操作
  const handleCancel = () => {
    handleReset();
    onCancel();
  };

  // 返回模板选择
  const handleBack = () => {
    setStep('select');
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'green';
      case 'medium':
        return 'orange';
      case 'hard':
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取难度文本
  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return '简单';
      case 'medium':
        return '中等';
      case 'hard':
        return '困难';
      default:
        return '未知';
    }
  };

  // 渲染模板卡片
  const renderTemplateCard = (template: FormTemplate) => (
    <Card
      key={template.id}
      hoverable
      className="template-card"
      cover={
        template.preview_image ? (
          <img
            alt={template.name}
            src={template.preview_image}
            style={{ height: 120, objectFit: 'cover' }}
          />
        ) : (
          <div
            style={{
              height: 120,
              background: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <FormOutlined style={{ fontSize: 32, color: '#ccc' }} />
          </div>
        )
      }
      actions={[
        <Button
          key="select"
          type="primary"
          icon={<CheckOutlined />}
          onClick={() => handleSelectTemplate(template)}
        >
          选择模板
        </Button>
      ]}
    >
      <Card.Meta
        title={
          <Space>
            <span>{template.name}</span>
            <Tag color={getDifficultyColor(template.difficulty)}>
              {getDifficultyText(template.difficulty)}
            </Tag>
          </Space>
        }
        description={
          <div>
            <Paragraph ellipsis={{ rows: 2 }}>
              {template.description}
            </Paragraph>
            <div className="template-meta">
              <Space size="small">
                <Tag icon={<TableOutlined />}>{template.category}</Tag>
                <Tag icon={<ClockCircleOutlined />}>
                  {template.estimatedTime}分钟
                </Tag>
              </Space>
            </div>
            <div className="template-tags">
              {template.tags.map(tag => (
                <Tag key={tag}>
                  {tag}
                </Tag>
              ))}
            </div>
          </div>
        }
      />
    </Card>
  );

  // 渲染模板选择步骤
  const renderSelectStep = () => (
    <div className="template-select">
      <div className="select-header">
        <Title level={4}>选择表单模板</Title>
        <Text type="secondary">
          从预设模板快速创建表单，节省配置时间
        </Text>
      </div>

      {templates.length > 0 ? (
        <Row gutter={[16, 16]} className="templates-grid">
          {templates.map(template => (
            <Col key={template.id} xs={24} sm={12} lg={8}>
              {renderTemplateCard(template)}
            </Col>
          ))}
        </Row>
      ) : (
        <Empty
          description="暂无可用模板"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}
    </div>
  );

  // 渲染配置步骤
  const renderConfigStep = () => {
    if (!selectedTemplate) return null;

    return (
      <div className="template-config">
        <div className="config-header">
          <Title level={4}>配置表单信息</Title>
          <Text type="secondary">
            基于模板 "{selectedTemplate.name}" 创建新表单
          </Text>
        </div>

        <Card title="模板信息" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>模板名称:</Text>
              <br />
              <Text>{selectedTemplate.name}</Text>
            </Col>
            <Col span={12}>
              <Text strong>分类:</Text>
              <br />
              <Text>{selectedTemplate.category}</Text>
            </Col>
          </Row>
          <Divider />
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>难度:</Text>
              <br />
              <Tag color={getDifficultyColor(selectedTemplate.difficulty)}>
                {getDifficultyText(selectedTemplate.difficulty)}
              </Tag>
            </Col>
            <Col span={12}>
              <Text strong>预估时间:</Text>
              <br />
              <Text>{selectedTemplate.estimatedTime} 分钟</Text>
            </Col>
          </Row>
          <Divider />
          <div>
            <Text strong>描述:</Text>
            <br />
            <Text>{selectedTemplate.description}</Text>
          </div>
        </Card>

        <Card title="表单配置">
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="表单名称"
                  rules={[{ required: true, message: '请输入表单名称' }]}
                >
                  <Input placeholder="请输入表单名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="entity"
                  label="关联实体"
                  rules={[{ required: true, message: '请选择关联实体' }]}
                >
                  <Select placeholder="请选择关联实体">
                    {entities.map(entity => (
                      <Option key={entity.id} value={entity.name}>
                        {entity.displayName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item
              name="description"
              label="表单描述"
            >
              <TextArea rows={3} placeholder="请输入表单描述" />
            </Form.Item>
          </Form>
        </Card>
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <TableOutlined />
          <span>
            {step === 'select' ? '选择表单模板' : '配置表单信息'}
          </span>
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        ...(step === 'config' ? [
          <Button key="back" onClick={handleBack}>
            返回
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirm}>
            创建表单
          </Button>
        ] : [])
      ]}
    >
      {step === 'select' ? renderSelectStep() : renderConfigStep()}

      <style>{`
        .template-select .select-header {
          margin-bottom: 24px;
          text-align: center;
        }

        .template-select .templates-grid .template-card .template-meta {
          margin: 8px 0;
        }

        .template-select .templates-grid .template-card .template-tags {
          margin-top: 8px;
        }

        .template-config .config-header {
          margin-bottom: 24px;
          text-align: center;
        }
      `}</style>
    </Modal>
  );
};

export default FormTemplateSelector;
