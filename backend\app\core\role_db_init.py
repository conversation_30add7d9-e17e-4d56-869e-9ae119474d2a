"""
角色管理数据库初始化脚本
动态创建角色相关的数据库表和初始化数据
"""
from sqlalchemy import create_engine
from app.core.database import get_db_session, engine
from app.models.role import create_role_tables, init_default_permissions


def init_role_database():
    """
    初始化角色管理数据库
    动态创建表结构和初始化数据
    """
    print("🚀 开始初始化角色管理数据库...")
    
    try:
        # 1. 动态创建角色相关的数据库表
        print("1️⃣ 创建角色管理数据库表...")
        if create_role_tables(engine):
            print("✅ 角色管理数据库表创建成功")
        else:
            print("❌ 角色管理数据库表创建失败")
            return False
        
        # 2. 初始化默认权限数据
        print("2️⃣ 初始化默认权限数据...")
        with get_db_session() as db:
            if init_default_permissions(db):
                print("✅ 默认权限数据初始化成功")
            else:
                print("❌ 默认权限数据初始化失败")
                return False
        
        # 3. 创建默认角色
        print("3️⃣ 创建默认角色...")
        if create_default_roles():
            print("✅ 默认角色创建成功")
        else:
            print("❌ 默认角色创建失败")
            return False
        
        print("🎉 角色管理数据库初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 角色管理数据库初始化失败: {e}")
        return False


def create_default_roles():
    """创建默认角色"""
    try:
        from app.services.role_service import RoleService
        from app.schemas.role import RoleCreateRequest, RoleStatus
        
        role_service = RoleService()
        
        # 检查是否已有角色数据
        with get_db_session() as db:
            from app.models.role import RoleDBModel
            existing_count = db.query(RoleDBModel).count()
            if existing_count > 0:
                print(f"✅ 角色数据已存在 ({existing_count} 个)")
                return True
        
        # 默认角色配置
        default_roles = [
            {
                "name": "超级管理员",
                "code": "super_admin",
                "level": 10,
                "description": "系统超级管理员，拥有所有权限",
                "status": RoleStatus.ACTIVE,
                "permissions": [
                    "products:*", "orders:*", "customers:*", "users:*", "roles:*",
                    "reports:*", "analytics:*", "settings:*"
                ],
                "metadata": {
                    "department": "system",
                    "is_system_role": True,
                    "can_manage_all": True
                }
            },
            {
                "name": "系统管理员",
                "code": "system_admin",
                "level": 8,
                "description": "系统管理员，负责系统配置和用户管理",
                "status": RoleStatus.ACTIVE,
                "permissions": [
                    "users:read", "users:create", "users:update",
                    "roles:read", "roles:create", "roles:update",
                    "settings:read", "settings:update",
                    "reports:read", "analytics:read"
                ],
                "metadata": {
                    "department": "it",
                    "can_manage_users": True,
                    "can_manage_roles": True
                }
            },
            {
                "name": "销售经理",
                "code": "sales_manager",
                "level": 6,
                "description": "销售经理，负责销售团队管理和业务决策",
                "status": RoleStatus.ACTIVE,
                "permissions": [
                    "products:read", "products:create", "products:update",
                    "orders:*", "customers:*",
                    "reports:sales", "analytics:read"
                ],
                "metadata": {
                    "department": "sales",
                    "can_approve_discount": True,
                    "max_discount_rate": 0.2
                }
            },
            {
                "name": "客服专员",
                "code": "customer_service",
                "level": 3,
                "description": "客服专员，处理客户咨询和售后服务",
                "status": RoleStatus.ACTIVE,
                "permissions": [
                    "products:read", "orders:read", "orders:update",
                    "customers:read", "customers:update"
                ],
                "metadata": {
                    "department": "service",
                    "can_handle_complaints": True,
                    "max_refund_amount": 1000
                }
            },
            {
                "name": "普通用户",
                "code": "regular_user",
                "level": 1,
                "description": "普通用户，基础访问权限",
                "status": RoleStatus.ACTIVE,
                "permissions": [
                    "products:read", "orders:read"
                ],
                "metadata": {
                    "department": "general",
                    "is_default_role": True
                }
            }
        ]
        
        # 创建默认角色
        created_count = 0
        for role_data in default_roles:
            request = RoleCreateRequest(**role_data)
            result = role_service.create_role(request, "system")
            
            if result["success"]:
                created_count += 1
                print(f"✅ 创建角色: {role_data['name']}")
            else:
                print(f"❌ 创建角色失败: {role_data['name']} - {result['details']}")
        
        print(f"✅ 成功创建 {created_count}/{len(default_roles)} 个默认角色")
        return created_count > 0
        
    except Exception as e:
        print(f"❌ 创建默认角色失败: {e}")
        return False


def check_role_database():
    """检查角色数据库状态"""
    try:
        print("🔍 检查角色数据库状态...")
        
        with get_db_session() as db:
            from app.models.role import RoleDBModel, PermissionDBModel
            
            # 检查角色数量
            role_count = db.query(RoleDBModel).count()
            print(f"📊 角色数量: {role_count}")
            
            # 检查权限数量
            permission_count = db.query(PermissionDBModel).count()
            print(f"📊 权限数量: {permission_count}")
            
            # 显示角色列表
            if role_count > 0:
                print("📋 角色列表:")
                roles = db.query(RoleDBModel).all()
                for role in roles:
                    print(f"  • {role.name} ({role.code}) - 级别{role.level} - {role.status}")
            
            # 显示权限统计
            if permission_count > 0:
                print("📋 权限统计:")
                permissions = db.query(PermissionDBModel).all()
                resources = {}
                for perm in permissions:
                    resources[perm.resource] = resources.get(perm.resource, 0) + 1
                
                for resource, count in resources.items():
                    print(f"  • {resource}: {count}个权限")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查角色数据库状态失败: {e}")
        return False


if __name__ == "__main__":
    # 直接运行此脚本进行数据库初始化
    success = init_role_database()
    if success:
        check_role_database()
        print("🎉 角色管理数据库初始化完成！")
    else:
        print("❌ 角色管理数据库初始化失败！")
        exit(1)
