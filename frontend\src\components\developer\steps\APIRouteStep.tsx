/**
 * API路由管理步骤组件 - Apple风格设计
 */

import React, { useState, useEffect } from 'react';
import {
  Button,
  Card,
  Table,
  Form,
  Input,
  Select,
  Modal,
  Space,
  Tag,
  Tooltip,
  message,
  Row,
  Col,
  Switch,
  Badge,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  StopOutlined,
  Bar<PERSON><PERSON>Outlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { routeAPI } from '../../../services/developer/routeAPI';
import type {
  APIRoute,
  APIRouteCreateRequest,
  HandlerType,
  HttpMethod,
  RouteQueryParams
} from '../../../types/developer/route';
import { HandlerTypeConfigs } from '../../../types/developer/route';
import './APIRouteStep.css';

const { Option } = Select;
const { TextArea } = Input;

interface APIRouteStepProps {
  onNext?: () => void;
  onPrev?: () => void;
}

const APIRouteStep: React.FC<APIRouteStepProps> = ({ onNext, onPrev }) => {
  const [routes, setRoutes] = useState<APIRoute[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRoute, setEditingRoute] = useState<APIRoute | null>(null);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [testingRoute, setTestingRoute] = useState<APIRoute | null>(null);
  const [form] = Form.useForm();
  const [testForm] = Form.useForm();
  const [queryParams, setQueryParams] = useState<RouteQueryParams>({});
  const [summary, setSummary] = useState({
    total_routes: 0,
    active_routes: 0,
    inactive_routes: 0,
    total_calls: 0,
    avg_response_time: 0
  });

  // HTTP方法选项
  const httpMethods: HttpMethod[] = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

  // 加载API路由列表
  const loadRoutes = async () => {
    setLoading(true);
    try {
      const response = await routeAPI.getRoutes(queryParams);
      setRoutes(response.data.routes || []);
      setSummary(response.data.summary || summary);
    } catch (error: any) {
      const errorMsg = error.message || '加载API路由失败';
      message.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRoutes();
  }, [queryParams]);

  // 获取HTTP方法颜色
  const getMethodColor = (method: HttpMethod) => {
    const colors: Record<HttpMethod, string> = {
      GET: 'green',
      POST: 'blue',
      PUT: 'orange',
      DELETE: 'red',
      PATCH: 'purple'
    };
    return colors[method] || 'default';
  };

  // 表格列定义
  const columns: ColumnsType<APIRoute> = [
    {
      title: 'API信息',
      key: 'api_info',
      width: 300,
      render: (_, record: APIRoute) => (
        <div className="api-info-cell">
          <div className="api-name">{record.name}</div>
          <div className="api-id">{record.api_id}</div>
          <div className="api-description">{record.description}</div>
        </div>
      )
    },
    {
      title: '端点',
      key: 'endpoint',
      width: 250,
      render: (_, record: APIRoute) => (
        <div className="endpoint-cell">
          <div className="endpoint-method">
            <Tag color={getMethodColor(record.method)}>{record.method}</Tag>
          </div>
          <code className="endpoint-path">{record.endpoint}</code>
        </div>
      )
    },
    {
      title: '处理器',
      dataIndex: ['handler', 'type'],
      key: 'handler_type',
      width: 150,
      render: (type: HandlerType) => (
        <Tag color="blue">
          {HandlerTypeConfigs[type]?.label || type}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge
          status={status === 'active' ? 'success' : 'error'}
          text={status === 'active' ? '活跃' : '停用'}
        />
      )
    },
    {
      title: '认证',
      dataIndex: 'auth_required',
      key: 'auth_required',
      width: 80,
      render: (required: boolean) => (
        <Tag color={required ? 'orange' : 'default'}>
          {required ? '需要' : '无需'}
        </Tag>
      )
    },
    {
      title: '统计信息',
      key: 'statistics',
      width: 150,
      render: (_, record: APIRoute) => (
        <div className="statistics-cell">
          <div className="stat-item">
            <span className="stat-label">调用次数:</span>
            <span className="stat-value">{record.call_count || 0}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">响应时间:</span>
            <span className="stat-value">{record.avg_response_time || 0}ms</span>
          </div>
          {record.last_called && (
            <div className="stat-item">
              <span className="stat-label">最后调用:</span>
              <span className="stat-value">
                {new Date(record.last_called).toLocaleString()}
              </span>
            </div>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: APIRoute) => (
        <Space size="small">
          <Tooltip title="测试API">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTestRoute(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditRoute(record)}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? '停用' : '激活'}>
            <Button
              type="text"
              icon={record.status === 'active' ? <StopOutlined /> : <CheckCircleOutlined />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteRoute(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 处理创建/编辑API路由
  const handleSaveRoute = async (values: any) => {
    try {

      // 解析参数列表
      let parameters = [];
      if (values.parameters) {
        try {
          parameters = JSON.parse(values.parameters);
        } catch (e) {
          message.error('参数格式错误，请检查JSON格式');
          return;
        }
      }

      // 解析处理器配置
      let handlerConfig = {};
      if (values.handler_config) {
        try {
          handlerConfig = JSON.parse(values.handler_config);
        } catch (e) {
          message.error('处理器配置格式错误，请检查JSON格式');
          return;
        }
      }

      // 解析响应定义
      let responses = {};
      if (values.responses) {
        try {
          responses = JSON.parse(values.responses);
        } catch (e) {
          message.error('响应定义格式错误，请检查JSON格式');
          return;
        }
      }

      const routeData: APIRouteCreateRequest = {
        api_id: values.api_id,
        name: values.name,
        endpoint: values.endpoint,
        method: values.method,
        description: values.description || '',
        auth_required: values.auth_required || false,
        handler: {
          type: values.handler_type,
          config: handlerConfig
        },
        parameters: parameters,
        responses: responses
      };

      if (editingRoute) {
        await routeAPI.updateRoute(editingRoute.id, routeData);
        message.success('API路由更新成功');
      } else {
        await routeAPI.createRoute(routeData);
        message.success('API路由创建成功');
      }

      setModalVisible(false);
      setEditingRoute(null);
      form.resetFields();
      loadRoutes();
    } catch (error: any) {
      const errorMsg = error.response?.data?.message || (editingRoute ? 'API路由更新失败' : 'API路由创建失败');
      message.error(errorMsg);
    }
  };

  // 处理编辑API路由
  const handleEditRoute = (route: APIRoute) => {
    setEditingRoute(route);
    form.setFieldsValue({
      api_id: route.api_id,
      name: route.name,
      endpoint: route.endpoint,
      method: route.method,
      description: route.description,
      auth_required: route.auth_required,
      handler_type: route.handler?.type || '',
      handler_config: route.handler?.config ? JSON.stringify(route.handler.config, null, 2) : '',
      parameters: route.parameters ? JSON.stringify(route.parameters, null, 2) : '',
      responses: route.responses ? JSON.stringify(route.responses, null, 2) : ''
    });
    setModalVisible(true);
  };

  // 处理删除API路由
  const handleDeleteRoute = (route: APIRoute) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除API路由 "${route.name}" 吗？`,
      onOk: async () => {
        try {
          await routeAPI.deleteRoute(route.id);
          message.success('API路由删除成功');
          loadRoutes();
        } catch (error) {
          message.error('API路由删除失败');
        }
      }
    });
  };

  // 处理切换状态
  const handleToggleStatus = async (route: APIRoute) => {
    try {
      if (route.status === 'active') {
        await routeAPI.deactivateRoute(route.id);
        message.success('API路由已停用');
      } else {
        await routeAPI.activateRoute(route.id);
        message.success('API路由已激活');
      }
      loadRoutes();
    } catch (error) {
      message.error('状态切换失败');
    }
  };

  // 处理测试API路由
  const handleTestRoute = (route: APIRoute) => {
    setTestingRoute(route);
    testForm.resetFields();
    setTestModalVisible(true);
  };

  // 处理API测试
  const handleRunTest = async (values: any) => {
    try {
      let testData = {};
      if (values.test_data) {
        testData = JSON.parse(values.test_data);
      }

      await routeAPI.testRoute(testingRoute!.id, testData);
      message.success('API测试成功');
      setTestModalVisible(false);
    } catch (error: any) {
      const errorMsg = error.response?.data?.message || 'API测试失败';
      message.error(errorMsg);
    }
  };

  return (
    <div className="api-route-step">
      {/* 步骤头部 */}
      <div className="step-header">
        <div className="step-title">
          <ApiOutlined className="step-icon" />
          <div>
            <h2>API路由管理</h2>
            <p>配置和管理系统的API接口路由</p>
          </div>
        </div>
        <div className="step-actions">
          <Button
            icon={<ReloadOutlined />}
            onClick={loadRoutes}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setModalVisible(true)}
          >
            添加API路由
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="step-statistics">
        <Row gutter={16}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总路由数"
                value={summary.total_routes}
                prefix={<ApiOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃路由"
                value={summary.active_routes}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总调用次数"
                value={summary.total_calls}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均响应时间"
                value={summary.avg_response_time}
                suffix="ms"
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </div>

      {/* 筛选器 */}
      <div className="step-filters">
        <Card>
          <Row gutter={16}>
            <Col span={6}>
              <Select
                placeholder="筛选状态"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => setQueryParams({ ...queryParams, status: value })}
                value={queryParams.status}
              >
                <Option value="active">活跃</Option>
                <Option value="inactive">停用</Option>
                <Option value="maintenance">维护</Option>
                <Option value="deprecated">已弃用</Option>
              </Select>
            </Col>
            <Col span={6}>
              <Select
                placeholder="筛选方法"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => setQueryParams({ ...queryParams, method: value })}
                value={queryParams.method}
              >
                {httpMethods.map(method => (
                  <Option key={method} value={method}>
                    <Tag color={getMethodColor(method)}>{method}</Tag>
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Input
                placeholder="搜索实体"
                allowClear
                value={queryParams.entity}
                onChange={(e) => setQueryParams({ ...queryParams, entity: e.target.value })}
              />
            </Col>
            <Col span={6}>
              <Button
                onClick={() => {
                  setQueryParams({});
                  loadRoutes();
                }}
              >
                重置筛选
              </Button>
            </Col>
          </Row>
        </Card>
      </div>

      {/* 路由表格 */}
      <div className="step-content">
        <Card>
          <Table
            columns={columns}
            dataSource={routes}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
            scroll={{ x: 1200 }}
          />
        </Card>
      </div>

      {/* 步骤底部 */}
      <div className="step-footer">
        <Space>
          <Button size="large" onClick={onPrev}>
            上一步
          </Button>
          <Button type="primary" size="large" onClick={onNext}>
            下一步
          </Button>
        </Space>
      </div>

      {/* 创建/编辑API路由模态框 */}
      <Modal
        title={editingRoute ? '编辑API路由' : '创建API路由'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingRoute(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
        className="api-route-modal"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveRoute}
          className="api-route-form"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="api_id"
                label="API ID"
                rules={[{ required: true, message: '请输入API ID' }]}
              >
                <Input placeholder="例如: product_list_api" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="API名称"
                rules={[{ required: true, message: '请输入API名称' }]}
              >
                <Input placeholder="例如: 商品列表API" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="method"
                label="HTTP方法"
                rules={[{ required: true, message: '请选择HTTP方法' }]}
              >
                <Select placeholder="选择HTTP方法">
                  {httpMethods.map(method => (
                    <Option key={method} value={method}>
                      <Tag color={getMethodColor(method)}>{method}</Tag>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item
                name="endpoint"
                label="端点路径"
                rules={[{ required: true, message: '请输入端点路径' }]}
              >
                <Input placeholder="例如: /api/products" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={2} placeholder="API功能描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="handler_type"
                label="处理器类型"
                rules={[{ required: true, message: '请选择处理器类型' }]}
              >
                <Select placeholder="选择处理器类型">
                  {Object.entries(HandlerTypeConfigs).map(([key, config]) => (
                    <Option key={key} value={key}>
                      {config.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="auth_required"
                label="需要认证"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="handler_config"
            label="处理器配置 (JSON)"
          >
            <TextArea
              rows={4}
              placeholder='{"entity": "product", "operation": "list", "default_limit": 20, "max_limit": 100}'
            />
          </Form.Item>

          <Form.Item
            name="parameters"
            label="参数定义 (JSON)"
          >
            <TextArea
              rows={6}
              placeholder={`[
  {
    "name": "page",
    "type": "integer",
    "location": "query",
    "required": false,
    "description": "页码，默认为1"
  },
  {
    "name": "limit",
    "type": "integer",
    "location": "query",
    "required": false,
    "description": "每页数量，默认为20"
  }
]`}
            />
          </Form.Item>

          <Form.Item
            name="responses"
            label="响应定义 (JSON)"
          >
            <TextArea
              rows={6}
              placeholder={`{
  "200": {
    "description": "成功返回数据",
    "schema": {
      "type": "object",
      "properties": {
        "code": {"type": "integer"},
        "message": {"type": "string"},
        "data": {"type": "object"}
      }
    }
  },
  "400": {
    "description": "请求参数错误",
    "schema": {"type": "object"}
  }
}`}
            />
          </Form.Item>

          <div className="form-actions">
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingRoute ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* API测试模态框 */}
      <Modal
        title={`测试API: ${testingRoute?.name}`}
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={testForm}
          layout="vertical"
          onFinish={handleRunTest}
        >
          <div className="test-info">
            <Tag color={getMethodColor(testingRoute?.method || 'GET')}>
              {testingRoute?.method}
            </Tag>
            <code>{testingRoute?.endpoint}</code>
          </div>

          <Form.Item
            name="test_data"
            label="测试数据 (JSON)"
          >
            <TextArea
              rows={6}
              placeholder='{"key": "value"}'
            />
          </Form.Item>

          <div className="form-actions">
            <Space>
              <Button onClick={() => setTestModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                运行测试
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default APIRouteStep;
