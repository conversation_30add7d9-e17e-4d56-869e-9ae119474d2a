# 系统健康检查模块 API 文档

## 📋 概述

系统健康检查模块提供系统运行状态监控和健康检查功能，确保AILF系统的稳定运行。

## 🔧 API 端点列表

### 基础健康检查
1. 检查系统健康状态

---

## API 详细文档

### 1. 检查系统健康状态

**GET** `/api/health`

#### 描述
全面检查AILF系统各个组件的健康状态，包括数据库连接、AI服务、文件系统等。

#### 请求参数

**请求头：**
```
Authorization: Bearer <token> (可选)
```

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| detailed | boolean | 否 | 是否返回详细信息，默认false |
| components | string | 否 | 指定检查的组件，逗号分隔 |

#### 支持的组件

| 组件 | 描述 |
|------|------|
| database | 数据库连接状态 |
| ai_service | AI服务连接状态 |
| file_system | 文件系统状态 |
| memory | 内存使用情况 |
| disk | 磁盘使用情况 |
| api_routes | API路由状态 |
| background_tasks | 后台任务状态 |

#### 请求示例
```bash
# 基础健康检查
curl -X GET "http://localhost:5000/api/health"

# 详细健康检查
curl -X GET "http://localhost:5000/api/health?detailed=true"

# 检查特定组件
curl -X GET "http://localhost:5000/api/health?components=database,ai_service&detailed=true"
```

#### 响应格式

**健康状态响应 (200 OK):**
```json
{
  "code": 200,
  "message": "系统健康检查完成",
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-20T12:00:00.000Z",
    "uptime": 86400,
    "version": "2.0.0",
    "environment": "production",
    "components": {
      "database": {
        "status": "healthy",
        "response_time": 15,
        "connection_pool": {
          "active": 5,
          "idle": 15,
          "max": 20
        },
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "ai_service": {
        "status": "healthy",
        "response_time": 234,
        "provider": "alibaba_bailian",
        "model": "qwen-coder-plus",
        "requests_today": 1247,
        "quota_remaining": 8753,
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "file_system": {
        "status": "healthy",
        "disk_usage": {
          "total": "100GB",
          "used": "45GB",
          "free": "55GB",
          "usage_percentage": 45
        },
        "temp_files": 23,
        "generated_files": 156,
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "memory": {
        "status": "healthy",
        "usage": {
          "total": "8GB",
          "used": "3.2GB",
          "free": "4.8GB",
          "usage_percentage": 40
        },
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "api_routes": {
        "status": "healthy",
        "total_routes": 85,
        "active_routes": 83,
        "inactive_routes": 2,
        "avg_response_time": 189,
        "requests_per_minute": 45,
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "background_tasks": {
        "status": "healthy",
        "active_tasks": 3,
        "completed_tasks": 156,
        "failed_tasks": 2,
        "queue_size": 5,
        "last_check": "2024-01-20T12:00:00.000Z"
      }
    },
    "metrics": {
      "cpu_usage": 35.6,
      "memory_usage": 40.0,
      "disk_usage": 45.0,
      "network_io": {
        "bytes_sent": 1048576,
        "bytes_received": 2097152
      },
      "request_rate": 45.2,
      "error_rate": 2.1
    },
    "alerts": [],
    "recommendations": [
      "系统运行正常，建议定期清理临时文件",
      "AI服务配额充足，可以正常使用"
    ]
  }
}
```

**部分异常状态响应 (200 OK):**
```json
{
  "code": 200,
  "message": "系统健康检查完成，发现部分问题",
  "data": {
    "status": "degraded",
    "timestamp": "2024-01-20T12:00:00.000Z",
    "uptime": 86400,
    "version": "2.0.0",
    "environment": "production",
    "components": {
      "database": {
        "status": "healthy",
        "response_time": 15,
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "ai_service": {
        "status": "warning",
        "response_time": 2340,
        "provider": "alibaba_bailian",
        "model": "qwen-coder-plus",
        "requests_today": 1247,
        "quota_remaining": 253,
        "issues": [
          "响应时间较慢",
          "配额即将用完"
        ],
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "file_system": {
        "status": "warning",
        "disk_usage": {
          "total": "100GB",
          "used": "85GB",
          "free": "15GB",
          "usage_percentage": 85
        },
        "issues": [
          "磁盘使用率过高"
        ],
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "memory": {
        "status": "healthy",
        "usage": {
          "usage_percentage": 40
        },
        "last_check": "2024-01-20T12:00:00.000Z"
      }
    },
    "alerts": [
      {
        "level": "warning",
        "component": "ai_service",
        "message": "AI服务响应时间超过阈值",
        "threshold": "1000ms",
        "current_value": "2340ms",
        "timestamp": "2024-01-20T11:58:00.000Z"
      },
      {
        "level": "warning",
        "component": "file_system",
        "message": "磁盘使用率过高",
        "threshold": "80%",
        "current_value": "85%",
        "timestamp": "2024-01-20T11:55:00.000Z"
      }
    ],
    "recommendations": [
      "清理不必要的文件以释放磁盘空间",
      "考虑升级AI服务配额",
      "监控AI服务响应时间，必要时重启服务"
    ]
  }
}
```

**严重异常状态响应 (503 Service Unavailable):**
```json
{
  "code": 503,
  "message": "系统存在严重问题，部分功能不可用",
  "data": {
    "status": "unhealthy",
    "timestamp": "2024-01-20T12:00:00.000Z",
    "uptime": 86400,
    "version": "2.0.0",
    "environment": "production",
    "components": {
      "database": {
        "status": "unhealthy",
        "error": "Connection timeout",
        "last_successful_check": "2024-01-20T11:45:00.000Z",
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "ai_service": {
        "status": "unhealthy",
        "error": "Service unavailable",
        "provider": "alibaba_bailian",
        "last_successful_check": "2024-01-20T11:30:00.000Z",
        "last_check": "2024-01-20T12:00:00.000Z"
      },
      "file_system": {
        "status": "critical",
        "disk_usage": {
          "usage_percentage": 98
        },
        "error": "Disk almost full",
        "last_check": "2024-01-20T12:00:00.000Z"
      }
    },
    "alerts": [
      {
        "level": "critical",
        "component": "database",
        "message": "数据库连接失败",
        "error": "Connection timeout after 30 seconds",
        "timestamp": "2024-01-20T12:00:00.000Z"
      },
      {
        "level": "critical",
        "component": "ai_service",
        "message": "AI服务不可用",
        "error": "HTTP 503 Service Unavailable",
        "timestamp": "2024-01-20T11:58:00.000Z"
      },
      {
        "level": "critical",
        "component": "file_system",
        "message": "磁盘空间严重不足",
        "threshold": "95%",
        "current_value": "98%",
        "timestamp": "2024-01-20T11:55:00.000Z"
      }
    ],
    "affected_features": [
      "数据存储和查询功能",
      "AI命令处理功能",
      "文件上传和代码生成功能"
    ],
    "recommendations": [
      "立即清理磁盘空间",
      "检查数据库服务状态",
      "联系AI服务提供商",
      "考虑系统维护模式"
    ]
  }
}
```

---

## 📝 健康状态等级

### 系统整体状态
| 状态 | 描述 | HTTP状态码 |
|------|------|------------|
| healthy | 所有组件正常运行 | 200 |
| degraded | 部分组件有警告，但系统可用 | 200 |
| unhealthy | 关键组件故障，系统功能受限 | 503 |
| maintenance | 系统维护模式 | 503 |

### 组件状态
| 状态 | 描述 | 影响 |
|------|------|------|
| healthy | 组件正常运行 | 无影响 |
| warning | 组件有警告但可用 | 性能可能下降 |
| unhealthy | 组件故障 | 相关功能不可用 |
| critical | 组件严重故障 | 系统功能严重受限 |
| unknown | 无法检查组件状态 | 状态不明 |

---

## 📝 监控指标说明

### 性能指标
- **响应时间**：各组件的平均响应时间
- **CPU使用率**：系统CPU使用百分比
- **内存使用率**：系统内存使用百分比
- **磁盘使用率**：磁盘空间使用百分比
- **网络IO**：网络输入输出流量

### 业务指标
- **请求速率**：每分钟API请求数
- **错误率**：请求错误百分比
- **活跃路由数**：正常工作的API路由数量
- **后台任务**：后台任务执行情况

### 资源指标
- **数据库连接池**：数据库连接使用情况
- **AI服务配额**：AI服务使用配额和剩余量
- **文件系统**：临时文件和生成文件数量

---

## 📝 告警阈值

### 默认阈值设置
```json
{
  "response_time": {
    "warning": 1000,
    "critical": 5000
  },
  "cpu_usage": {
    "warning": 70,
    "critical": 90
  },
  "memory_usage": {
    "warning": 80,
    "critical": 95
  },
  "disk_usage": {
    "warning": 80,
    "critical": 95
  },
  "error_rate": {
    "warning": 5,
    "critical": 10
  },
  "ai_quota_remaining": {
    "warning": 1000,
    "critical": 100
  }
}
```

---

## 📝 故障排除指南

### 常见问题及解决方案

#### 数据库连接问题
- **症状**：database状态为unhealthy
- **可能原因**：数据库服务停止、网络问题、连接池耗尽
- **解决方案**：
  1. 检查数据库服务状态
  2. 重启数据库连接池
  3. 检查网络连接

#### AI服务不可用
- **症状**：ai_service状态为unhealthy
- **可能原因**：服务提供商故障、配额用完、网络问题
- **解决方案**：
  1. 检查服务提供商状态
  2. 确认配额余量
  3. 重试连接

#### 磁盘空间不足
- **症状**：file_system状态为critical
- **可能原因**：临时文件过多、生成文件未清理
- **解决方案**：
  1. 清理临时文件
  2. 删除过期的生成文件
  3. 扩展磁盘空间

#### 内存使用过高
- **症状**：memory使用率超过阈值
- **可能原因**：内存泄漏、大量并发请求
- **解决方案**：
  1. 重启应用服务
  2. 检查内存泄漏
  3. 优化内存使用

---

## 📝 监控最佳实践

### 定期检查
- **频率**：每5分钟进行一次基础健康检查
- **详细检查**：每小时进行一次详细检查
- **组件检查**：根据组件重要性调整检查频率

### 告警设置
- **即时告警**：critical级别问题立即通知
- **汇总告警**：warning级别问题定期汇总通知
- **趋势监控**：监控指标变化趋势

### 日志记录
- **健康检查日志**：记录所有健康检查结果
- **告警日志**：记录所有告警事件
- **恢复日志**：记录问题恢复过程

---

## 📝 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| health_check_failed | 500 | 健康检查执行失败 | 检查系统基础服务状态 |
| component_not_found | 404 | 指定的组件不存在 | 检查组件名称是否正确 |
| insufficient_permissions | 403 | 权限不足 | 使用管理员权限访问 |
| service_unavailable | 503 | 服务不可用 | 系统正在维护或存在严重问题 |
