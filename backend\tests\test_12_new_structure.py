#!/usr/bin/env python3
"""
第十二部分：新文件结构测试
测试重构后的AI服务结构，确保amis schema能正确调用API并考虑用户权限
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_new_file_structure():
    """测试新的文件结构"""
    print("=== 测试新文件结构 ===")
    
    try:
        # 测试AI代码服务
        from app.services.ai_code_service import ai_code_service
        print("✅ AI代码服务导入成功")
        
        # 测试AI AMIS服务
        from app.services.ai_amis_service import ai_amis_service
        print("✅ AI AMIS服务导入成功")
        
        # 测试用户权限服务
        from app.services.user_permission_service import user_permission_service
        print("✅ 用户权限服务导入成功")
        
        # 测试AI服务协调器
        from app.services.ai_service import ai_service
        print("✅ AI服务协调器导入成功")
        
        # 测试核心AI组件（应该保持不变）
        from app.core.ai.bailian_client import bailian_client
        from app.core.ai.amis_knowledge import amis_knowledge
        from app.core.ai.amis_validator import amis_validator
        print("✅ 核心AI组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件结构测试失败: {str(e)}")
        return False

def test_permission_service():
    """测试用户权限服务"""
    print("\n=== 测试用户权限服务 ===")
    
    try:
        from app.services.user_permission_service import user_permission_service
        
        # 测试默认权限获取
        guest_apis = user_permission_service._get_guest_apis()
        assert isinstance(guest_apis, list), "访客API应该是列表"
        assert len(guest_apis) > 0, "访客应该有基础API权限"
        print(f"✅ 访客API权限: {len(guest_apis)} 个")
        
        # 测试不同角色级别的API权限
        user_apis = user_permission_service._get_apis_by_role_level(1)
        admin_apis = user_permission_service._get_apis_by_role_level(5)
        super_admin_apis = user_permission_service._get_apis_by_role_level(9)
        
        assert len(admin_apis) > len(user_apis), "管理员应该比普通用户有更多API权限"
        assert len(super_admin_apis) > len(admin_apis), "超级管理员应该比管理员有更多API权限"
        print(f"✅ 权限级别测试通过: 用户({len(user_apis)}) < 管理员({len(admin_apis)}) < 超管({len(super_admin_apis)})")
        
        # 测试默认权限
        user_perms = user_permission_service._get_default_permissions_by_level(1)
        admin_perms = user_permission_service._get_default_permissions_by_level(5)
        super_perms = user_permission_service._get_default_permissions_by_level(9)
        
        assert "user:read" in user_perms, "普通用户应该有读取权限"
        assert "user:*" in admin_perms, "管理员应该有用户管理权限"
        assert "*:*" in super_perms, "超级管理员应该有全部权限"
        print("✅ 权限分配测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 权限服务测试失败: {str(e)}")
        return False

def test_amis_service():
    """测试AI AMIS服务"""
    print("\n=== 测试AI AMIS服务 ===")
    
    try:
        from app.services.ai_amis_service import ai_amis_service
        
        # 测试权限检查
        intent = {"intent_type": "data_query", "entity": "users", "action": "list"}
        user_permissions = ["user:read", "users:list"]
        accessible_apis = [
            {"resource": "users", "action": "list", "endpoint": "/api/users", "method": "GET"}
        ]
        
        has_permission = ai_amis_service._check_intent_permission(intent, user_permissions, accessible_apis)
        assert has_permission, "用户应该有查询用户的权限"
        print("✅ 权限检查测试通过")
        
        # 测试实体字段获取
        fields = ai_amis_service._get_entity_fields("users")
        assert isinstance(fields, list), "实体字段应该是列表"
        assert len(fields) > 0, "应该有默认字段"
        print(f"✅ 实体字段获取测试通过: {len(fields)} 个字段")
        
        # 测试API认证确保
        test_schema = {
            "type": "crud",
            "api": "/api/users"
        }
        ai_amis_service._ensure_api_authentication(test_schema)
        
        api_config = test_schema.get("api")
        assert isinstance(api_config, dict), "API配置应该转换为对象"
        assert "headers" in api_config, "应该包含头部信息"
        assert "Authorization" in api_config["headers"], "应该包含认证头部"
        print("✅ API认证确保测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ AMIS服务测试失败: {str(e)}")
        return False

def test_code_service():
    """测试AI代码服务"""
    print("\n=== 测试AI代码服务 ===")
    
    try:
        from app.services.ai_code_service import ai_code_service
        
        # 测试代码解析
        test_code = '''
import os
from typing import Dict

class TestClass:
    def __init__(self):
        pass
    
    def test_method(self):
        return "test"

def test_function():
    return True
'''
        
        parsed = ai_code_service._parse_generated_code(test_code)
        
        assert "imports" in parsed, "应该解析出导入语句"
        assert "classes" in parsed, "应该解析出类定义"
        assert "functions" in parsed, "应该解析出函数定义"
        
        assert len(parsed["imports"]) >= 2, "应该解析出至少2个导入语句"
        assert "TestClass" in parsed["classes"], "应该解析出TestClass"
        assert "test_function" in parsed["functions"], "应该解析出test_function"
        
        print("✅ 代码解析测试通过")
        
        # 测试健康状态
        health = ai_code_service.get_health_status()
        assert "service" in health, "健康状态应该包含服务信息"
        assert health["service"] == "ai_code_service", "服务名称应该正确"
        print("✅ 健康状态测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 代码服务测试失败: {str(e)}")
        return False

def test_service_coordination():
    """测试服务协调"""
    print("\n=== 测试服务协调 ===")
    
    try:
        from app.services.ai_service import ai_service
        
        # 测试备用意图分析
        intent1 = ai_service._fallback_intent_analysis("显示用户列表")
        assert intent1["intent_type"] == "data_query", "应该识别为数据查询"
        assert intent1["entity"] == "users", "应该识别为用户实体"
        print("✅ 用户列表意图分析正确")
        
        intent2 = ai_service._fallback_intent_analysis("创建新角色")
        assert intent2["intent_type"] == "data_create", f"应该识别为数据创建，实际为: {intent2['intent_type']}"
        print("✅ 创建意图分析正确")
        
        intent3 = ai_service._fallback_intent_analysis("编辑用户信息")
        assert intent3["intent_type"] == "data_update", "应该识别为数据更新"
        print("✅ 更新意图分析正确")
        
        # 测试实体信息获取（同步版本）
        import asyncio
        entities = asyncio.run(ai_service._get_entities_for_intent({"entity": "users"}))
        assert isinstance(entities, list), "实体信息应该是列表"
        if entities:
            user_entity = entities[0]
            assert user_entity["name"] == "users", "实体名称应该正确"
            assert "fields" in user_entity, "应该包含字段信息"
            print(f"✅ 用户实体信息获取正确: {len(user_entity['fields'])} 个字段")
        
        # 测试健康状态
        health = ai_service.get_health_status()
        assert "service" in health, "健康状态应该包含服务信息"
        assert "components" in health, "应该包含组件状态"
        print("✅ 服务协调健康状态正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务协调测试失败: {str(e)}")
        return False

def test_api_integration():
    """测试API集成"""
    print("\n=== 测试API集成 ===")
    
    try:
        # 测试amis知识库的API配置生成
        from app.core.ai.amis_knowledge import amis_knowledge
        
        api_config = amis_knowledge.generate_api_config("GET", "/api/users")
        
        assert "method" in api_config, "API配置应该包含方法"
        assert "url" in api_config, "API配置应该包含URL"
        assert "headers" in api_config, "API配置应该包含头部"
        assert "Authorization" in api_config["headers"], "应该自动添加认证头部"
        assert api_config["headers"]["Authorization"] == "Bearer ${ls:access_token}", "认证头部格式应该正确"
        
        print("✅ API配置生成正确")
        
        # 测试CRUD权限生成
        accessible_apis = [
            {"resource": "users", "action": "list", "endpoint": "/api/users", "method": "GET"},
            {"resource": "users", "action": "create", "endpoint": "/api/users", "method": "POST"}
        ]
        entity_fields = [
            {"name": "id", "label": "ID", "type": "integer", "primary": True, "readonly": True},
            {"name": "username", "label": "用户名", "type": "string", "required": True}
        ]
        
        crud_schema = amis_knowledge.generate_crud_with_permissions("users", accessible_apis, entity_fields)
        
        assert crud_schema["type"] == "crud", "应该生成CRUD组件"
        assert "api" in crud_schema, "CRUD应该包含API配置"
        assert "columns" in crud_schema, "CRUD应该包含列配置"
        
        # 检查API配置
        api_config = crud_schema["api"]
        assert isinstance(api_config, dict), "API配置应该是对象"
        assert "headers" in api_config, "API配置应该包含认证头部"
        
        print("✅ CRUD权限生成正确")
        
        return True
        
    except Exception as e:
        print(f"❌ API集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始第十二部分新文件结构测试...")
    print("=" * 60)
    
    tests = [
        ("新文件结构", test_new_file_structure),
        ("用户权限服务", test_permission_service),
        ("AI AMIS服务", test_amis_service),
        ("AI代码服务", test_code_service),
        ("服务协调", test_service_coordination),
        ("API集成", test_api_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！新文件结构功能正常")
        print("\n📋 新文件结构总结:")
        print("✅ AI服务按功能分离：代码生成、AMIS生成、权限管理")
        print("✅ 使用统一的bailian_client进行AI调用")
        print("✅ AMIS生成的schema能正确调用已有API")
        print("✅ 后端能准确告诉AI当前用户权限和可访问API")
        print("✅ 所有API调用自动包含正确的认证头部")
        print("✅ 基于用户权限的界面生成和功能控制")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
