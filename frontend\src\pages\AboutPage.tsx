/**
 * AILF About Page - 基于Apple Siri设计理念的关于页面
 * 设计理念：极简主义、情境化展示、对话式交互、视觉层次、渐进式展示
 */

import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import '../components/SiriAboutPage.css';

const AboutPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentScenario, setCurrentScenario] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [visibleSections, setVisibleSections] = useState<Set<string>>(new Set());

  // Refs for sections
  const heroRef = useRef<HTMLElement>(null);
  const scenariosRef = useRef<HTMLElement>(null);
  const workflowRef = useRef<HTMLElement>(null);
  const featuresRef = useRef<HTMLElement>(null);
  const privacyRef = useRef<HTMLElement>(null);
  const techRef = useRef<HTMLElement>(null);
  const ctaRef = useRef<HTMLElement>(null);

  const handleGoHome = () => {
    navigate('/');
  };

  // 页面加载动画
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // 优化的Intersection Observer - 针对独立容器
  useEffect(() => {
    const container = document.querySelector('.about-page-container');
    if (!container) return;

    const observerOptions = {
      root: container, // 使用独立容器作为root
      threshold: 0.2,
      rootMargin: '0px 0px -20% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const sectionId = entry.target.getAttribute('data-section');
        if (sectionId && entry.isIntersecting) {
          setVisibleSections(prev => {
            const newSet = new Set(prev);
            newSet.add(sectionId);
            return newSet;
          });
        }
      });
    }, observerOptions);

    // Observe all sections
    const sections = [
      { ref: heroRef, id: 'hero' },
      { ref: scenariosRef, id: 'scenarios' },
      { ref: workflowRef, id: 'workflow' },
      { ref: featuresRef, id: 'features' },
      { ref: privacyRef, id: 'privacy' },
      { ref: techRef, id: 'tech' },
      { ref: ctaRef, id: 'cta' }
    ];

    sections.forEach(({ ref, id }) => {
      if (ref.current) {
        ref.current.setAttribute('data-section', id);
        observer.observe(ref.current);
      }
    });

    // 备用滚动监听器 - 监听容器滚动
    const handleScroll = () => {
      sections.forEach(({ ref, id }) => {
        if (ref.current && container) {
          const rect = ref.current.getBoundingClientRect();
          const containerRect = container.getBoundingClientRect();
          const isVisible = rect.top < containerRect.bottom * 0.8 && rect.bottom > containerRect.top;

          if (isVisible) {
            setVisibleSections(prev => {
              const newSet = new Set(prev);
              newSet.add(id);
              return newSet;
            });
          }
        }
      });
    };

    // 监听容器滚动
    container.addEventListener('scroll', handleScroll);

    // 立即检查一次
    setTimeout(handleScroll, 500);

    return () => {
      observer.disconnect();
      container.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // AILF革命性"无固定前端"理念展示
  const scenarios = [
    {
      id: 'no-fixed-frontend',
      title: '无固定前端',
      subtitle: 'AILF革命性理念',
      description: 'AILF没有预设的固定界面。每次交互都根据用户的具体需求，动态生成完全个性化的专属前端界面。',
      quote: '"我需要一个适合我业务的用户管理界面"',
      visual: '🎤→✨',
      color: '#007AFF',
      highlight: true
    },
    {
      id: 'voice-understanding',
      title: '语音理解生成',
      subtitle: '自然语言驱动界面',
      description: 'AI深度理解用户的语音描述，识别业务意图和具体需求，然后实时创造出最符合用户期望的界面布局和功能。',
      quote: '"做一个销售数据分析面板，要有图表"',
      visual: '🧠💬',
      color: '#5856D6',
      highlight: false
    },
    {
      id: 'dynamic-creation',
      title: '动态界面创造',
      subtitle: '每次都是全新体验',
      description: '不是从模板库选择，而是AI根据当前对话上下文和用户权限，创造性地生成独一无二的界面解决方案。',
      quote: '"这次要简洁一些，重点突出数据趋势"',
      visual: '⚡🎨',
      color: '#34C759',
      highlight: false
    },
    {
      id: 'developer-framework',
      title: '开发者配置框架',
      subtitle: '配置API，AI创造界面',
      description: '开发者只需配置业务API和权限规则，AI自动将这些能力包装成用户友好的界面，无需手写前端代码。',
      quote: '"基于我配置的订单API生成管理界面"',
      visual: '🔧🌟',
      color: '#FF9500',
      highlight: false
    }
  ];

  // 自动切换场景
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentScenario((prev) => (prev + 1) % scenarios.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [scenarios.length]);

  return (
    <div className="about-page-container">
      <div className={`siri-about-page ${isVisible ? 'visible' : ''}`}>
        {/* Apple风格顶部导航 */}
        <nav className="siri-nav">
          <button className="siri-nav-button" onClick={handleGoHome} aria-label="返回主页">
            <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
            <span>返回主页</span>
          </button>
        </nav>

      {/* 英雄区域 - 模仿Apple Siri主页 */}
      <section
        ref={heroRef}
        className={`siri-hero ${visibleSections.has('hero') ? 'animate-in' : ''}`}
      >
        <div className="siri-hero-content">
          <h1 className="siri-hero-title">
            <span className="siri-logo">AILF</span>
          </h1>
          <p className="siri-hero-subtitle">
            无固定前端，AI为每个用户创造专属界面
          </p>
          <p className="siri-hero-description">
            AILF颠覆传统前端开发模式。没有预设界面，没有固定模板。<br/>
            AI理解您的语音需求，实时创造完全个性化的专属前端体验。
          </p>
        </div>

        {/* 设备生态展示 */}
        <div className="siri-devices">
          <div className="device-showcase">
            <div className="device-item">💻</div>
            <div className="device-item">📱</div>
            <div className="device-item">⌚</div>
          </div>
          <p className="devices-text">适配所有设备和业务场景</p>
        </div>
      </section>

      {/* 情境化场景展示 - 模仿Apple Siri的生活场景 */}
      <section
        ref={scenariosRef}
        className={`siri-scenarios ${visibleSections.has('scenarios') ? 'animate-in' : ''}`}
      >
        <div className="scenario-container">
          <div className="scenario-visual">
            <div className="scenario-icon" style={{ color: scenarios[currentScenario].color }}>
              {scenarios[currentScenario].visual}
            </div>
          </div>

          <div className="scenario-content">
            <h2 className="scenario-title">{scenarios[currentScenario].title}</h2>
            <h3 className="scenario-subtitle">{scenarios[currentScenario].subtitle}</h3>
            <p className="scenario-description">{scenarios[currentScenario].description}</p>

            <div className="scenario-quote">
              <div className="quote-bubble">
                {scenarios[currentScenario].quote}
              </div>
            </div>
          </div>
        </div>

        {/* 场景指示器 */}
        <div className="scenario-indicators">
          {scenarios.map((_, index) => (
            <button
              key={index}
              className={`indicator ${index === currentScenario ? 'active' : ''}`}
              onClick={() => setCurrentScenario(index)}
              aria-label={`切换到场景 ${index + 1}`}
            />
          ))}
        </div>
      </section>

      {/* AILF工作流程 - 展示软件核心工作原理 */}
      <section
        ref={workflowRef}
        className={`siri-workflow ${visibleSections.has('workflow') ? 'animate-in' : ''}`}
      >
        <div className="workflow-content">
          <div className="workflow-header">
            <h2>AILF如何工作</h2>
            <p>无固定前端的革命性工作流程</p>
          </div>

          <div className="workflow-steps">
            <div className="workflow-step">
              <div className="step-number">01</div>
              <div className="step-visual">🎤</div>
              <h3>语音表达需求</h3>
              <p>用自然语言描述您的具体需求："我需要一个销售数据分析界面，重点显示本月趋势"</p>
            </div>

            <div className="workflow-arrow">→</div>

            <div className="workflow-step">
              <div className="step-number">02</div>
              <div className="step-visual">🧠</div>
              <h3>AI理解创造</h3>
              <p>AI深度理解您的意图和上下文，结合可用API和权限，创造性地设计专属界面方案</p>
            </div>

            <div className="workflow-arrow">→</div>

            <div className="workflow-step">
              <div className="step-number">03</div>
              <div className="step-visual">✨</div>
              <h3>实时生成界面</h3>
              <p>瞬间生成完全个性化的界面，每次都是独一无二的专属前端体验</p>
            </div>
          </div>

          <div className="workflow-demo">
            <div className="demo-showcase">
              <div className="demo-input">
                <span className="demo-label">输入</span>
                <div className="demo-text">"做一个销售数据分析面板"</div>
              </div>
              <div className="demo-output">
                <span className="demo-label">输出</span>
                <div className="demo-interface">
                  <div className="demo-chart">📊</div>
                  <div className="demo-table">📋</div>
                  <div className="demo-controls">⚙️</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 核心特性 - Apple风格的特性展示 */}
      <section
        ref={featuresRef}
        className={`siri-features ${visibleSections.has('features') ? 'animate-in' : ''}`}
      >
        <div className="features-header">
          <h2>AILF核心优势</h2>
          <p>颠覆传统前端开发，开创无固定界面的全新时代</p>
        </div>

        <div className="features-grid">
          <div className="feature-item feature-primary">
            <div className="feature-visual">🚫🖥️</div>
            <h3>无固定前端</h3>
            <p>彻底摆脱传统固定界面束缚，每次交互都是全新的个性化界面创造，真正做到千人千面</p>
          </div>

          <div className="feature-item">
            <div className="feature-visual">🧠✨</div>
            <h3>智能理解创造</h3>
            <p>AI深度理解语音意图和业务上下文，创造性地生成最符合用户需求的专属界面解决方案</p>
          </div>

          <div className="feature-item">
            <div className="feature-visual">🔧⚙️</div>
            <h3>开发者配置驱动</h3>
            <p>开发者只需配置API和权限框架，AI自动将后端能力包装成用户友好的动态界面</p>
          </div>

          <div className="feature-item">
            <div className="feature-visual">🌟🎯</div>
            <h3>专属体验</h3>
            <p>每个用户获得完全个性化的界面体验，AI根据具体需求实时创造，无需适应固定模板</p>
          </div>
        </div>
      </section>

      {/* 隐私保护 - 模仿Apple的隐私重视 */}
      <section
        ref={privacyRef}
        className={`siri-privacy ${visibleSections.has('privacy') ? 'animate-in' : ''}`}
      >
        <div className="privacy-content">
          <h2>隐私保护，始终如一</h2>
          <p>
            AILF在设计时就将隐私保护放在首位。您的语音输入和生成的界面数据都在本地处理，
            我们不会收集或存储您的个人信息。
          </p>
          <div className="privacy-features">
            <div className="privacy-item">
              <span className="privacy-icon">🛡️</span>
              <span>本地处理</span>
            </div>
            <div className="privacy-item">
              <span className="privacy-icon">🔐</span>
              <span>端到端加密</span>
            </div>
            <div className="privacy-item">
              <span className="privacy-icon">🚫</span>
              <span>不收集个人数据</span>
            </div>
          </div>
        </div>
      </section>

      {/* 技术生态 - Apple风格的技术展示 */}
      <section
        ref={techRef}
        className={`siri-tech ${visibleSections.has('tech') ? 'animate-in' : ''}`}
      >
        <div className="tech-content">
          <h2>先进的技术架构</h2>
          <p>基于现代化技术栈，为您提供稳定可靠的服务</p>

          <div className="tech-categories">
            <div className="tech-category">
              <h3>前端技术</h3>
              <div className="tech-items">
                <span>React 18</span>
                <span>TypeScript</span>
                <span>React Router</span>
                <span>Amis Framework</span>
              </div>
            </div>

            <div className="tech-category">
              <h3>AI引擎</h3>
              <div className="tech-items">
                <span>阿里云百炼</span>
                <span>Qwen-Coder-Plus</span>
                <span>语音识别</span>
                <span>自然语言处理</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 行动召唤 - Apple风格的CTA */}
      <section
        ref={ctaRef}
        className={`siri-cta ${visibleSections.has('cta') ? 'animate-in' : ''}`}
      >
        <div className="cta-content">
          <h2>开始您的AI界面之旅</h2>
          <p>体验AILF带来的全新界面生成方式</p>
          <button className="cta-button" onClick={handleGoHome}>
            立即体验
          </button>
        </div>
      </section>

      {/* 页脚 - Apple风格的简洁页脚 */}
      <footer className="siri-footer">
        <div className="footer-content">
          <div className="footer-info">
            <p>© 2025 AILF Team. 保留所有权利。</p>
            <p>基于 Apache 2.0 开源协议</p>
          </div>

          <div className="footer-links">
            <button
              onClick={() => window.open('https://github.com/ailf-team/ailf', '_blank')}
              className="footer-link-button"
            >
              GitHub
            </button>
            <button
              onClick={() => window.open('https://ailf.dev/docs', '_blank')}
              className="footer-link-button"
            >
              文档
            </button>
            <button
              onClick={() => window.open('https://ailf.dev/support', '_blank')}
              className="footer-link-button"
            >
              支持
            </button>
          </div>
        </div>
      </footer>
      </div>
    </div>
  );
};

export default AboutPage;
