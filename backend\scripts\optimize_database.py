"""
数据库性能优化脚本
创建索引、优化查询性能
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import text, Index
from app.core.database import engine, get_db_session
from app.models.scenario import ScenarioDBModel
from app.models.entity import EntityDBModel, EntityFieldDBModel, EntityRelationshipDBModel, EntityRecordDBModel


def create_performance_indexes():
    """创建性能优化索引"""
    print("🚀 开始创建数据库性能索引...")

    with get_db_session() as db:
        try:
            # 定义要创建的索引
            indexes = [
                # 1. 场景表索引
                ("idx_scenarios_status", "scenarios", "status", "场景状态索引"),
                ("idx_scenarios_type", "scenarios", "type", "场景类型索引"),
                ("idx_scenarios_created_at", "scenarios", "created_at", "场景创建时间索引"),

                # 2. 实体表索引
                ("idx_entities_name_unique", "entities", "name", "实体名称唯一索引"),
                ("idx_entities_status", "entities", "status", "实体状态索引"),
                ("idx_entities_scenario_id", "entities", "scenario_id", "实体场景ID索引"),
                ("idx_entities_created_at", "entities", "created_at", "实体创建时间索引"),

                # 3. 实体字段表索引
                ("idx_entity_fields_entity_id", "entity_fields", "entity_id", "字段实体ID索引"),
                ("idx_entity_fields_name", "entity_fields", "name", "字段名称索引"),
                ("idx_entity_fields_type", "entity_fields", "type", "字段类型索引"),
                ("idx_entity_fields_sort_order", "entity_fields", "sort_order", "字段排序索引"),

                # 4. 实体关系表索引
                ("idx_entity_relationships_source", "entity_relationships", "source_entity_id", "源实体ID索引"),
                ("idx_entity_relationships_target", "entity_relationships", "target_entity_id", "目标实体ID索引"),
                ("idx_entity_relationships_type", "entity_relationships", "type", "关系类型索引"),

                # 5. 实体记录表索引
                ("idx_entity_records_entity_id", "entity_records", "entity_id", "记录实体ID索引"),
                ("idx_entity_records_created_at", "entity_records", "created_at", "记录创建时间索引"),
                ("idx_entity_records_updated_at", "entity_records", "updated_at", "记录更新时间索引"),
            ]

            # 复合索引
            composite_indexes = [
                ("idx_entity_fields_entity_sort", "entity_fields", "entity_id, sort_order", "实体字段复合索引"),
                ("idx_entity_relationships_source_type", "entity_relationships", "source_entity_id, type", "关系源实体类型复合索引"),
                ("idx_entity_records_entity_created", "entity_records", "entity_id, created_at DESC", "记录实体创建时间复合索引"),
                ("idx_entity_records_entity_updated", "entity_records", "entity_id, updated_at DESC", "记录实体更新时间复合索引"),
            ]

            # 创建单列索引
            for index_name, table_name, column_name, description in indexes:
                try:
                    # 检查索引是否已存在
                    check_sql = f"""
                        SELECT COUNT(*) as count FROM information_schema.statistics
                        WHERE table_schema = DATABASE()
                        AND table_name = '{table_name}'
                        AND index_name = '{index_name}'
                    """
                    result = db.execute(text(check_sql)).scalar()

                    if result == 0:
                        # 创建索引
                        if index_name == "idx_entities_name_unique":
                            create_sql = f"CREATE UNIQUE INDEX {index_name} ON {table_name}({column_name})"
                        else:
                            create_sql = f"CREATE INDEX {index_name} ON {table_name}({column_name})"

                        db.execute(text(create_sql))
                        print(f"  ✅ 创建索引: {description}")
                    else:
                        print(f"  ⏭️ 索引已存在: {description}")

                except Exception as e:
                    print(f"  ❌ 创建索引失败 {description}: {e}")

            # 创建复合索引
            for index_name, table_name, columns, description in composite_indexes:
                try:
                    # 检查索引是否已存在
                    check_sql = f"""
                        SELECT COUNT(*) as count FROM information_schema.statistics
                        WHERE table_schema = DATABASE()
                        AND table_name = '{table_name}'
                        AND index_name = '{index_name}'
                    """
                    result = db.execute(text(check_sql)).scalar()

                    if result == 0:
                        create_sql = f"CREATE INDEX {index_name} ON {table_name}({columns})"
                        db.execute(text(create_sql))
                        print(f"  ✅ 创建复合索引: {description}")
                    else:
                        print(f"  ⏭️ 复合索引已存在: {description}")

                except Exception as e:
                    print(f"  ❌ 创建复合索引失败 {description}: {e}")

            
            print("✅ 所有性能索引创建完成！")
            
        except Exception as e:
            print(f"❌ 创建索引失败: {e}")
            raise


def analyze_query_performance():
    """分析查询性能"""
    print("🔍 分析查询性能...")
    
    with get_db_session() as db:
        try:
            # 检查索引使用情况
            result = db.execute(text("""
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    COLUMN_NAME,
                    CARDINALITY
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME IN ('scenarios', 'entities', 'entity_fields', 'entity_relationships', 'entity_records')
                ORDER BY TABLE_NAME, INDEX_NAME
            """))
            
            print("📊 当前数据库索引状况:")
            current_table = None
            for row in result:
                if row[0] != current_table:
                    current_table = row[0]
                    print(f"\n📋 表: {current_table}")
                print(f"  • 索引: {row[1]} -> 字段: {row[2]} (基数: {row[3]})")
            
            # 检查表大小
            result = db.execute(text("""
                SELECT 
                    TABLE_NAME,
                    TABLE_ROWS,
                    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'SIZE_MB'
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME IN ('scenarios', 'entities', 'entity_fields', 'entity_relationships', 'entity_records')
                ORDER BY TABLE_ROWS DESC
            """))
            
            print("\n📈 表大小统计:")
            for row in result:
                print(f"  • {row[0]}: {row[1]} 行, {row[2]} MB")
                
        except Exception as e:
            print(f"❌ 性能分析失败: {e}")


def optimize_mysql_config():
    """优化MySQL配置建议"""
    print("\n⚙️ MySQL配置优化建议:")
    print("""
    # 在MySQL配置文件 my.cnf 中添加以下配置:
    
    [mysqld]
    # 查询缓存
    query_cache_type = 1
    query_cache_size = 64M
    
    # InnoDB缓冲池
    innodb_buffer_pool_size = 1G
    innodb_buffer_pool_instances = 4
    
    # 连接优化
    max_connections = 200
    max_connect_errors = 1000
    
    # 查询优化
    tmp_table_size = 64M
    max_heap_table_size = 64M
    
    # 日志优化
    slow_query_log = 1
    slow_query_log_file = /var/log/mysql/slow.log
    long_query_time = 2
    
    # 字符集
    character-set-server = utf8mb4
    collation-server = utf8mb4_unicode_ci
    """)


if __name__ == "__main__":
    print("🚀 AILF数据库性能优化")
    print("=" * 50)
    
    try:
        # 创建性能索引
        create_performance_indexes()
        
        # 分析查询性能
        analyze_query_performance()
        
        # 显示配置建议
        optimize_mysql_config()
        
        print("\n🎉 数据库性能优化完成！")
        
    except Exception as e:
        print(f"\n❌ 优化过程中发生错误: {e}")
        sys.exit(1)
