"""
AI交互API路由
提供AI命令处理、意图分析、推荐等功能
"""
import logging
from fastapi import APIRouter, HTTPException, Depends
from typing import Optional, List

from app.schemas.ai import (
    CommandRequest, CommandResponse, CodeGenerationRequest, CodeGenerationResponse,
    EntitySuggestionRequest, WorkflowSuggestionRequest, APISuggestionRequest,
    SuggestionResponse, HealthCheckResponse
)
from pydantic import BaseModel
from typing import Dict, Any
from app.services.ai_service import ai_service
from app.services.user_permission_service import user_permission_service
from app.core.auth.jwt_handler import verify_developer_token, verify_token
from app.core.database import get_db
from app.models.user import User
from app.models.role import RoleDBModel
from sqlalchemy.orm import Session
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

logger = logging.getLogger(__name__)

router = APIRouter(tags=["AI交互"])
security = HTTPBearer(auto_error=False)


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[Dict[str, Any]]:
    """获取当前用户信息（可选，不强制认证）"""
    if not credentials:
        return None

    try:
        token = credentials.credentials

        # 首先尝试开发者token验证
        try:
            dev_payload = verify_developer_token({"Authorization": f"Bearer {token}"})
            if dev_payload and dev_payload.get("user_type") == "developer":
                # 返回开发者用户信息
                return {
                    "user_id": "developer",
                    "username": "developer",
                    "user_object": None,
                    "role": {
                        "id": "developer",
                        "name": "开发者",
                        "level": 10,
                        "permissions": ["*:*"]
                    },
                    "permissions": ["*:*"],
                    "accessible_apis": []
                }
        except:
            pass  # 如果不是开发者token，继续尝试用户token

        # 尝试用户token验证
        payload = verify_token(token)
        user_id = payload.get("user_id")

        if not user_id:
            return None

        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None

        # 获取用户角色信息
        user_role = db.query(RoleDBModel).filter(RoleDBModel.id == user.role_id).first()
        if not user_role:
            return None

        # 获取用户权限
        permissions = []
        for permission in user_role.permissions:
            permissions.append(permission.name)

        # 如果没有关联权限，根据角色级别返回默认权限
        if not permissions:
            if user_role.level >= 9:  # 超级管理员
                permissions = ["*:*"]
            elif user_role.level >= 5:  # 管理员
                permissions = ["user:*", "data:*", "report:read"]
            elif user_role.level >= 1:  # 普通用户
                permissions = ["user:read", "user:update_self", "data:query"]
            else:  # 访客
                permissions = ["user:read"]

        # 获取可访问的API列表
        accessible_apis = await get_user_accessible_apis(user_role)

        return {
            "user_id": user.id,
            "username": user.username,
            "user_object": user,  # 添加用户对象供权限服务使用
            "role": {
                "id": user_role.id,
                "name": user_role.name,
                "level": user_role.level
            },
            "permissions": permissions,
            "accessible_apis": accessible_apis
        }

    except Exception as e:
        logger.warning(f"获取用户信息失败: {str(e)}")
        return None


async def get_user_accessible_apis(role: RoleDBModel) -> List[Dict[str, Any]]:
    """获取用户可访问的API列表"""
    base_apis = [
        {
            "endpoint": "/api/user/me",
            "method": "GET",
            "description": "获取个人信息",
            "resource": "user",
            "action": "read"
        },
        {
            "endpoint": "/api/user/logout",
            "method": "POST",
            "description": "用户登出",
            "resource": "user",
            "action": "logout"
        }
    ]

    if role.level >= 1:  # 普通用户及以上
        base_apis.extend([
            {
                "endpoint": "/api/command",
                "method": "POST",
                "description": "AI命令处理",
                "resource": "ai",
                "action": "command"
            },
            {
                "endpoint": "/api/ai/validate-schema",
                "method": "POST",
                "description": "Schema校验",
                "resource": "ai",
                "action": "validate"
            }
        ])

    if role.level >= 5:  # 管理员及以上
        base_apis.extend([
            {
                "endpoint": "/api/users",
                "method": "GET",
                "description": "用户列表查询",
                "resource": "users",
                "action": "list"
            },
            {
                "endpoint": "/api/roles",
                "method": "GET",
                "description": "角色列表查询",
                "resource": "roles",
                "action": "list"
            }
        ])

    return base_apis


class SchemaValidationRequest(BaseModel):
    """Schema验证请求"""
    schema: Dict[str, Any]
    schema_type: str = "amis"  # amis, json-schema等


class SchemaValidationResponse(BaseModel):
    """Schema验证响应"""
    success: bool
    valid: bool = False
    errors: list = []
    warnings: list = []
    suggestions: list = []
    error: str = None


class AmisPreviewRequest(BaseModel):
    """Amis预览请求"""
    schema: Dict[str, Any]
    mock_data: Dict[str, Any] = {}


class AmisPreviewResponse(BaseModel):
    """Amis预览响应"""
    success: bool
    preview_url: str = None
    error: str = None


@router.post("/command", response_model=CommandResponse)
async def process_command(
    request: CommandRequest,
    current_user: dict = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    处理用户的语音/文本命令，生成动态界面

    支持自然语言命令处理，返回生成的amis schema和响应文本
    支持的命令示例：
    - "显示用户列表"
    - "创建新用户"
    - "编辑用户信息"
    - "查看产品详情"
    - "生成销售报表"
    """
    try:
        # 获取用户权限信息并合并到上下文中
        enhanced_context = request.context or {}

        if current_user:
            # 检查是否为开发者用户
            if current_user.get("user_id") == "developer":
                # 开发者用户直接使用预设的权限信息
                enhanced_context.update({
                    "user_permissions": ["*:*", "workflow:execute", "workflow:*", "train_ticket:booking"],
                    "accessible_apis": current_user.get("accessible_apis", []),
                    "user_role": current_user.get("role", {"id": "developer", "name": "开发者", "level": 10})
                })
            else:
                # 普通用户使用用户权限服务获取完整的权限信息
                user_context = user_permission_service.get_user_context_for_ai(
                    current_user.get("user_object"),
                    db
                )
                enhanced_context.update(user_context)
        else:
            # 未登录用户使用访客权限
            enhanced_context.update({
                "user_id": None,
                "user_permissions": [],
                "accessible_apis": user_permission_service._get_guest_apis(),
                "user_role": {"id": "guest", "name": "访客", "level": 0}
            })

        result = await ai_service.process_command(
            command=request.command,
            context=enhanced_context
        )
        
        if result["success"]:
            return CommandResponse(
                success=True,
                data=result["data"]
            )
        else:
            return CommandResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"处理命令失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "处理命令失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/ai/generate-code", response_model=CodeGenerationResponse)
async def generate_code(
    request: CodeGenerationRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    AI生成代码
    
    根据提示生成高质量的Python代码
    """
    try:
        result = await ai_service.generate_code(
            prompt=request.prompt,
            context=request.context
        )
        
        if result["success"]:
            return CodeGenerationResponse(
                success=True,
                content=result["content"],
                usage=result.get("usage"),
                model=result.get("model"),
                timestamp=result.get("timestamp")
            )
        else:
            return CodeGenerationResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"代码生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "代码生成失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/ai/suggest-entities", response_model=SuggestionResponse)
async def suggest_entities(
    request: EntitySuggestionRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    AI推荐业务实体设计
    
    基于业务描述推荐合适的数据实体结构
    """
    try:
        result = await ai_service.suggest_entities(request.business_description)
        
        if result["success"]:
            return SuggestionResponse(
                success=True,
                suggestions=result["suggestions"],
                raw_response=result.get("raw_response")
            )
        else:
            return SuggestionResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"实体推荐失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "实体推荐失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/ai/suggest-workflows", response_model=SuggestionResponse)
async def suggest_workflows(
    request: WorkflowSuggestionRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    AI推荐工作流设计
    
    基于业务实体和场景推荐合适的工作流程
    """
    try:
        result = await ai_service.suggest_workflows(
            entities=request.entities,
            business_scenario=request.business_scenario
        )
        
        if result["success"]:
            return SuggestionResponse(
                success=True,
                suggestions=result["suggestions"],
                raw_response=result.get("raw_response")
            )
        else:
            return SuggestionResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"工作流推荐失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "工作流推荐失败",
                "data": {"error": str(e)}
            }
        )


@router.post("/ai/suggest-apis", response_model=SuggestionResponse)
async def suggest_apis(
    request: APISuggestionRequest,
    _: dict = Depends(verify_developer_token)
):
    """
    AI推荐API设计
    
    基于业务实体和操作类型推荐RESTful API设计
    """
    try:
        result = await ai_service.suggest_apis(
            entities=request.entities,
            operations=request.operations
        )
        
        if result["success"]:
            return SuggestionResponse(
                success=True,
                suggestions=result["suggestions"],
                raw_response=result.get("raw_response")
            )
        else:
            return SuggestionResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"API推荐失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": "API推荐失败",
                "data": {"error": str(e)}
            }
        )


@router.get("/ai/health", response_model=HealthCheckResponse)
async def ai_health_check(
    _: dict = Depends(verify_developer_token)
):
    """
    AI服务健康检查
    
    检查AI客户端连接状态和服务可用性
    """
    try:
        result = await ai_service.health_check()
        
        return HealthCheckResponse(
            success=result["success"],
            ai_client=result["ai_client"],
            error=result.get("error")
        )
        
    except Exception as e:
        logger.error(f"AI健康检查失败: {str(e)}")
        return HealthCheckResponse(
            success=False,
            ai_client={"status": "unhealthy", "error": str(e)},
            error=str(e)
        )


@router.post("/ai/validate-schema", response_model=SchemaValidationResponse)
async def validate_amis_schema(
    request: SchemaValidationRequest
):
    """
    验证amis schema的正确性

    使用专业的amis校验器检查schema结构、组件配置、数据绑定等是否符合amis规范
    """
    try:
        from app.core.ai.amis_validator import amis_validator

        schema = request.schema
        schema_type = request.schema_type

        if schema_type == "amis":
            # 使用专业的amis校验器
            validation_result = amis_validator.validate(schema)

            return SchemaValidationResponse(
                success=True,
                valid=validation_result.is_valid,
                errors=[error["message"] for error in validation_result.errors],
                warnings=[warning["message"] for warning in validation_result.warnings],
                suggestions=[info["message"] for info in validation_result.infos]
            )
        else:
            return SchemaValidationResponse(
                success=False,
                error=f"不支持的schema类型: {schema_type}"
            )

    except Exception as e:
        logger.error(f"Schema验证失败: {str(e)}")
        return SchemaValidationResponse(
            success=False,
            error=f"验证过程出错: {str(e)}"
        )


@router.post("/ai/preview-schema", response_model=AmisPreviewResponse)
async def preview_amis_schema(
    request: AmisPreviewRequest
):
    """
    预览amis schema效果

    生成可预览的HTML页面，展示schema的实际效果
    """
    try:
        schema = request.schema
        mock_data = request.mock_data

        # 这里可以生成预览页面
        # 暂时返回成功响应
        preview_url = "/preview/amis-schema"  # 实际应该生成唯一的预览URL

        return AmisPreviewResponse(
            success=True,
            preview_url=preview_url
        )

    except Exception as e:
        logger.error(f"Schema预览失败: {str(e)}")
        return AmisPreviewResponse(
            success=False,
            error=f"预览生成失败: {str(e)}"
        )



