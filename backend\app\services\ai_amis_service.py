"""
AI AMIS Schema生成服务
专门处理amis schema生成，考虑用户权限和API访问控制
使用core/ai/bailian_client进行AI调用
"""
import logging
from typing import Dict, Any, Optional, List
import json

from app.core.ai.bailian_client import bailian_client
from app.core.ai.amis_knowledge import amis_knowledge
from app.core.ai.amis_validator import amis_validator
from app.core.ai.prompt_templates import prompt_manager, PromptType
from app.services.workflow_service import workflow_service

logger = logging.getLogger(__name__)


class AIAmisService:
    """AI AMIS Schema生成服务"""
    
    def __init__(self):
        self.client = bailian_client
    
    async def generate_schema_with_permissions(self, 
                                             command: str,
                                             intent: Dict[str, Any],
                                             user_permissions: List[str],
                                             accessible_apis: List[Dict[str, Any]],
                                             user_role: Dict[str, Any],
                                             entities: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        根据用户权限生成amis schema
        这是核心方法，确保生成的schema能正确调用已有API并考虑用户权限
        
        Args:
            command: 用户原始命令
            intent: 用户意图分析结果
            user_permissions: 用户权限列表
            accessible_apis: 用户可访问的API列表
            user_role: 用户角色信息
            entities: 实体信息
            
        Returns:
            生成的amis schema和相关信息
        """
        try:
            logger.info(f"开始生成amis schema - 命令: {command}, 用户角色: {user_role.get('name')}, 权限数量: {len(user_permissions)}")
            
            # 1. 检查用户是否有权限执行此操作
            if not self._check_intent_permission(intent, user_permissions, accessible_apis):
                return self._generate_permission_denied_response(intent, command)
            
            # 2. 根据意图类型和权限生成schema
            intent_type = intent.get("intent_type", "unknown")
            entity = intent.get("entity", "")
            
            if intent_type == "data_query" and entity:
                return await self._generate_data_query_schema(
                    entity, accessible_apis, user_permissions, user_role, entities
                )
            elif intent_type == "data_create" and entity:
                return await self._generate_data_create_schema(
                    entity, accessible_apis, user_permissions, user_role, entities
                )
            elif intent_type == "data_update" and entity:
                return await self._generate_data_update_schema(
                    entity, accessible_apis, user_permissions, user_role, entities
                )
            elif intent_type == "workflow_execute":
                return await self._generate_workflow_schema(
                    command, intent, accessible_apis, user_permissions, user_role, entities
                )
            else:
                # 使用AI生成通用schema
                return await self._generate_ai_schema(
                    command, intent, accessible_apis, user_permissions, user_role, entities
                )
                
        except Exception as e:
            logger.error(f"AMIS schema生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "amis_schema": self._generate_error_schema(str(e)),
                "response_text": f"抱歉，生成界面时出现错误：{str(e)}"
            }
    
    def _check_intent_permission(self, intent: Dict[str, Any],
                                user_permissions: List[str],
                                accessible_apis: List[Dict[str, Any]]) -> bool:
        """检查用户是否有权限执行此意图"""
        intent_type = intent.get("intent_type", "")
        entity = intent.get("entity", "")
        action = intent.get("action", "")

        logger.info(f"权限检查 - 意图类型: {intent_type}, 实体: {entity}, 动作: {action}")
        logger.info(f"用户权限: {user_permissions}")
        logger.info(f"可访问API数量: {len(accessible_apis)}")
        print(f"🔍 权限检查 - 意图类型: {intent_type}, 实体: {entity}, 动作: {action}")
        print(f"🔍 用户权限: {user_permissions}")
        print(f"🔍 可访问API数量: {len(accessible_apis)}")

        # 超级管理员有所有权限
        if "*:*" in user_permissions:
            logger.info("用户有超级管理员权限，允许访问")
            print("✅ 用户有超级管理员权限，允许访问")
            return True

        # 对于工作流执行类型，检查是否有工作流权限
        if intent_type == "workflow_execute":
            # 检查通用工作流权限
            if ("workflow:execute" in user_permissions or
                "workflow:*" in user_permissions or
                "train_ticket:booking" in user_permissions):
                logger.info("用户有工作流执行权限，允许访问")
                print("✅ 用户有工作流执行权限，允许访问")
                return True

            # 临时：开发阶段允许所有工作流执行
            logger.info("开发阶段：允许工作流执行")
            print("✅ 开发阶段：允许工作流执行")
            return True
        
        # 检查具体权限
        required_permission = f"{entity}:{action}"
        logger.info(f"检查具体权限: {required_permission}")
        if required_permission in user_permissions:
            logger.info("找到匹配的具体权限，允许访问")
            return True

        # 检查API访问权限
        for api in accessible_apis:
            if (api.get("resource") == entity and
                api.get("action") == action):
                logger.info("找到匹配的API权限，允许访问")
                return True

        logger.info("权限检查失败，拒绝访问")
        return False
    
    async def _generate_data_query_schema(self, entity: str,
                                        accessible_apis: List[Dict[str, Any]],
                                        user_permissions: List[str],
                                        user_role: Dict[str, Any],
                                        entities: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """生成数据查询schema（CRUD列表）"""
        try:
            # 获取实体字段信息
            entity_fields = self._get_entity_fields(entity, entities)
            
            # 使用增强的amis知识库生成CRUD，确保API调用正确
            crud_schema = amis_knowledge.generate_crud_with_permissions(
                entity, accessible_apis, entity_fields
            )
            
            # 如果用户没有权限，返回权限提示
            if crud_schema.get("type") == "alert":
                return {
                    "success": True,
                    "amis_schema": {
                        "type": "page",
                        "title": f"{entity}管理",
                        "body": [crud_schema]
                    },
                    "response_text": f"您没有权限查看{entity}数据，请联系管理员获取相应权限。",
                    "validation": {"is_valid": True, "errors": [], "warnings": [], "infos": []}
                }
            
            # 构建完整的页面schema
            page_schema = {
                "type": "page",
                "title": f"{entity}管理",
                "className": "bg-dark text-light",
                "body": [crud_schema]
            }
            
            # 确保所有API调用都包含正确的认证头部
            self._ensure_api_authentication(page_schema)
            
            # 校验生成的schema
            validation_result = amis_validator.validate(page_schema)
            
            return {
                "success": True,
                "amis_schema": page_schema,
                "response_text": f"已为您生成{entity}管理界面，您可以查看、搜索和管理{entity}数据。",
                "validation": validation_result.to_dict(),
                "entity_fields": entity_fields,
                "accessible_apis": accessible_apis
            }
            
        except Exception as e:
            logger.error(f"数据查询schema生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "amis_schema": self._generate_error_schema(str(e)),
                "response_text": f"生成{entity}查询界面失败：{str(e)}"
            }
    
    async def _generate_workflow_schema(self, command: str,
                                      intent: Dict[str, Any],
                                      accessible_apis: List[Dict[str, Any]],
                                      user_permissions: List[str],
                                      user_role: Dict[str, Any],
                                      entities: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """生成工作流执行schema"""
        try:
            # 获取所有可用的工作流信息
            workflow_config_result = workflow_service.get_amis_configuration_info()

            if not workflow_config_result.get("success"):
                return {
                    "success": False,
                    "error": "获取工作流配置失败",
                    "amis_schema": self._generate_error_schema("无法获取工作流配置"),
                    "response_text": "抱歉，无法获取工作流配置信息。"
                }

            workflow_data = workflow_config_result.get("data", {})
            available_workflows = workflow_data.get("workflows", {}).get("items", [])
            available_apis = workflow_data.get("apis", {}).get("items", [])

            # 根据用户命令匹配最合适的工作流
            matched_workflow = self._match_workflow_by_intent(command, intent, available_workflows)

            if matched_workflow:
                # 生成特定工作流的执行界面
                return await self._generate_specific_workflow_schema(
                    matched_workflow, available_apis, user_permissions, user_role
                )
            else:
                # 生成工作流选择界面
                return await self._generate_workflow_selection_schema(
                    available_workflows, user_permissions, user_role
                )

        except Exception as e:
            logger.error(f"工作流schema生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "amis_schema": self._generate_error_schema(str(e)),
                "response_text": f"生成工作流界面失败：{str(e)}"
            }

    async def _generate_ai_schema(self, command: str,
                                intent: Dict[str, Any],
                                accessible_apis: List[Dict[str, Any]],
                                user_permissions: List[str],
                                user_role: Dict[str, Any],
                                entities: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """使用AI生成通用schema"""
        try:
            # 获取工作流配置信息，增强AI的上下文
            workflow_config_result = workflow_service.get_amis_configuration_info()
            workflow_context = ""
            if workflow_config_result.get("success"):
                workflow_data = workflow_config_result.get("data", {})
                workflow_context = json.dumps(workflow_data, ensure_ascii=False)

            # 构建包含权限信息和工作流信息的提示词
            messages = prompt_manager.get_messages(
                PromptType.AMIS_SCHEMA_GENERATION,
                command=command,
                intent=json.dumps(intent, ensure_ascii=False),
                user_permissions=json.dumps(user_permissions, ensure_ascii=False),
                accessible_apis=json.dumps(accessible_apis, ensure_ascii=False),
                user_role=json.dumps(user_role, ensure_ascii=False),
                entities=json.dumps(entities or [], ensure_ascii=False),
                scenario_config=workflow_context
            )
            
            # 调用AI生成schema
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=2000
            )
            
            if not response.get("success"):
                return {
                    "success": False,
                    "error": "AI schema生成失败",
                    "amis_schema": self._generate_error_schema("AI调用失败"),
                    "response_text": "抱歉，AI服务暂时不可用，请稍后重试。"
                }
            
            # 解析AI生成的schema
            try:
                schema_content = response.get("content", "{}")
                # 尝试提取JSON部分
                if "```json" in schema_content:
                    json_start = schema_content.find("```json") + 7
                    json_end = schema_content.find("```", json_start)
                    schema_content = schema_content[json_start:json_end].strip()
                elif "{" in schema_content:
                    # 提取第一个完整的JSON对象
                    start = schema_content.find("{")
                    schema_content = schema_content[start:]
                
                schema = json.loads(schema_content)
            except json.JSONDecodeError as e:
                logger.warning(f"AI返回的schema格式错误: {str(e)}")
                schema = self._generate_fallback_schema(intent)
            
            # 确保schema包含正确的API调用和认证
            self._ensure_api_authentication(schema)
            self._apply_permission_constraints(schema, accessible_apis, user_permissions)
            
            # 校验生成的schema
            validation_result = amis_validator.validate(schema)
            
            return {
                "success": True,
                "amis_schema": schema,
                "response_text": f"已根据您的需求生成相应界面。",
                "validation": validation_result.to_dict(),
                "ai_usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"AI schema生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "amis_schema": self._generate_error_schema(str(e)),
                "response_text": f"生成界面失败：{str(e)}"
            }
    
    def _ensure_api_authentication(self, schema: Dict[str, Any]):
        """确保schema中的所有API调用都包含正确的认证头部"""
        def process_component(component):
            if not isinstance(component, dict):
                return
            
            # 处理API配置
            if "api" in component:
                api_config = component["api"]
                if isinstance(api_config, dict):
                    # 确保包含认证头部
                    if "headers" not in api_config:
                        api_config["headers"] = {}
                    api_config["headers"]["Authorization"] = "Bearer ${ls:access_token}"
                elif isinstance(api_config, str):
                    # 转换为对象格式并添加认证
                    component["api"] = {
                        "url": api_config,
                        "headers": {
                            "Authorization": "Bearer ${ls:access_token}"
                        }
                    }
            
            # 递归处理子组件
            for key, value in component.items():
                if key == "body" and isinstance(value, list):
                    for item in value:
                        process_component(item)
                elif key in ["dialog", "drawer"] and isinstance(value, dict):
                    process_component(value.get("body", {}))
                elif isinstance(value, dict):
                    process_component(value)
        
        process_component(schema)
    
    def _apply_permission_constraints(self, schema: Dict[str, Any],
                                    accessible_apis: List[Dict[str, Any]],
                                    user_permissions: List[str]):
        """应用权限约束到schema"""
        try:
            # 这里可以添加更复杂的权限约束逻辑
            # 例如：移除用户无权限的按钮、禁用某些功能等
            pass
        except Exception as e:
            logger.warning(f"应用权限约束失败: {str(e)}")
    
    def _get_entity_fields(self, entity: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """获取实体字段信息"""
        if entities:
            for ent in entities:
                if ent.get("name") == entity:
                    return ent.get("fields", [])
        
        # 返回默认字段
        return [
            {"name": "id", "label": "ID", "type": "integer", "primary": True, "readonly": True},
            {"name": "name", "label": "名称", "type": "string", "required": True},
            {"name": "description", "label": "描述", "type": "string"},
            {"name": "created_at", "label": "创建时间", "type": "datetime", "readonly": True}
        ]
    
    def _generate_permission_denied_response(self, intent: Dict[str, Any], command: str) -> Dict[str, Any]:
        """生成权限不足的响应"""
        entity = intent.get("entity", "资源")
        action = intent.get("action", "操作")
        
        return {
            "success": True,
            "amis_schema": {
                "type": "page",
                "title": "权限不足",
                "body": [
                    {
                        "type": "alert",
                        "level": "warning",
                        "body": f"您没有权限{action}{entity}，请联系管理员获取相应权限。"
                    }
                ]
            },
            "response_text": f"抱歉，您没有权限执行\"{command}\"操作，请联系管理员获取相应权限。",
            "validation": {"is_valid": True, "errors": [], "warnings": [], "infos": []}
        }
    
    def _generate_error_schema(self, error_message: str) -> Dict[str, Any]:
        """生成错误提示schema"""
        return {
            "type": "page",
            "title": "生成失败",
            "body": [
                {
                    "type": "alert",
                    "level": "danger",
                    "body": f"界面生成失败: {error_message}"
                }
            ]
        }
    
    def _generate_fallback_schema(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """生成备用schema（当AI生成失败时）"""
        entity = intent.get("entity", "数据")
        
        return {
            "type": "page",
            "title": f"{entity}管理",
            "body": [
                {
                    "type": "alert",
                    "level": "info",
                    "body": f"正在为您准备{entity}管理界面，请稍候..."
                }
            ]
        }
    
    def _match_workflow_by_intent(self, command: str, intent: Dict[str, Any], workflows: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """根据用户意图匹配最合适的工作流"""
        try:
            command_lower = command.lower()
            best_match = None
            best_score = 0.0

            print(f"🔍 工作流匹配 - 命令: '{command}'")
            print(f"🔍 工作流匹配 - 可用工作流数量: {len(workflows)}")

            for workflow in workflows:
                score = 0.0
                workflow_name = workflow.get("label", workflow.get("name", "未知工作流"))

                print(f"🔍 检查工作流: {workflow_name}")

                # 检查用户意图匹配
                user_intents = workflow.get("user_intents", []) or []
                print(f"   用户意图: {user_intents}")
                for user_intent in user_intents:
                    if user_intent and user_intent.lower() in command_lower:
                        score += 0.8
                        print(f"   ✅ 用户意图匹配: '{user_intent}' (+0.8)")
                        break

                # 检查触发关键词匹配
                trigger_keywords = workflow.get("trigger_keywords", []) or []
                print(f"   触发关键词: {trigger_keywords}")
                for keyword in trigger_keywords:
                    if keyword and keyword.lower() in command_lower:
                        score += 0.6
                        print(f"   ✅ 关键词匹配: '{keyword}' (+0.6)")

                # 检查业务场景匹配
                business_scenario = workflow.get("business_scenario", "")
                if business_scenario and business_scenario.lower() in command_lower:
                    score += 0.5
                    print(f"   ✅ 业务场景匹配: '{business_scenario}' (+0.5)")

                # 检查工作流名称匹配
                if workflow_name and any(word in command_lower for word in workflow_name.lower().split()):
                    score += 0.4
                    print(f"   ✅ 名称匹配: '{workflow_name}' (+0.4)")

                print(f"   总分: {score}")

                if score > best_score:
                    best_score = score
                    best_match = workflow

            # 只有当匹配度足够高时才返回匹配结果
            print(f"🎯 最佳匹配: {best_match.get('label', '无') if best_match else '无'}, 分数: {best_score}")
            return best_match if best_score >= 0.4 else None

        except Exception as e:
            logger.error(f"工作流匹配失败: {str(e)}")
            return None

    async def _generate_specific_workflow_schema(self, workflow: Dict[str, Any],
                                               available_apis: List[Dict[str, Any]],
                                               user_permissions: List[str],
                                               user_role: Dict[str, Any]) -> Dict[str, Any]:
        """生成特定工作流的执行界面"""
        try:
            workflow_name = workflow.get("label", workflow.get("name", "未知工作流"))
            workflow_description = workflow.get("description", "")
            workflow_inputs = workflow.get("inputs", [])
            workflow_nodes = workflow.get("nodes", [])
            business_scenario = workflow.get("business_scenario", "")

            print(f"🔧 生成工作流界面: {workflow_name}")
            print(f"   业务场景: {business_scenario}")
            print(f"   节点数量: {len(workflow_nodes)}")
            print(f"   输入字段: {len(workflow_inputs)}")

            # 使用AI生成更智能的表单界面
            ai_prompt = f"""
请为"{workflow_name}"工作流生成一个用户友好的amis表单界面。

工作流信息：
- 名称：{workflow_name}
- 描述：{workflow_description}
- 业务场景：{business_scenario}
- 节点数量：{len(workflow_nodes)}

可用API信息：
{json.dumps([{"name": api.get("name"), "endpoint": api.get("endpoint"), "description": api.get("description")} for api in available_apis[:5]], ensure_ascii=False, indent=2)}

请生成一个包含以下特点的表单：
1. 根据业务场景设计合适的输入字段
2. 使用合适的表单组件类型
3. 添加必要的验证规则
4. 提供清晰的标签和提示信息
5. 使用深色主题样式

请直接返回JSON格式的amis schema，不要包含其他文本。
"""

            # 调用AI生成schema
            try:
                ai_response = await self.client.chat_completion(
                    messages=[{"role": "user", "content": ai_prompt}],
                    temperature=0.3,
                    max_tokens=2000
                )

                if ai_response.get("success") and ai_response.get("content"):
                    ai_content = ai_response["content"].strip()
                    # 尝试解析AI返回的JSON
                    try:
                        import re
                        # 提取JSON部分
                        json_match = re.search(r'\{.*\}', ai_content, re.DOTALL)
                        if json_match:
                            ai_schema = json.loads(json_match.group())
                            print("✅ AI生成的schema解析成功")

                            return {
                                "success": True,
                                "amis_schema": ai_schema,
                                "response_text": f"已为您生成{workflow_name}的智能执行界面，请填写必要参数后执行。",
                                "validation": {"is_valid": True, "errors": [], "warnings": [], "infos": []},
                                "matched_workflow": workflow
                            }
                    except json.JSONDecodeError as e:
                        print(f"⚠️ AI返回的JSON解析失败: {e}")
                        # 继续使用默认方法

            except Exception as e:
                print(f"⚠️ AI生成schema失败: {e}")
                # 继续使用默认方法

            # 默认方法：构建基础工作流执行表单
            form_items = []

            # 根据业务场景生成特定的表单字段
            if business_scenario == "train_ticket_booking":
                form_items = [
                    {
                        "type": "input-text",
                        "name": "departure_city",
                        "label": "出发城市",
                        "required": True,
                        "placeholder": "请输入出发城市，如：北京"
                    },
                    {
                        "type": "input-text",
                        "name": "arrival_city",
                        "label": "到达城市",
                        "required": True,
                        "placeholder": "请输入到达城市，如：上海"
                    },
                    {
                        "type": "input-date",
                        "name": "departure_date",
                        "label": "出发日期",
                        "required": True,
                        "format": "YYYY-MM-DD"
                    },
                    {
                        "type": "select",
                        "name": "seat_type",
                        "label": "座位类型",
                        "options": [
                            {"label": "硬座", "value": "hard_seat"},
                            {"label": "硬卧", "value": "hard_sleeper"},
                            {"label": "软卧", "value": "soft_sleeper"},
                            {"label": "高铁二等座", "value": "second_class"},
                            {"label": "高铁一等座", "value": "first_class"},
                            {"label": "商务座", "value": "business_class"}
                        ],
                        "value": "second_class"
                    },
                    {
                        "type": "input-number",
                        "name": "passenger_count",
                        "label": "乘客数量",
                        "required": True,
                        "min": 1,
                        "max": 6,
                        "value": 1
                    }
                ]
            else:
                # 通用表单字段
                for input_item in workflow_inputs:
                    form_item = {
                        "type": "input-text",
                        "name": input_item.get("name"),
                        "label": input_item.get("description"),
                        "required": input_item.get("required", False),
                        "placeholder": input_item.get("ai_prompt", "")
                    }

                    # 根据输入类型调整表单项
                    input_type = input_item.get("type", "string")
                    if input_type == "date":
                        form_item["type"] = "input-date"
                    elif input_type == "number":
                        form_item["type"] = "input-number"
                    elif input_type == "email":
                        form_item["type"] = "input-email"

                    form_items.append(form_item)

            # 构建工作流执行schema
            schema = {
                "type": "page",
                "title": f"执行工作流：{workflow_name}",
                "className": "bg-dark text-light",
                "body": [
                    {
                        "type": "alert",
                        "level": "info",
                        "body": workflow_description
                    },
                    {
                        "type": "form",
                        "title": "工作流参数",
                        "body": form_items,
                        "actions": [
                            {
                                "type": "button",
                                "label": "执行工作流",
                                "level": "primary",
                                "actionType": "submit"
                            }
                        ],
                        "api": {
                            "method": "post",
                            "url": f"/api/workflows/{workflow.get('id')}/execute",
                            "headers": {
                                "Authorization": "Bearer ${token}"
                            }
                        }
                    }
                ]
            }

            return {
                "success": True,
                "amis_schema": schema,
                "response_text": f"已为您生成{workflow_name}的执行界面，请填写必要参数后执行。",
                "validation": {"is_valid": True, "errors": [], "warnings": [], "infos": []},
                "matched_workflow": workflow
            }

        except Exception as e:
            logger.error(f"特定工作流schema生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "amis_schema": self._generate_error_schema(str(e)),
                "response_text": f"生成工作流执行界面失败：{str(e)}"
            }

    async def _generate_workflow_selection_schema(self, workflows: List[Dict[str, Any]],
                                                user_permissions: List[str],
                                                user_role: Dict[str, Any]) -> Dict[str, Any]:
        """生成工作流选择界面"""
        try:
            # 构建工作流选项
            workflow_options = []
            for workflow in workflows:
                workflow_options.append({
                    "label": workflow.get("name", "未知工作流"),
                    "value": workflow.get("id"),
                    "description": workflow.get("description", ""),
                    "business_scenario": workflow.get("business_scenario", "")
                })

            schema = {
                "type": "page",
                "title": "选择工作流",
                "className": "bg-dark text-light",
                "body": [
                    {
                        "type": "alert",
                        "level": "info",
                        "body": "请选择要执行的工作流："
                    },
                    {
                        "type": "cards",
                        "source": workflow_options,
                        "card": {
                            "header": {
                                "title": "${label}",
                                "subTitle": "${business_scenario}"
                            },
                            "body": [
                                {
                                    "type": "tpl",
                                    "tpl": "${description}"
                                }
                            ],
                            "actions": [
                                {
                                    "type": "button",
                                    "label": "执行",
                                    "level": "primary",
                                    "actionType": "link",
                                    "link": "/workflow/execute/${value}"
                                }
                            ]
                        }
                    }
                ]
            }

            return {
                "success": True,
                "amis_schema": schema,
                "response_text": f"找到{len(workflows)}个可用工作流，请选择要执行的工作流。",
                "validation": {"is_valid": True, "errors": [], "warnings": [], "infos": []},
                "available_workflows": workflows
            }

        except Exception as e:
            logger.error(f"工作流选择schema生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "amis_schema": self._generate_error_schema(str(e)),
                "response_text": f"生成工作流选择界面失败：{str(e)}"
            }

    def get_health_status(self) -> Dict[str, Any]:
        """获取AMIS生成服务健康状态"""
        return {
            "service": "ai_amis_service",
            "status": "healthy",
            "client_status": self.client.get_health_status()
        }


# 全局AI AMIS生成服务实例
ai_amis_service = AIAmisService()
