"""
调试AI command接口
"""
import requests
import json

BASE_URL = "http://localhost:5000"

def get_developer_token():
    """获取开发者token"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/developer",
            json={"password": "AILF_DEV_2024_SECURE"},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"认证响应状态码: {response.status_code}")
        print(f"认证响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            return data["data"]["token"]
        else:
            print(f"❌ 获取token失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取token异常: {e}")
        return None

def test_ai_command(token):
    """测试AI command接口"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"\n🔍 测试AI command接口...")
        print(f"URL: {BASE_URL}/api/command")
        print(f"Headers: {headers}")
        
        response = requests.post(
            f"{BASE_URL}/api/command",
            json={"command": "我要买火车票"},
            headers=headers
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ AI command接口测试成功")
            return True
        else:
            print(f"❌ AI command接口测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI command接口测试异常: {e}")
        return False

def test_api_docs():
    """测试API文档是否可访问"""
    try:
        response = requests.get(f"{BASE_URL}/docs")
        print(f"API文档状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ API文档可访问")
            return True
        else:
            print("❌ API文档不可访问")
            return False
    except Exception as e:
        print(f"❌ API文档访问异常: {e}")
        return False

def test_health():
    """测试健康检查"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康检查状态码: {response.status_code}")
        print(f"健康检查响应: {response.text}")
        if response.status_code == 200:
            print("✅ 服务健康")
            return True
        else:
            print("❌ 服务不健康")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 AI Command 接口调试工具")
    print("=" * 60)
    
    # 1. 测试服务健康状态
    print("1️⃣ 测试服务健康状态...")
    if not test_health():
        print("❌ 服务不健康，调试终止")
        return 1
    
    # 2. 测试API文档
    print("\n2️⃣ 测试API文档...")
    test_api_docs()
    
    # 3. 获取开发者token
    print("\n3️⃣ 获取开发者token...")
    token = get_developer_token()
    if not token:
        print("❌ 无法获取开发者token，调试终止")
        return 1
    
    print(f"✅ 获取token成功: {token[:50]}...")
    
    # 4. 测试AI command接口
    print("\n4️⃣ 测试AI command接口...")
    if test_ai_command(token):
        print("🎉 AI command接口调试成功！")
        return 0
    else:
        print("❌ AI command接口调试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
