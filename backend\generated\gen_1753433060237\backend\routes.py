```python
from fastapi import FastAPI, HTTPException, Depends, status
from pydantic import BaseModel, Field
from typing import List, Optional
from decimal import Decimal
import uvicorn

app = FastAPI(
    title="商品管理系统API",
    description="提供商品管理的RESTful API接口",
    version="1.0.0"
)

# 数据模型定义
class ProductBase(BaseModel):
    name: str = Field(..., title="商品名称", description="商品的名称", example="苹果")
    price: Decimal = Field(..., title="价格", description="商品的价格", example=5.99, gt=0)
    category: str = Field(..., title="分类", description="商品的分类", example="水果")

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = Field(None, title="商品名称", description="商品的名称", example="苹果")
    price: Optional[Decimal] = Field(None, title="价格", description="商品的价格", example=5.99, gt=0)
    category: Optional[str] = Field(None, title="分类", description="商品的分类", example="水果")

class Product(ProductBase):
    id: int = Field(..., title="商品ID", description="商品的唯一标识符", example=1)
    
    class Config:
        orm_mode = True

# 模拟数据库
fake_products_db = [
    {"id": 1, "name": "苹果", "price": Decimal("5.99"), "category": "水果"},
    {"id": 2, "name": "香蕉", "price": Decimal("2.99"), "category": "水果"},
    {"id": 3, "name": "牛奶", "price": Decimal("3.49"), "category": "乳制品"},
]

fake_products_db_next_id = 4

# 权限依赖
def verify_admin_token():
    # 这里应该实现实际的权限验证逻辑
    # 例如验证JWT token或API key
    return True

# API路由实现
@app.get(
    "/api/products",
    response_model=List[Product],
    summary="获取商品列表",
    description="获取所有商品的列表信息"
)
async def get_products(
    category: Optional[str] = None,
    min_price: Optional[Decimal] = None,
    max_price: Optional[Decimal] = None
):
    """
    获取商品列表:
    
    - **category**: 可选，按分类筛选商品
    - **min_price**: 可选，筛选价格不低于此值的商品
    - **max_price**: 可选，筛选价格不高于此值的商品
    
    返回:
    - 商品列表
    """
    filtered_products = fake_products_db
    
    if category:
        filtered_products = [p for p in filtered_products if p["category"] == category]
    
    if min_price is not None:
        filtered_products = [p for p in filtered_products if p["price"] >= min_price]
    
    if max_price is not None:
        filtered_products = [p for p in filtered_products if p["price"] <= max_price]
    
    return filtered_products

@app.get(
    "/api/products/{product_id}",
    response_model=Product,
    summary="获取商品详情",
    description="根据商品ID获取单个商品的详细信息"
)
async def get_product(product_id: int):
    """
    获取商品详情:
    
    - **product_id**: 商品的唯一标识符
    
    返回:
    - 商品详细信息
    
    异常:
    - 404: 商品未找到
    """
    for product in fake_products_db:
        if product["id"] == product_id:
            return product
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="商品未找到"
    )

@app.post(
    "/api/products",
    response_model=Product,
    status_code=status.HTTP_201_CREATED,
    summary="创建商品",
    description="创建一个新的商品",
    dependencies=[Depends(verify_admin_token)]
)
async def create_product(product: ProductCreate):
    """
    创建商品:
    
    请求体:
    - **name**: 商品名称
    - **price**: 商品价格
    - **category**: 商品分类
    
    返回:
    - 创建的商品信息
    
    权限:
    - 需要管理员权限
    """
    global fake_products_db_next_id
    new_product = product.dict()
    new_product["id"] = fake_products_db_next_id
    fake_products_db_next_id += 1
    fake_products_db.append(new_product)
    return new_product

@app.put(
    "/api/products/{product_id}",
    response_model=Product,
    summary="更新商品",
    description="根据商品ID更新商品信息",
    dependencies=[Depends(verify_admin_token)]
)
async def update_product(product_id: int, product: ProductUpdate):
    """
    更新商品:
    
    - **product_id**: 商品的唯一标识符
    
    请求体:
    - **name**: 商品名称 (可选)
    - **price**: 商品价格 (可选)
    - **category**: 商品分类 (可选)
    
    返回:
    - 更新后的商品信息
    
    异常:
    - 404: 商品未找到
    
    权限:
    - 需要管理员权限
    """
    for index, existing_product in enumerate(fake_products_db):
        if existing_product["id"] == product_id:
            update_data = product.dict(exclude_unset=True)
            updated_product = fake_products_db[index] | update_data
            fake_products_db[index] = updated_product
            return updated_product
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="商品未找到"
    )

@app.delete(
    "/api/products/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除商品",
    description="根据商品ID删除商品",
    dependencies=[Depends(verify_admin_token)]
)
async def delete_product(product_id: int):
    """
    删除商品:
    
    - **product_id**: 商品的唯一标识符
    
    异常:
    - 404: 商品未找到
    
    权限:
    - 需要管理员权限
    """
    for index, product in enumerate(fake_products_db):
        if product["id"] == product_id:
            fake_products_db.pop(index)
            return
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="商品未找到"
    )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```