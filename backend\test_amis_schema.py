"""
测试amis schema生成
"""
import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

BASE_URL = "http://localhost:5000"

def get_developer_token():
    """获取开发者token"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/developer",
            json={"password": "AILF_DEV_2024_SECURE"},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data["data"]["token"]
        else:
            print(f"❌ 获取token失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取token异常: {e}")
        return None

def test_amis_schema_generation(token):
    """测试amis schema生成"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 测试火车票预订相关的命令
        test_commands = [
            "我要买火车票",
            "帮我订火车票",
            "查询火车票",
            "预订车票"
        ]
        
        for command in test_commands:
            print(f"\n🎯 测试命令: '{command}'")
            print("=" * 50)
            
            response = requests.post(
                f"{BASE_URL}/api/command",
                json={"command": command},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 命令处理成功")
                
                # 显示意图分析结果
                if data.get('success') and data.get('data'):
                    response_data = data['data']
                    
                    if 'intent' in response_data:
                        intent = response_data['intent']
                        print(f"🧠 意图分析:")
                        print(f"   类型: {intent.get('intent_type')}")
                        print(f"   实体: {intent.get('entity')}")
                        print(f"   动作: {intent.get('action')}")
                        print(f"   置信度: {intent.get('confidence')}")
                    
                    if 'response_text' in response_data:
                        print(f"💬 响应文本: {response_data['response_text']}")
                    
                    # 显示生成的amis schema
                    if 'schema' in response_data and response_data['schema']:
                        schema = response_data['schema']
                        print(f"📄 生成的amis schema:")
                        print(json.dumps(schema, ensure_ascii=False, indent=2))
                    else:
                        print(f"⚠️ 未生成amis schema")
                        
                    # 显示匹配的工作流信息
                    if 'matched_workflow' in response_data:
                        workflow = response_data['matched_workflow']
                        print(f"🎯 匹配的工作流:")
                        print(f"   名称: {workflow.get('name', workflow.get('label', '未知'))}")
                        print(f"   业务场景: {workflow.get('business_scenario')}")
                        print(f"   节点数量: {len(workflow.get('nodes', []))}")
                
            else:
                print(f"❌ 命令处理失败: {response.status_code} - {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 AILF amis Schema 生成测试工具")
    print("=" * 60)
    
    # 1. 获取开发者token
    print("1️⃣ 获取开发者token...")
    token = get_developer_token()
    if not token:
        print("❌ 无法获取开发者token，测试终止")
        return 1
    
    print(f"✅ 获取token成功")
    
    # 2. 测试amis schema生成
    print("\n2️⃣ 测试amis schema生成...")
    if not test_amis_schema_generation(token):
        print("❌ amis schema生成测试失败")
        return 1
    
    print("\n🎉 所有测试完成！")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
