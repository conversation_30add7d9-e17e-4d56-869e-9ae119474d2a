/**
 * 开发者认证API服务
 */

import { apiClient } from '../common/apiClient';
import type {
  DeveloperAuthRequest,
  DeveloperAuthResponse,
  TokenVerifyRequest,
  TokenVerifyResponse,
} from '../../types/developer/auth';

export const authAPI = {
  /**
   * 开发者认证
   * POST /api/auth/developer
   */
  async authenticate(request: DeveloperAuthRequest): Promise<DeveloperAuthResponse> {
    const response = await apiClient.post<DeveloperAuthResponse['data']>(
      '/api/auth/developer',
      request
    );
    return response;
  },

  /**
   * 验证开发者令牌
   * POST /api/auth/developer/verify-token
   */
  async verifyToken(request: TokenVerifyRequest): Promise<TokenVerifyResponse> {
    const response = await apiClient.post<TokenVerifyResponse['data']>(
      '/api/auth/developer/verify-token',
      request
    );
    return response;
  },

  /**
   * 设置认证令牌到API客户端
   */
  setAuthToken(token: string | null) {
    apiClient.setToken(token);
  },

  /**
   * 获取当前认证令牌
   */
  getAuthToken(): string | null {
    return apiClient.getToken();
  },
};

export default authAPI;
