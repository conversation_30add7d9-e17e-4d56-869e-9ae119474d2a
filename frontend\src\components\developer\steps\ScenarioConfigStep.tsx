/**
 * 场景配置步骤组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Select, 
  Button, 
  Card, 
  Row, 
  Col, 
  Space, 
  Typography, 
  Alert,
  Tag,
  Divider
} from 'antd';
import { 
  ShoppingCartOutlined,
  MedicineBoxOutlined,
  CoffeeOutlined,
  BookOutlined,
  CarOutlined,
  BankOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useScenarioConfig } from '../../../hooks/developer/useScenarioConfig';
import type { ScenarioFormData, ScenarioType } from '../../../types/developer/scenario';
import { SCENARIO_TYPES } from '../../../types/developer/scenario';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface ScenarioConfigStepProps {
  onSuccess: () => void;
  onBack: () => void;
}

const ScenarioConfigStep: React.FC<ScenarioConfigStepProps> = ({ onSuccess, onBack }) => {
  const [form] = Form.useForm();
  const { scenarioState, saveScenario, clearError } = useScenarioConfig();
  const [selectedType, setSelectedType] = useState<ScenarioType>('ecommerce');
  const [customUsers, setCustomUsers] = useState<string[]>([]);
  const [customFeatures, setCustomFeatures] = useState<string[]>([]);
  const [newUser, setNewUser] = useState('');
  const [newFeature, setNewFeature] = useState('');

  // 图标映射
  const iconMap = {
    'shopping-cart': <ShoppingCartOutlined />,
    'medicine-box': <MedicineBoxOutlined />,
    'coffee': <CoffeeOutlined />,
    'book': <BookOutlined />,
    'car': <CarOutlined />,
    'bank': <BankOutlined />,
    'setting': <SettingOutlined />,
  };

  // 初始化表单数据
  useEffect(() => {
    if (scenarioState.currentScenario) {
      const scenario = scenarioState.currentScenario;
      form.setFieldsValue({
        name: scenario.name,
        type: scenario.type,
        description: scenario.description,
        business_domain: scenario.config.business_domain,
      });
      setSelectedType(scenario.type);
      setCustomUsers(scenario.config.target_users);
      setCustomFeatures(scenario.config.key_features);
    }
  }, [scenarioState.currentScenario, form]);

  const handleSubmit = async (values: any) => {
    clearError();
    
    const formData: ScenarioFormData = {
      name: values.name,
      type: selectedType,
      description: values.description || '',
      business_domain: values.business_domain,
      target_users: customUsers,
      key_features: customFeatures,
      custom_settings: {},
    };

    const result = await saveScenario(formData);
    
    if (result.success) {
      onSuccess();
    }
  };

  const handleTypeChange = (type: ScenarioType) => {
    setSelectedType(type);
    const selectedOption = SCENARIO_TYPES.find(t => t.value === type);
    if (selectedOption) {
      // 预设一些默认值
      setCustomUsers([]);
      setCustomFeatures([]);
      form.setFieldsValue({
        business_domain: selectedOption.label,
      });
    }
  };

  const addUser = () => {
    if (newUser.trim() && !customUsers.includes(newUser.trim())) {
      setCustomUsers([...customUsers, newUser.trim()]);
      setNewUser('');
    }
  };

  const removeUser = (user: string) => {
    setCustomUsers(customUsers.filter(u => u !== user));
  };

  const addFeature = () => {
    if (newFeature.trim() && !customFeatures.includes(newFeature.trim())) {
      setCustomFeatures([...customFeatures, newFeature.trim()]);
      setNewFeature('');
    }
  };

  const removeFeature = (feature: string) => {
    setCustomFeatures(customFeatures.filter(f => f !== feature));
  };

  const selectedTypeOption = SCENARIO_TYPES.find(t => t.value === selectedType);

  return (
    <div className="scenario-config-step" style={{ padding: '40px', maxWidth: 1000, margin: '0 auto' }}>
      <div style={{ textAlign: 'center', marginBottom: 40 }}>
        <Title level={2} style={{ color: '#1d1d1f', marginBottom: 8 }}>
          场景配置
        </Title>
        <Text type="secondary" style={{ fontSize: 16 }}>
          配置您的业务场景，定义系统的核心功能和用户群体
        </Text>
      </div>

      {scenarioState.error && (
        <Alert
          message="配置失败"
          description={scenarioState.error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 24 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Row gutter={24}>
          <Col span={24}>
            <Card title="基本信息" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="name"
                    label="场景名称"
                    rules={[
                      { required: true, message: '请输入场景名称' },
                      { min: 2, message: '场景名称至少2个字符' },
                    ]}
                  >
                    <Input 
                      placeholder="例如：电商管理系统"
                      size="large"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="business_domain"
                    label="业务领域"
                    rules={[{ required: true, message: '请输入业务领域' }]}
                  >
                    <Input 
                      placeholder="例如：电子商务"
                      size="large"
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="description"
                label="场景描述"
              >
                <TextArea 
                  placeholder="描述您的业务场景..."
                  rows={3}
                  size="large"
                />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={24}>
            <Card title="场景类型" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                {SCENARIO_TYPES.map(type => (
                  <Col span={8} key={type.value} style={{ marginBottom: 16 }}>
                    <Card
                      hoverable
                      className={selectedType === type.value ? 'selected-type' : ''}
                      onClick={() => handleTypeChange(type.value)}
                      style={{
                        border: selectedType === type.value ? '2px solid #007aff' : '1px solid #d9d9d9',
                        backgroundColor: selectedType === type.value ? 'rgba(0, 122, 255, 0.05)' : 'white',
                      }}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 32, marginBottom: 8, color: '#007aff' }}>
                          {iconMap[type.icon as keyof typeof iconMap]}
                        </div>
                        <Title level={5} style={{ marginBottom: 4 }}>
                          {type.label}
                        </Title>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {type.description}
                        </Text>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
              
              {selectedTypeOption && (
                <div style={{ marginTop: 16, padding: 16, backgroundColor: '#f8f9fa', borderRadius: 8 }}>
                  <Text strong>推荐功能：</Text>
                  <div style={{ marginTop: 8 }}>
                    {selectedTypeOption.features.map(feature => (
                      <Tag key={feature} color="blue" style={{ margin: '2px 4px' }}>
                        {feature}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Card title="目标用户" style={{ marginBottom: 24 }}>
              <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
                <Input
                  placeholder="添加用户角色"
                  value={newUser}
                  onChange={(e) => setNewUser(e.target.value)}
                  onPressEnter={addUser}
                />
                <Button type="primary" icon={<PlusOutlined />} onClick={addUser}>
                  添加
                </Button>
              </Space.Compact>
              
              <div>
                {customUsers.map(user => (
                  <Tag
                    key={user}
                    closable
                    onClose={() => removeUser(user)}
                    style={{ margin: '4px 4px' }}
                  >
                    {user}
                  </Tag>
                ))}
              </div>
            </Card>
          </Col>
          
          <Col span={12}>
            <Card title="关键功能" style={{ marginBottom: 24 }}>
              <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
                <Input
                  placeholder="添加功能模块"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  onPressEnter={addFeature}
                />
                <Button type="primary" icon={<PlusOutlined />} onClick={addFeature}>
                  添加
                </Button>
              </Space.Compact>
              
              <div>
                {customFeatures.map(feature => (
                  <Tag
                    key={feature}
                    closable
                    onClose={() => removeFeature(feature)}
                    style={{ margin: '4px 4px' }}
                  >
                    {feature}
                  </Tag>
                ))}
              </div>
            </Card>
          </Col>
        </Row>

        <div style={{ textAlign: 'center', marginTop: 32 }}>
          <Space size="large">
            <Button size="large" onClick={onBack}>
              上一步
            </Button>
            <Button 
              type="primary" 
              size="large" 
              htmlType="submit"
              loading={scenarioState.isLoading}
            >
              {scenarioState.isLoading ? '保存中...' : '保存并继续'}
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default ScenarioConfigStep;
