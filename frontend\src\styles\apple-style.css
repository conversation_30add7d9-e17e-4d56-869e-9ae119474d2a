/* 苹果风格样式 - 简约优雅 */

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}



@keyframes siri-background-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

#root {
  height: 100vh;
  width: 100vw;
}

/* Siri主容器 */
.App {
  height: 100vh;
  width: 100vw;
  background: transparent;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.siri-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 800px;
  height: 100vh;
  padding: 40px 20px;
  text-align: center;
  position: relative;
}

/* 标题区域 */
.ailf-header {
  margin-bottom: 60px;
  animation: fadeInUp 1.5s ease-out;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 开发者配置按钮 */
.setup-btn {
  position: absolute;
  top: 0;
  right: -120px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
}

.setup-btn:hover {
  background: rgba(79, 172, 254, 0.2);
  border-color: rgba(79, 172, 254, 0.4);
  color: #4facfe;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
}

.ailf-title {
  font-size: 56px;
  font-weight: 600;
  letter-spacing: -0.005em;
  color: #ffffff;
  margin-bottom: 20px;
  line-height: 1.07;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 1068px) {
  .ailf-title {
    font-size: 48px;
  }
}

@media (max-width: 734px) {
  .ailf-title {
    font-size: 32px;
    line-height: 1.125;
  }
}

/* AILF 标题 - 清晰美化版 */
.app-name {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2.8rem;
  font-weight: 300; /* 增加字重，提升清晰度 */
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 12px;
  z-index: 10;
  /* 简化阴影，保持清晰度 */
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  /* 确保文字渲染清晰 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  animation: breathe 4s ease-in-out infinite;
}

.app-name::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.app-name::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 255, 255, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

/* 响应区域 */
.response-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-height: 60vh;
  overflow-y: auto;
  padding: 20px 0;
}

.ai-response {
  font-size: 2.2rem;
  font-weight: 300;
  line-height: 1.4;
  margin-bottom: 30px;
  opacity: 0.95;
  text-align: center;
  max-width: 700px;
  animation: fadeInUp 0.6s ease-out;
}

.user-transcript {
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  margin-bottom: 20px;
  animation: fadeInUp 0.4s ease-out;
}

/* 占位文本样式已移至 text.css 文件中统一管理 */

.placeholder-text br {
  margin: 10px 0;
}

/* 错误消息 */
.error-message {
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.3);
  color: #FF453A;
  padding: 16px 24px;
  border-radius: 12px;
  margin: 20px 0;
  font-weight: 400;
  font-size: 1.1rem;
  text-align: center;
  max-width: 500px;
  animation: shake 0.5s ease-in-out;
}

/* 麦克风按钮容器 */
.mic-button-container {
  padding: 20px 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* 苹果毛玻璃麦克风按钮 - 暗色主题 */
.mic-button {
  /* 基础样式 */
  width: 160px;
  height: 160px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  /* 毛玻璃背景 */
  background:
    linear-gradient(135deg,
      rgba(28, 28, 30, 0.95) 0%,
      rgba(44, 44, 46, 0.9) 50%,
      rgba(28, 28, 30, 0.95) 100%
    );
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);

  /* 边框效果 */
  border: 1px solid rgba(255, 255, 255, 0.08);

  /* 阴影系统 */
  box-shadow:
    /* 主要阴影 */
    0 24px 48px rgba(0, 0, 0, 0.4),
    /* 近距离阴影 */
    0 8px 16px rgba(0, 0, 0, 0.3),
    /* 内部高光 */
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    /* 内部阴影 */
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);

  /* 动画过渡 */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* 图标颜色 */
  color: rgba(255, 255, 255, 0.85);
}

/* 外圈光晕效果 */
.mic-button::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background:
    radial-gradient(circle,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 60%
    );
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

/* 内部纹理效果 */
.mic-button::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 30%,
      rgba(255, 255, 255, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: 1;
}

/* Hover状态 */
.mic-button:hover {
  /* 背景变亮 */
  background:
    linear-gradient(135deg,
      rgba(48, 48, 52, 0.95) 0%,
      rgba(64, 64, 68, 0.9) 50%,
      rgba(48, 48, 52, 0.95) 100%
    );

  /* 变换效果 */
  transform: scale(1.05) translateY(-4px);

  /* 边框增强 */
  border-color: rgba(255, 255, 255, 0.15);

  /* 阴影增强 */
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.5),
    0 16px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);

  /* 图标变亮 */
  color: rgba(255, 255, 255, 1);
}

.mic-button:hover::before {
  opacity: 1;
}

/* 按下状态 */
.mic-button:active {
  transform: scale(0.98) translateY(-2px);
  transition: all 0.1s ease;
}

/* 监听状态 - Siri蓝色 */
.mic-button.listening {
  background:
    linear-gradient(135deg,
      rgba(0, 122, 255, 0.9) 0%,
      rgba(0, 100, 210, 0.95) 50%,
      rgba(0, 122, 255, 0.9) 100%
    );

  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 1);

  box-shadow:
    0 0 0 0 rgba(0, 122, 255, 0.6),
    0 24px 48px rgba(0, 122, 255, 0.3),
    0 8px 16px rgba(0, 122, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);

  animation: siriPulse 2.5s ease-in-out infinite;
}

/* 监听状态的外圈动画 */
.mic-button.listening::before {
  background:
    radial-gradient(circle,
      rgba(0, 122, 255, 0.4) 0%,
      rgba(0, 122, 255, 0.1) 40%,
      transparent 70%
    );
  opacity: 1;
  animation: siriRipple 2.5s ease-in-out infinite;
}

/* 图标样式 */
.mic-button svg {
  width: 56px;
  height: 56px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  z-index: 2;
  position: relative;
}

/* 苹果风格Siri动画效果 */
@keyframes siriPulse {
  0% {
    box-shadow:
      0 0 0 0 rgba(0, 122, 255, 0.6),
      0 24px 48px rgba(0, 122, 255, 0.3),
      0 8px 16px rgba(0, 122, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow:
      0 0 0 20px rgba(0, 122, 255, 0.1),
      0 32px 64px rgba(0, 122, 255, 0.4),
      0 12px 24px rgba(0, 122, 255, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
  100% {
    box-shadow:
      0 0 0 0 rgba(0, 122, 255, 0.6),
      0 24px 48px rgba(0, 122, 255, 0.3),
      0 8px 16px rgba(0, 122, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1);
  }
}

@keyframes siriRipple {
  0% {
    transform: scale(0.9);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes breathe {
  0%, 100% {
    opacity: 0.8;
    text-shadow:
      0 0 20px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(255, 255, 255, 0.2);
  }
  50% {
    opacity: 1;
    text-shadow:
      0 0 30px rgba(255, 255, 255, 0.6),
      0 0 60px rgba(255, 255, 255, 0.3),
      0 0 90px rgba(255, 255, 255, 0.1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200% 0;
    opacity: 0.3;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateX(-50%) scale(1.5);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .siri-container {
    padding: 30px 15px;
  }

  .app-name {
    font-size: 1.5rem;
    top: 40px;
  }

  .ai-response {
    font-size: 1.8rem;
  }

  .user-transcript {
    font-size: 1.2rem;
  }

  .placeholder-text {
    font-size: 1.5rem;
  }

  .mic-button {
    width: 100px;
    height: 100px;
  }

  .mic-button svg {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .app-name {
    font-size: 1.3rem;
    top: 30px;
  }

  .ai-response {
    font-size: 1.5rem;
  }

  .placeholder-text {
    font-size: 1.3rem;
  }

  .mic-button {
    width: 80px;
    height: 80px;
  }

  .mic-button svg {
    width: 32px;
    height: 32px;
  }
}





/* 更自然的金属呼吸动画 */
@keyframes naturalMetalBreathing {
  0%, 100% {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))
            drop-shadow(0 0 20px rgba(255, 255, 255, 0.1))
            brightness(1);
    transform: scale(1);
  }
  25% {
    filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.35))
            drop-shadow(0 0 25px rgba(255, 255, 255, 0.15))
            brightness(1.03);
    transform: scale(1.005);
  }
  50% {
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4))
            drop-shadow(0 0 30px rgba(255, 255, 255, 0.2))
            brightness(1.05);
    transform: scale(1.01);
  }
  75% {
    filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.35))
            drop-shadow(0 0 25px rgba(255, 255, 255, 0.15))
            brightness(1.03);
    transform: scale(1.005);
  }
}

/* 金属点点闪烁动画 */
@keyframes metalSparkle {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  75% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 苹果风格金属光泽移动 */
@keyframes appleMetalShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 苹果风格闪光扫过效果 */
@keyframes appleShimmerSweep {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 0;
  }
}

/* 苹果风格微妙悬浮 */
@keyframes subtleLevitation {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg);
  }
  50% {
    transform: translateY(-3px) rotateX(1deg);
  }
}

.ailf-subtitle {
  font-size: 28px;
  font-weight: 400;
  color: #a1a1a6;
  letter-spacing: 0.004em;
  line-height: 1.14;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* 用户信息栏 */
.user-info-bar {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 50px;
  padding: 8px 20px 8px 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.user-role {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  text-transform: capitalize;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-panel-btn,
.logout-btn {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(10px);
}

.admin-panel-btn:hover {
  background: rgba(79, 172, 254, 0.2);
  border-color: rgba(79, 172, 254, 0.4);
  color: #4facfe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.logout-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.4);
  color: #ff6b6b;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.admin-panel-btn:active,
.logout-btn:active {
  transform: scale(0.95);
}

/* 语音输入区域 */
.voice-input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  animation: fadeInUp 1.5s ease-out 0.8s both;
  margin-bottom: 40px;
}



/* 监听指示器 */
.listening-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: rgba(255, 255, 255, 0.8);
  animation: fadeIn 0.3s ease-out;
}

.listening-indicator p {
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* 苹果风格Siri波浪动画 */
.listening-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  height: 40px;
}

.wave {
  width: 3px;
  background: linear-gradient(to top,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.3) 100%);
  border-radius: 2px;
  animation: siriWave 1.4s ease-in-out infinite;
}

.wave:nth-child(1) { animation-delay: 0s; height: 20px; }
.wave:nth-child(2) { animation-delay: 0.1s; height: 30px; }
.wave:nth-child(3) { animation-delay: 0.2s; height: 40px; }
.wave:nth-child(4) { animation-delay: 0.3s; height: 30px; }
.wave:nth-child(5) { animation-delay: 0.4s; height: 20px; }

@keyframes siriWave {
  0%, 100% {
    transform: scaleY(0.3);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* 状态显示 */
.status-container {
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  animation: fadeInUp 1.5s ease-out 1.2s both;
  max-width: 500px;
  width: 100%;
}

.response-text {
  font-size: 15px;
  font-weight: 400;
  text-align: center;
  max-width: 450px;
  line-height: 1.5;
  animation: subtleFadeIn 0.5s ease-out;
}

.error-text {
  font-size: 15px;
  font-weight: 400;
  text-align: center;
  max-width: 450px;
  line-height: 1.5;
  animation: subtleFadeIn 0.5s ease-out;
}

/* 苹果风格测试按钮 */
.apple-test-button {
  position: relative;
  overflow: hidden;
  animation: subtleFadeIn 1.8s ease-out 1.5s both;
}

.apple-test-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.5s ease;
}

.apple-test-button:hover::before {
  left: 100%;
}

/* 苹果风格动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 苹果风格微妙淡入动画 */
@keyframes subtleFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 更温和的麦克风浮动动画 */
@keyframes gentleMicFloat {
  0%, 100% {
    transform: translateY(0px) translateZ(0);
  }
  25% {
    transform: translateY(-1px) translateZ(0);
  }
  50% {
    transform: translateY(-2px) translateZ(0);
  }
  75% {
    transform: translateY(-1px) translateZ(0);
  }
}

/* 背景浮动动画 */
@keyframes backgroundFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  33% {
    transform: translateY(-10px) rotate(0.3deg);
    opacity: 0.8;
  }
  66% {
    transform: translateY(5px) rotate(-0.2deg);
    opacity: 0.9;
  }
}

/* 微妙光晕动画 */
@keyframes gentleGlow {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
}

/* 更温和的宇宙呼吸动画 */
@keyframes gentleCosmicBreathing {
  0%, 100% {
    background-size: 100% 100%, 120% 120%, 110% 110%, 130% 130%, 140% 140%, 100% 100%;
    filter: brightness(1) contrast(1) saturate(1);
  }
  20% {
    background-size: 102% 102%, 122% 122%, 112% 112%, 132% 132%, 142% 142%, 102% 102%;
    filter: brightness(1.01) contrast(1.005) saturate(1.02);
  }
  40% {
    background-size: 104% 104%, 124% 124%, 114% 114%, 134% 134%, 144% 144%, 104% 104%;
    filter: brightness(1.02) contrast(1.01) saturate(1.04);
  }
  60% {
    background-size: 106% 106%, 126% 126%, 116% 116%, 136% 136%, 146% 146%, 106% 106%;
    filter: brightness(1.03) contrast(1.015) saturate(1.06);
  }
  80% {
    background-size: 104% 104%, 124% 124%, 114% 114%, 134% 134%, 144% 144%, 104% 104%;
    filter: brightness(1.02) contrast(1.01) saturate(1.04);
  }
}

@keyframes listeningPulse {
  0% {
    box-shadow: 
      0 0 0 0 rgba(255, 59, 48, 0.4),
      0 12px 40px rgba(255, 59, 48, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  70% {
    box-shadow: 
      0 0 0 20px rgba(255, 59, 48, 0),
      0 12px 40px rgba(255, 59, 48, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow: 
      0 0 0 0 rgba(255, 59, 48, 0),
      0 12px 40px rgba(255, 59, 48, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

/* 苹果风格生成界面 */
.amis-render-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(135deg,
      rgba(248, 249, 250, 0.98) 0%,
      rgba(255, 255, 255, 0.96) 100%);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: slideInFromBottom 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.amis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: rgba(248, 249, 250, 0.9);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.amis-content {
  flex: 1;
  padding: 24px;
  overflow: auto;
  color: #1d1d1f;
}

/* 苹果风格按钮 - 毛玻璃效果 */
.close-button, .generated-mic-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  position: relative;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.close-button {
  background:
    linear-gradient(135deg,
      rgba(255, 59, 48, 0.15) 0%,
      rgba(255, 59, 48, 0.1) 100%
    );
  color: #ff3b30;
  border-color: rgba(255, 59, 48, 0.2);
}

.close-button:hover {
  background:
    linear-gradient(135deg,
      rgba(255, 59, 48, 0.25) 0%,
      rgba(255, 59, 48, 0.2) 100%
    );
  transform: scale(1.08) translateY(-1px);
  border-color: rgba(255, 59, 48, 0.3);
  box-shadow:
    0 12px 24px rgba(255, 59, 48, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.generated-mic-button {
  background:
    linear-gradient(135deg,
      rgba(0, 122, 255, 0.15) 0%,
      rgba(0, 122, 255, 0.1) 100%
    );
  color: #007aff;
  border-color: rgba(0, 122, 255, 0.2);
  position: relative;
}

.generated-mic-button:hover {
  background:
    linear-gradient(135deg,
      rgba(0, 122, 255, 0.25) 0%,
      rgba(0, 122, 255, 0.2) 100%
    );
  transform: scale(1.08) translateY(-1px);
  border-color: rgba(0, 122, 255, 0.3);
  box-shadow:
    0 12px 24px rgba(0, 122, 255, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.generated-mic-button.listening {
  background:
    linear-gradient(135deg,
      rgba(0, 122, 255, 0.3) 0%,
      rgba(0, 122, 255, 0.25) 100%
    );
  color: #007aff;
  border-color: rgba(0, 122, 255, 0.4);
  animation: listeningPulse 2s ease-in-out infinite;
  box-shadow:
    0 0 0 0 rgba(0, 122, 255, 0.5),
    0 8px 16px rgba(0, 122, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.listening-text {
  position: absolute;
  top: -36px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  font-weight: 500;
  color: #007aff;
  white-space: nowrap;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 100%
    );
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid rgba(0, 122, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 小按钮的监听动画 */
@keyframes listeningPulse {
  0% {
    box-shadow:
      0 0 0 0 rgba(0, 122, 255, 0.5),
      0 8px 16px rgba(0, 122, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 0 0 8px rgba(0, 122, 255, 0.1),
      0 12px 24px rgba(0, 122, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }
  100% {
    box-shadow:
      0 0 0 0 rgba(0, 122, 255, 0.5),
      0 8px 16px rgba(0, 122, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

/* 语音不支持提示 */
.voice-unsupported {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.voice-unsupported p {
  margin: 8px 0;
  line-height: 1.5;
}

/* Siri风格彩色波浪动画容器 */
.siri-wave-container {
  position: fixed;
  bottom: 100px;
  left: 0;
  right: 0;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 10;
}

.siri-wave {
  position: relative;
  width: 100%;
  height: 100px;
  overflow: hidden;
}

/* 多层彩色波浪 */
.wave-layer {
  position: absolute;
  bottom: 50%;
  left: 0;
  width: 200%;
  height: 4px;
  border-radius: 2px;
  opacity: 0.8;
  transform-origin: center;
}

.wave-layer:nth-child(1) {
  background: linear-gradient(90deg,
    #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #feca57 100%);
  animation: waveMove 3s ease-in-out infinite, waveHeight1 2s ease-in-out infinite;
  animation-delay: 0s;
  height: 6px;
}

.wave-layer:nth-child(2) {
  background: linear-gradient(90deg,
    #a8e6cf 0%, #ff8b94 25%, #ffaaa5 50%, #ff677d 75%, #d4a4eb 100%);
  animation: waveMove 3.5s ease-in-out infinite reverse, waveHeight2 2.2s ease-in-out infinite;
  animation-delay: 0.3s;
  height: 5px;
}

.wave-layer:nth-child(3) {
  background: linear-gradient(90deg,
    #74b9ff 0%, #0984e3 25%, #6c5ce7 50%, #a29bfe 75%, #fd79a8 100%);
  animation: waveMove 4s ease-in-out infinite, waveHeight3 1.8s ease-in-out infinite;
  animation-delay: 0.6s;
  height: 4px;
}

.wave-layer:nth-child(4) {
  background: linear-gradient(90deg,
    #55efc4 0%, #00b894 25%, #00cec9 50%, #74b9ff 75%, #0984e3 100%);
  animation: waveMove 2.8s ease-in-out infinite reverse, waveHeight4 2.5s ease-in-out infinite;
  animation-delay: 0.9s;
  height: 7px;
}

.wave-layer:nth-child(5) {
  background: linear-gradient(90deg,
    #fdcb6e 0%, #e17055 25%, #d63031 50%, #74b9ff 75%, #0984e3 100%);
  animation: waveMove 3.2s ease-in-out infinite, waveHeight5 1.9s ease-in-out infinite;
  animation-delay: 1.2s;
  height: 5px;
}

/* 波浪移动动画 */
@keyframes waveMove {
  0% {
    transform: translateX(-50%) scaleY(1);
  }
  50% {
    transform: translateX(-25%) scaleY(1.2);
  }
  100% {
    transform: translateX(-50%) scaleY(1);
  }
}

/* 不同波浪的高度变化动画 */
@keyframes waveHeight1 {
  0%, 100% { transform: scaleY(1) scaleX(1); }
  25% { transform: scaleY(1.5) scaleX(0.8); }
  50% { transform: scaleY(0.8) scaleX(1.2); }
  75% { transform: scaleY(1.3) scaleX(0.9); }
}

@keyframes waveHeight2 {
  0%, 100% { transform: scaleY(1) scaleX(1); }
  30% { transform: scaleY(1.8) scaleX(0.7); }
  60% { transform: scaleY(0.6) scaleX(1.3); }
}

@keyframes waveHeight3 {
  0%, 100% { transform: scaleY(1) scaleX(1); }
  40% { transform: scaleY(2.2) scaleX(0.6); }
  80% { transform: scaleY(0.4) scaleX(1.4); }
}

@keyframes waveHeight4 {
  0%, 100% { transform: scaleY(1) scaleX(1); }
  20% { transform: scaleY(1.6) scaleX(0.8); }
  70% { transform: scaleY(0.7) scaleX(1.1); }
}

@keyframes waveHeight5 {
  0%, 100% { transform: scaleY(1) scaleX(1); }
  35% { transform: scaleY(1.9) scaleX(0.7); }
  65% { transform: scaleY(0.5) scaleX(1.3); }
}

/* 底部 SiriWave 动画样式 - 清晰版 */
.bottom-siri-wave {
  position: fixed;
  bottom: 120px; /* 调整位置，更居中 */
  left: 0;
  right: 0;
  width: 100%;
  height: 100px; /* 增加高度以容纳更大的波浪 */
  z-index: 10;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除模糊效果，保持清晰 */
  background: transparent;
  /* 添加平滑的显示/隐藏动画 */
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: siriWaveSlideIn 0.4s ease-out;
}

/* SiriWave 滑入动画 */
@keyframes siriWaveSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.bottom-siri-wave .siri-wave-container {
  width: 100%;
  height: 100%;
  margin: 0;
  position: relative;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-siri-wave .siri-wave-canvas {
  width: 100% !important;
  height: 100px !important;
  border-radius: 0 !important;
  background: transparent !important;
  backdrop-filter: none !important;
  border: none !important;
  box-shadow: none !important;
  /* 添加微妙的发光效果 */
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.1));
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ailf-title {
    font-size: 4.5rem;
  }

  .ailf-subtitle {
    font-size: 1.1rem;
  }

  .mic-button {
    width: 100px;
    height: 100px;
  }

  .mic-button svg {
    width: 36px;
    height: 36px;
  }

  .ailf-main-container {
    padding: 30px 20px;
    max-width: 500px;
  }

  .bottom-siri-wave {
    bottom: 100px;
    height: 80px;
  }

  .bottom-siri-wave .siri-wave-canvas {
    height: 80px !important;
  }
}

@media (max-width: 480px) {
  .ailf-title {
    font-size: 3.5rem;
  }

  .ailf-subtitle {
    font-size: 1rem;
  }

  .mic-button {
    width: 90px;
    height: 90px;
  }

  .mic-button svg {
    width: 32px;
    height: 32px;
  }

  .ailf-main-container {
    padding: 20px 15px;
    max-width: 400px;
  }

  .bottom-siri-wave {
    bottom: 80px;
    height: 60px;
  }

  .bottom-siri-wave .siri-wave-canvas {
    height: 60px !important;
  }
}

/* 右下角隐蔽菜单 */
.dev-menu-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.dev-menu-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(128, 128, 128, 0.3);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dev-menu-button:hover {
  background: rgba(128, 128, 128, 0.5);
  transform: scale(1.1);
}

.dev-menu-button svg {
  width: 20px;
  height: 20px;
  fill: rgba(255, 255, 255, 0.7);
  transition: fill 0.3s ease;
}

.dev-menu-button:hover svg {
  fill: rgba(255, 255, 255, 0.9);
}

.dev-menu-dropdown {
  position: absolute;
  bottom: 60px;
  right: 0;
  background: rgba(40, 40, 40, 0.95);
  border-radius: 12px;
  padding: 8px 0;
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dev-menu-container:hover .dev-menu-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dev-menu-item {
  display: block;
  width: 100%;
  padding: 12px 20px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  text-decoration: none;
}

.dev-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 1);
}

/* 动画 */
@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}