import React, { useEffect } from 'react';
import { render as renderAmis } from 'amis';
import '../styles/generated-interface.css';

interface GeneratedInterfaceProps {
  schema: any;
  amisKey: number;
  amisEnv: any;
  onClose: () => void;
  onToggleListening: () => void;
  isListening: boolean;
}

const GeneratedInterface: React.FC<GeneratedInterfaceProps> = ({
  schema,
  amisKey,
  amisEnv,
  onClose,
  onToggleListening,
  isListening
}) => {
  // 控制body样式，完全覆盖主页面样式
  useEffect(() => {
    if (schema) {
      // 保存原始样式
      const originalBodyStyle = {
        background: document.body.style.background,
        animation: document.body.style.animation,
        className: document.body.className
      };

      // 应用生成界面样式
      document.body.style.background = '#ffffff';
      document.body.style.animation = 'none';
      document.body.className = 'generated-interface-body';

      return () => {
        // 恢复原始样式
        document.body.style.background = originalBodyStyle.background;
        document.body.style.animation = originalBodyStyle.animation;
        document.body.className = originalBodyStyle.className;
      };
    }
  }, [schema]);

  if (!schema) return null;

  return (
    <div className="amis-render-area generated-interface" key={`amis-${amisKey}`} style={{
      position: 'fixed',
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      background: 'rgba(0, 0, 0, 0.8)',
      zIndex: 1000,
      overflow: 'auto'
    }}>
      <div className="amis-header" style={{
        position: 'sticky',
        top: '0',
        background: 'rgba(0, 0, 0, 0.9)',
        padding: '10px 20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        {/* 关闭按钮 */}
        <button className="close-button" onClick={onClose} style={{
          background: 'rgba(255, 255, 255, 0.1)',
          border: 'none',
          borderRadius: '50%',
          width: '40px',
          height: '40px',
          color: 'white',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>

        {/* 生成后的麦克风按钮 */}
        <button
          className={`generated-mic-button ${isListening ? 'listening' : ''}`}
          onClick={onToggleListening}
          aria-label={isListening ? '停止录音' : '继续语音输入'}
          title="继续语音输入新需求"
          style={{
            background: isListening ? 'rgba(255, 0, 0, 0.2)' : 'rgba(0, 122, 255, 0.2)',
            border: `2px solid ${isListening ? '#ff4444' : '#007AFF'}`,
            borderRadius: '50%',
            width: '50px',
            height: '50px',
            color: isListening ? '#ff4444' : '#007AFF',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.3s ease'
          }}
        >
          <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
          </svg>
          {isListening && <span className="listening-text" style={{
            marginLeft: '8px',
            fontSize: '12px'
          }}>正在听...</span>}
        </button>
      </div>
      
      <div className="amis-content" style={{
        minHeight: '400px',
        padding: '20px',
        borderRadius: '8px',
        margin: '20px'
      }}>
        <div style={{ marginBottom: '10px', color: 'black' }}>
          <strong>调试信息:</strong> Schema类型: {schema?.type}, 标题: {schema?.title}
        </div>
        {renderAmis(schema, {
          locale: 'zh-CN',
          theme: 'cxd'
        }, amisEnv)}
      </div>
    </div>
  );
};

export default GeneratedInterface;
