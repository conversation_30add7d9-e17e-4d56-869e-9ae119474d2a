import { apiClient } from '../common/apiClient';
import type {
  FormConfig,
  FormConfigCreateRequest,
  FormConfigUpdateRequest,
  FormRenderConfig,
  FormDataSubmission,
  FormValidationResult,
  FormFieldConfig,
  FormTemplate,
  FormStatistics
} from '../../types/developer/form';

// 表单配置API服务
export class FormAPI {
  private static baseURL = '/api/forms';

  // 创建表单配置
  static async createForm(data: FormConfigCreateRequest): Promise<FormConfig> {
    const response = await apiClient.post(this.baseURL, data);
    return (response.data as any).data.form;
  }

  // 获取所有表单配置
  static async getForms(params?: {
    entity?: string;
    page?: number;
    limit?: number;
  }): Promise<{ forms: FormConfig[]; total: number }> {
    const response = await apiClient.get(this.baseURL);
    return (response.data as any).data;
  }

  // 获取特定表单配置
  static async getForm(formId: string): Promise<FormConfig> {
    const response = await apiClient.get(`${this.baseURL}/${formId}`);
    return (response.data as any).data.form;
  }

  // 更新表单配置
  static async updateForm(formId: string, data: FormConfigUpdateRequest): Promise<FormConfig> {
    const response = await apiClient.put(`${this.baseURL}/${formId}`, data);
    return (response.data as any).data.form;
  }

  // 删除表单配置
  static async deleteForm(formId: string): Promise<void> {
    await apiClient.delete(`${this.baseURL}/${formId}`);
  }

  // 获取表单渲染配置（amis schema）
  static async getFormRenderConfig(
    formId: string,
    params?: {
      mode?: 'create' | 'edit' | 'view';
      record_id?: string;
    }
  ): Promise<FormRenderConfig> {
    const response = await apiClient.get(`${this.baseURL}/${formId}/render`);
    return (response.data as any).data;
  }

  // 提交表单数据
  static async submitFormData(formId: string, data: FormDataSubmission): Promise<any> {
    const response = await apiClient.post(`${this.baseURL}/${formId}/submit`, data);
    return (response.data as any).data;
  }

  // 获取表单数据用于编辑
  static async getFormData(formId: string, recordId: string): Promise<any> {
    const response = await apiClient.get(`${this.baseURL}/${formId}/data/${recordId}`);
    return (response.data as any).data;
  }

  // 验证表单配置
  static async validateFormConfig(data: {
    form_config: any;
    entity_id: string;
  }): Promise<FormValidationResult> {
    const response = await apiClient.post(`${this.baseURL}/validate`, data);
    return (response.data as any).data;
  }

  // 获取表单字段列表
  static async getFormFields(formId: string): Promise<{ fields: FormFieldConfig[]; total: number }> {
    const response = await apiClient.get(`${this.baseURL}/${formId}/fields`);
    return (response.data as any).data;
  }

  // 添加表单字段
  static async addFormField(formId: string, field: Omit<FormFieldConfig, 'id'>): Promise<FormFieldConfig> {
    const response = await apiClient.post(`${this.baseURL}/${formId}/fields`, field);
    return (response.data as any).data.field;
  }

  // 更新表单字段
  static async updateFormField(fieldId: string, data: Partial<FormFieldConfig>): Promise<FormFieldConfig> {
    const response = await apiClient.put(`${this.baseURL}/fields/${fieldId}`, data);
    return (response.data as any).data.field;
  }

  // 删除表单字段
  static async deleteFormField(fieldId: string): Promise<void> {
    await apiClient.delete(`${this.baseURL}/fields/${fieldId}`);
  }

  // 获取表单模板
  static async getFormTemplates(params?: {
    category?: string;
    entity_type?: string;
  }): Promise<FormTemplate[]> {
    const response = await apiClient.get(`${this.baseURL}/templates`);
    return (response.data as any).data.templates;
  }

  // 从模板创建表单
  static async createFormFromTemplate(templateId: string, data: {
    name: string;
    entity: string;
    description?: string;
  }): Promise<FormConfig> {
    const response = await apiClient.post(`${this.baseURL}/templates/${templateId}/create`, data);
    return (response.data as any).data.form;
  }

  // 获取表单统计信息
  static async getFormStatistics(formId?: string): Promise<FormStatistics> {
    const url = formId ? `${this.baseURL}/${formId}/statistics` : `${this.baseURL}/statistics`;
    const response = await apiClient.get(url);
    return (response.data as any).data;
  }

  // 导出表单配置
  static async exportForm(formId: string, format: 'json' | 'yaml' | 'xml' = 'json'): Promise<Blob> {
    const response = await apiClient.get(`${this.baseURL}/${formId}/export?format=${format}`);
    return response.data as Blob;
  }

  // 导入表单配置
  static async importForm(file: File): Promise<FormConfig> {
    const formData = new FormData();
    formData.append('file', file);
    const response = await apiClient.post(`${this.baseURL}/import`, formData);
    return (response.data as any).data.form;
  }

  // 复制表单配置
  static async duplicateForm(formId: string, data: {
    name: string;
    description?: string;
  }): Promise<FormConfig> {
    const response = await apiClient.post(`${this.baseURL}/${formId}/duplicate`, data);
    return (response.data as any).data.form;
  }

  // 预览表单
  static async previewForm(formConfig: any): Promise<FormRenderConfig> {
    const response = await apiClient.post(`${this.baseURL}/preview`, { form_config: formConfig });
    return (response.data as any).data;
  }
}