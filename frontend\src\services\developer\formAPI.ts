/**
 * 表单配置相关API服务
 */

import { apiClient } from '../common/apiClient';

// 表单配置相关类型定义
export interface FormField {
  id: string;
  entityField: string;
  displayType: string;
  label: string;
  placeholder?: string;
  required: boolean;
  readonly?: boolean;
  hidden?: boolean;
  validation?: {
    rules: string[];
    messages: Record<string, string>;
  };
}

export interface FormSection {
  id: string;
  title: string;
  collapsible: boolean;
  fields: FormField[];
}

export interface FormLayout {
  type: string;
  columns: number;
  spacing: number;
}

export interface FormPermission {
  role: string;
  actions: string[];
}

export interface FormConfig {
  id?: string;
  name: string;
  entity: string;
  description: string;
  layout: FormLayout;
  sections: FormSection[];
  permissions: FormPermission[];
  created_at?: string;
  updated_at?: string;
}

export interface FormListItem {
  id: string;
  name: string;
  entity: string;
  description: string;
  field_count: number;
  section_count: number;
  created_at: string;
  updated_at: string;
}

export interface FormRenderSchema {
  type: string;
  title: string;
  mode: string;
  api: {
    method: string;
    url: string;
  };
  body: any[];
  actions: any[];
}

export interface FormSubmitData {
  data: Record<string, any>;
  mode: string;
  record_id?: string;
}

export interface FormValidationResult {
  valid: boolean;
  errors?: string[];
  warnings?: string[];
  suggestions?: string[];
}

// API响应类型
interface APIResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface FormListResponse {
  forms: FormListItem[];
  total: number;
}

export interface FormRenderResponse {
  schema: FormRenderSchema;
  metadata: {
    form_id: string;
    entity: string;
    mode: string;
    version: string;
  };
}

export interface FormDataResponse {
  record: Record<string, any>;
  form_metadata: {
    form_id: string;
    entity: string;
    editable_fields: string[];
    readonly_fields: string[];
  };
}

/**
 * 表单配置API服务
 */
export const formAPI = {
  /**
   * 创建动态表单配置
   */
  async createForm(formData: Omit<FormConfig, 'id' | 'created_at' | 'updated_at'>): Promise<APIResponse<{ form: FormConfig }>> {
    return await apiClient.post<{ form: FormConfig }>('/api/forms', formData);
  },

  /**
   * 获取所有表单配置
   */
  async getForms(params?: {
    entity?: string;
    page?: number;
    limit?: number;
  }): Promise<APIResponse<FormListResponse>> {
    const queryParams = new URLSearchParams();
    if (params?.entity) queryParams.append('entity', params.entity);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/api/forms${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return await apiClient.get<FormListResponse>(url);
  },

  /**
   * 获取特定表单配置
   */
  async getForm(formId: string): Promise<APIResponse<{ form: FormConfig }>> {
    return await apiClient.get<{ form: FormConfig }>(`/api/forms/${formId}`);
  },

  /**
   * 更新表单配置
   */
  async updateForm(formId: string, formData: Partial<FormConfig>): Promise<APIResponse<{ form: FormConfig }>> {
    return await apiClient.put<{ form: FormConfig }>(`/api/forms/${formId}`, formData);
  },

  /**
   * 删除表单配置
   */
  async deleteForm(formId: string): Promise<APIResponse<{ form_id: string; deleted_at: string }>> {
    return await apiClient.delete<{ form_id: string; deleted_at: string }>(`/api/forms/${formId}`);
  },

  /**
   * 获取表单渲染配置（amis schema）
   */
  async getFormRender(formId: string, params?: {
    mode?: string;
    record_id?: string;
  }): Promise<APIResponse<FormRenderResponse>> {
    const queryParams = new URLSearchParams();
    if (params?.mode) queryParams.append('mode', params.mode);
    if (params?.record_id) queryParams.append('record_id', params.record_id);

    const url = `/api/forms/${formId}/render${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return await apiClient.get<FormRenderResponse>(url);
  },

  /**
   * 提交表单数据
   */
  async submitForm(formId: string, submitData: FormSubmitData): Promise<APIResponse<{ record: any; form_id: string; mode: string }>> {
    return await apiClient.post<{ record: any; form_id: string; mode: string }>(`/api/forms/${formId}/submit`, submitData);
  },

  /**
   * 获取表单数据用于编辑
   */
  async getFormData(formId: string, recordId: string): Promise<APIResponse<FormDataResponse>> {
    return await apiClient.get<FormDataResponse>(`/api/forms/${formId}/data/${recordId}`);
  },

  /**
   * 验证表单配置
   */
  async validateForm(data: {
    form_config: Omit<FormConfig, 'id' | 'created_at' | 'updated_at'>;
    entity_id: string;
  }): Promise<APIResponse<FormValidationResult>> {
    return await apiClient.post<FormValidationResult>('/api/forms/validate', data);
  },

  /**
   * 获取表单字段列表
   */
  async getFormFields(formId: string): Promise<APIResponse<{ fields: FormField[]; total: number }>> {
    return await apiClient.get<{ fields: FormField[]; total: number }>(`/api/forms/${formId}/fields`);
  },

  /**
   * 添加表单字段
   */
  async addFormField(formId: string, fieldData: Omit<FormField, 'id'>): Promise<APIResponse<{ field: FormField }>> {
    return await apiClient.post<{ field: FormField }>(`/api/forms/${formId}/fields`, fieldData);
  },

  /**
   * 更新表单字段
   */
  async updateFormField(fieldId: string, fieldData: Partial<FormField>): Promise<APIResponse<{ field: FormField }>> {
    return await apiClient.put<{ field: FormField }>(`/api/forms/fields/${fieldId}`, fieldData);
  },

  /**
   * 删除表单字段
   */
  async deleteFormField(fieldId: string): Promise<APIResponse<{ field_id: string; deleted_at: string }>> {
    return await apiClient.delete<{ field_id: string; deleted_at: string }>(`/api/forms/fields/${fieldId}`);
  }
};

// 表单显示类型配置
export const FORM_DISPLAY_TYPES = {
  input: { label: '普通输入框', applicable: ['text', 'number', 'email', 'phone'] },
  textarea: { label: '多行文本', applicable: ['text'] },
  select: { label: '下拉选择', applicable: ['select'] },
  radio: { label: '单选按钮', applicable: ['select'] },
  checkbox: { label: '复选框', applicable: ['multiselect', 'boolean'] },
  switch: { label: '开关', applicable: ['boolean'] },
  'date-picker': { label: '日期选择', applicable: ['date', 'datetime'] },
  upload: { label: '文件上传', applicable: ['image', 'file'] },
  'rich-text': { label: '富文本编辑器', applicable: ['text'] }
};

// 表单布局类型配置
export const FORM_LAYOUT_TYPES = {
  vertical: { label: '垂直布局', description: '字段较少的简单表单' },
  horizontal: { label: '水平布局', description: '标签和输入框水平排列' },
  grid: { label: '网格布局', description: '多列显示，充分利用空间' },
  inline: { label: '内联布局', description: '字段在同一行显示' },
  tabs: { label: '标签页布局', description: '字段较多需要分组显示' }
};

// 验证规则配置
export const VALIDATION_RULES = {
  // 通用验证规则
  required: { label: '必填验证', hasValue: false },
  min: { label: '最小值验证', hasValue: true },
  max: { label: '最大值验证', hasValue: true },
  minLength: { label: '最小长度验证', hasValue: true },
  maxLength: { label: '最大长度验证', hasValue: true },
  
  // 特定类型验证规则
  email: { label: '邮箱格式验证', hasValue: false },
  phone: { label: '电话号码格式验证', hasValue: false },
  numeric: { label: '数字格式验证', hasValue: false },
  url: { label: 'URL格式验证', hasValue: false },
  date: { label: '日期格式验证', hasValue: false }
};

// 权限操作配置
export const PERMISSION_ACTIONS = {
  create: '创建新记录',
  read: '查看记录',
  update: '更新记录',
  delete: '删除记录',
  export: '导出数据',
  import: '导入数据'
};
