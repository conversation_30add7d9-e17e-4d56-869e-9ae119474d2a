"""
初始化工作流数据脚本
将示例工作流保存到数据库
"""
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import get_db_session, init_database
from app.models.workflow import WorkflowDBModel, WorkflowStatus
from app.schemas.workflow import WorkflowCreateRequest, WorkflowNodeCreateRequest
from app.services.workflow_service import workflow_service


def load_workflow_from_json(file_path: str) -> dict:
    """从JSON文件加载工作流配置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载工作流文件失败: {e}")
        return None


def save_workflow_to_database(workflow_data: dict) -> bool:
    """将工作流数据保存到数据库"""
    try:
        # 转换为请求对象
        request = WorkflowCreateRequest(**workflow_data)
        
        # 调用服务保存
        result = workflow_service.create_workflow(request)
        
        if result["success"]:
            print(f"✅ 工作流 '{workflow_data['name']}' 保存成功")
            print(f"   工作流ID: {result['data']['workflow']['id']}")
            print(f"   节点数量: {result['data']['workflow']['node_count']}")
            return True
        else:
            print(f"❌ 工作流保存失败: {result['details']}")
            return False
            
    except Exception as e:
        print(f"❌ 保存工作流到数据库失败: {e}")
        return False


def check_workflow_exists(workflow_name: str) -> bool:
    """检查工作流是否已存在"""
    try:
        with get_db_session() as db:
            existing = db.query(WorkflowDBModel).filter(
                WorkflowDBModel.name == workflow_name
            ).first()
            return existing is not None
    except Exception as e:
        print(f"❌ 检查工作流是否存在失败: {e}")
        return False


def init_sample_workflows():
    """初始化示例工作流"""
    print("🚀 开始初始化示例工作流...")
    
    # 确保数据库已初始化
    if not init_database():
        print("❌ 数据库初始化失败")
        return False
    
    # 示例工作流文件路径
    examples_dir = project_root / "examples"
    workflow_files = [
        "train_booking_workflow.json"
    ]
    
    success_count = 0
    total_count = len(workflow_files)
    
    for workflow_file in workflow_files:
        file_path = examples_dir / workflow_file
        
        if not file_path.exists():
            print(f"⚠️ 工作流文件不存在: {file_path}")
            continue
        
        print(f"\n📄 处理工作流文件: {workflow_file}")
        
        # 加载工作流数据
        workflow_data = load_workflow_from_json(str(file_path))
        if not workflow_data:
            continue
        
        # 检查是否已存在
        if check_workflow_exists(workflow_data["name"]):
            print(f"⚠️ 工作流 '{workflow_data['name']}' 已存在，跳过")
            continue
        
        # 保存到数据库
        if save_workflow_to_database(workflow_data):
            success_count += 1
    
    print(f"\n📊 工作流初始化完成:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   失败: {total_count - success_count}/{total_count}")
    
    return success_count == total_count


def list_workflows_in_database():
    """列出数据库中的所有工作流"""
    try:
        with get_db_session() as db:
            workflows = db.query(WorkflowDBModel).all()
            
            if not workflows:
                print("📋 数据库中没有工作流")
                return
            
            print(f"\n📋 数据库中的工作流 (共 {len(workflows)} 个):")
            for workflow in workflows:
                print(f"   • {workflow.name}")
                print(f"     ID: {workflow.id}")
                print(f"     业务场景: {workflow.business_scenario}")
                print(f"     状态: {workflow.status.value}")
                print(f"     节点数: {workflow.node_count}")
                print(f"     创建时间: {workflow.created_at}")
                print()
                
    except Exception as e:
        print(f"❌ 列出工作流失败: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("🔧 AILF 工作流数据初始化工具")
    print("=" * 60)
    
    # 初始化示例工作流
    success = init_sample_workflows()
    
    # 列出所有工作流
    list_workflows_in_database()
    
    if success:
        print("✅ 所有工作流初始化成功！")
        return 0
    else:
        print("⚠️ 部分工作流初始化失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
