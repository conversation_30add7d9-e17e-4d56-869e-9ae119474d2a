"""
认证模块API路由
严格按照API文档实现开发者认证和令牌验证接口
"""
from fastapi import APIRouter, HTTPException, Header
from typing import Optional
from app.schemas.auth import (
    DeveloperAuthRequest,
    DeveloperAuthResponse,
    TokenVerifyRequest,
    TokenVerifyResponse,
    APIResponse,
    ErrorResponse,
    ErrorData
)
from app.services.auth_service import auth_service

router = APIRouter(prefix="/auth", tags=["认证模块"])


@router.post(
    "/developer",
    response_model=DeveloperAuthResponse,
    summary="开发者认证",
    description="开发者使用密码进行身份认证，获取访问令牌"
)
async def authenticate_developer(request: DeveloperAuthRequest):
    """
    开发者认证接口
    
    - **password**: 开发者密码
    
    返回JWT访问令牌和相关信息
    """
    # 验证请求参数
    if not request.password:
        raise HTTPException(
            status_code=400,
            detail={
                "code": 400,
                "message": "请求参数错误",
                "data": {
                    "error": "validation_error",
                    "details": {
                        "password": ["密码不能为空"]
                    }
                }
            }
        )
    
    # 调用认证服务
    result = auth_service.authenticate_developer(request.password)
    
    if result["success"]:
        # 认证成功
        return DeveloperAuthResponse(
            code=200,
            message="认证成功",
            data=result["data"]
        )
    else:
        # 认证失败
        raise HTTPException(
            status_code=401,
            detail={
                "code": 401,
                "message": "密码错误",
                "data": {
                    "error": result["error"],
                    "details": result["details"]
                }
            }
        )


@router.post(
    "/developer/verify-token",
    response_model=TokenVerifyResponse,
    summary="验证开发者令牌",
    description="验证开发者令牌的有效性，用于检查令牌是否过期或被撤销"
)
async def verify_developer_token(
    request: TokenVerifyRequest,
    authorization: Optional[str] = Header(None)
):
    """
    验证开发者令牌接口
    
    - **token**: 需要验证的JWT令牌
    - **Authorization**: 请求头中的Bearer token（可选）
    
    返回令牌验证结果
    """
    # 验证请求参数
    if not request.token:
        raise HTTPException(
            status_code=400,
            detail={
                "code": 400,
                "message": "请求参数错误",
                "data": {
                    "error": "validation_error",
                    "details": {
                        "token": ["令牌格式不正确"]
                    }
                }
            }
        )
    
    # 调用令牌验证服务
    result = auth_service.verify_token(request.token)
    
    if result["success"]:
        # 验证成功
        return TokenVerifyResponse(
            code=200,
            message="令牌有效",
            data=result["data"]
        )
    else:
        # 验证失败
        raise HTTPException(
            status_code=401,
            detail={
                "code": 401,
                "message": "令牌无效",
                "data": result["data"].model_dump()
            }
        )
