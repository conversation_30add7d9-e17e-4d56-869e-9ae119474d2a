"""
业务API路由 - 带权限验证的示例
展示如何在系统运行时进行真正的角色权限验证
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.security import HTTPAuthorizationCredentials
from typing import Optional, List, Dict, Any

from app.core.permissions import (
    require_permission, 
    require_any_permission, 
    require_role_level,
    security
)

router = APIRouter()


# 客户管理API - 需要customers:read权限
@router.get("/customers")
@require_permission("customers", "read")
async def get_customers(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None,
    permission_context: dict = None
):
    """
    获取客户列表
    
    需要权限: customers:read
    适用角色: 前台、教练、销售经理等
    """
    return {
        "code": 200,
        "message": "获取客户列表成功",
        "data": {
            "customers": [
                {
                    "id": "customer_001",
                    "name": "张三",
                    "phone": "13800138001",
                    "membership": "VIP",
                    "join_date": "2024-01-15"
                },
                {
                    "id": "customer_002", 
                    "name": "李四",
                    "phone": "13800138002",
                    "membership": "普通",
                    "join_date": "2024-02-20"
                }
            ],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 2
            },
            "access_info": {
                "user": current_user["name"],
                "user_id": current_user["user_id"],
                "permission": permission_context["permission"]["permission_name"],
                "granted_by": permission_context["permission"]["granted_by"]
            }
        }
    }


# 创建客户API - 需要customers:create权限
@router.post("/customers")
@require_permission("customers", "create")
async def create_customer(
    customer_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None
):
    """
    创建新客户
    
    需要权限: customers:create
    适用角色: 前台、销售经理等
    """
    return {
        "code": 201,
        "message": "客户创建成功",
        "data": {
            "customer": {
                "id": "customer_new_001",
                "name": customer_data.get("name", "新客户"),
                "created_by": current_user["name"],
                "created_at": "2024-01-20T10:00:00Z"
            }
        }
    }


# 更新客户API - 需要customers:update权限
@router.put("/customers/{customer_id}")
@require_permission("customers", "update")
async def update_customer(
    customer_id: str,
    customer_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None
):
    """
    更新客户信息
    
    需要权限: customers:update
    适用角色: 教练、销售经理等
    """
    return {
        "code": 200,
        "message": "客户信息更新成功",
        "data": {
            "customer": {
                "id": customer_id,
                "updated_by": current_user["name"],
                "updated_at": "2024-01-20T10:30:00Z"
            }
        }
    }


# 删除客户API - 需要customers:delete权限
@router.delete("/customers/{customer_id}")
@require_permission("customers", "delete")
async def delete_customer(
    customer_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None
):
    """
    删除客户
    
    需要权限: customers:delete
    适用角色: 高级管理员等
    """
    return {
        "code": 200,
        "message": "客户删除成功",
        "data": {
            "deleted_customer_id": customer_id,
            "deleted_by": current_user["name"],
            "deleted_at": "2024-01-20T11:00:00Z"
        }
    }


# 订单管理API - 需要orders:read权限
@router.get("/orders")
@require_permission("orders", "read")
async def get_orders(
    customer_id: Optional[str] = Query(None, description="筛选特定客户的订单"),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None
):
    """
    获取订单列表
    
    需要权限: orders:read
    适用角色: 前台、教练、销售经理等
    """
    return {
        "code": 200,
        "message": "获取订单列表成功",
        "data": {
            "orders": [
                {
                    "id": "order_001",
                    "customer_id": "customer_001",
                    "product": "健身卡",
                    "amount": 1200.00,
                    "status": "已支付"
                }
            ],
            "access_info": {
                "user": current_user["name"],
                "filtered_by_customer": customer_id
            }
        }
    }


# 创建订单API - 需要orders:create权限
@router.post("/orders")
@require_permission("orders", "create")
async def create_order(
    order_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None
):
    """
    创建新订单
    
    需要权限: orders:create
    适用角色: 前台、教练、销售经理等
    """
    return {
        "code": 201,
        "message": "订单创建成功",
        "data": {
            "order": {
                "id": "order_new_001",
                "customer_id": order_data.get("customer_id"),
                "created_by": current_user["name"],
                "created_at": "2024-01-20T12:00:00Z"
            }
        }
    }


# 产品管理API - 需要products:read权限
@router.get("/products")
@require_permission("products", "read")
async def get_products(
    category: Optional[str] = Query(None, description="产品分类"),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None
):
    """
    获取产品列表
    
    需要权限: products:read
    适用角色: 前台、教练、销售经理等
    """
    return {
        "code": 200,
        "message": "获取产品列表成功",
        "data": {
            "products": [
                {
                    "id": "product_001",
                    "name": "月卡",
                    "price": 300.00,
                    "category": "健身卡"
                },
                {
                    "id": "product_002",
                    "name": "年卡", 
                    "price": 2400.00,
                    "category": "健身卡"
                }
            ],
            "access_info": {
                "user": current_user["name"],
                "category_filter": category
            }
        }
    }


# 报表API - 需要任一报表权限
@router.get("/reports/sales")
@require_any_permission([
    ("reports", "read"),
    ("reports", "sales"),
    ("analytics", "read")
])
async def get_sales_report(
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None,
    permission_results: List[dict] = None
):
    """
    获取销售报表
    
    需要权限: reports:read 或 reports:sales 或 analytics:read
    适用角色: 销售经理、高级管理员等
    """
    # 找到允许访问的权限
    allowed_permission = None
    for result in permission_results:
        if result["allowed"]:
            allowed_permission = result["permission"]["permission_name"]
            break
    
    return {
        "code": 200,
        "message": "获取销售报表成功",
        "data": {
            "report": {
                "period": f"{start_date} 至 {end_date}",
                "total_sales": 15600.00,
                "order_count": 13,
                "avg_order_value": 1200.00
            },
            "access_info": {
                "user": current_user["name"],
                "permission_used": allowed_permission
            }
        }
    }


# 系统管理API - 需要角色级别7或以上
@router.get("/admin/system-info")
@require_role_level(7)
async def get_system_info(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: dict = None,
    user_roles: List[dict] = None,
    max_role_level: int = None
):
    """
    获取系统信息
    
    需要角色级别: 7或以上
    适用角色: 系统管理员、超级管理员
    """
    return {
        "code": 200,
        "message": "获取系统信息成功",
        "data": {
            "system": {
                "version": "1.0.0",
                "uptime": "7 days",
                "active_users": 156,
                "total_orders": 1234
            },
            "access_info": {
                "user": current_user["name"],
                "user_roles": [role["name"] for role in user_roles],
                "max_role_level": max_role_level
            }
        }
    }


# 权限测试API - 展示权限验证效果
@router.get("/test/permission-demo")
async def permission_demo(
    request: Request,
    test_resource: str = Query("customers", description="测试资源"),
    test_action: str = Query("read", description="测试操作")
):
    """
    权限验证演示API
    
    不需要权限验证，用于展示权限系统的工作原理
    """
    auth_header = request.headers.get("Authorization", "")
    
    if not auth_header.startswith("Bearer "):
        return {
            "code": 401,
            "message": "缺少认证令牌",
            "data": {
                "demo": "请在Authorization头中提供Bearer token",
                "example_tokens": {
                    "developer": "Bearer AILF_DEV_2024_SECURE",
                    "front_desk": "Bearer front_desk_token", 
                    "coach": "Bearer coach_token",
                    "manager": "Bearer manager_token"
                }
            }
        }
    
    token = auth_header[7:]
    
    try:
        from app.core.permissions import extract_user_from_token
        from app.services.permission_service import PermissionService
        from app.core.database import get_db_session
        
        user_info = extract_user_from_token(token)
        permission_service = PermissionService()
        
        with get_db_session() as db:
            permission_check = permission_service.check_permission(
                user_id=user_info["user_id"],
                resource=test_resource,
                action=test_action,
                context={},
                db=db
            )
        
        return {
            "code": 200,
            "message": "权限验证演示",
            "data": {
                "user_info": user_info,
                "test_permission": f"{test_resource}:{test_action}",
                "permission_check": permission_check,
                "explanation": {
                    "allowed": permission_check["allowed"],
                    "reason": permission_check.get("reason", "权限验证通过") if not permission_check["allowed"] else "用户有此权限",
                    "user_roles": permission_check["user"]["roles"],
                    "next_steps": "如果权限被拒绝，请联系管理员为您的角色授予相应权限"
                }
            }
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": "权限验证演示失败",
            "data": {
                "error": str(e),
                "token_received": token[:20] + "..." if len(token) > 20 else token
            }
        }
