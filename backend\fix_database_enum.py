#!/usr/bin/env python3
"""
直接修复数据库中的工作流状态枚举值
将小写的枚举值转换为大写
"""
import mysql.connector
import sys

def fix_database_enum():
    """修复数据库中的枚举值"""
    print('🔧 开始修复数据库中的工作流状态枚举值...')
    
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='ailf_db'
        )
        cursor = conn.cursor()
        
        # 检查当前数据
        print('📊 检查当前数据...')
        cursor.execute('SELECT id, name, status FROM workflows ORDER BY created_at DESC')
        rows = cursor.fetchall()
        print(f'📋 当前数据库中有 {len(rows)} 条工作流记录:')
        
        for row in rows:
            print(f'  - {row[0]}: {row[1]} -> {row[2]}')
        
        # 更新枚举值映射
        status_mapping = {
            'draft': 'DRAFT',
            'active': 'ACTIVE', 
            'inactive': 'INACTIVE',
            'archived': 'ARCHIVED'
        }
        
        total_updated = 0
        print('\n🔄 开始更新枚举值...')
        
        for old_status, new_status in status_mapping.items():
            cursor.execute(
                'UPDATE workflows SET status = %s WHERE status = %s',
                (new_status, old_status)
            )
            if cursor.rowcount > 0:
                print(f'✅ 更新了 {cursor.rowcount} 条记录：{old_status} -> {new_status}')
                total_updated += cursor.rowcount
        
        if total_updated == 0:
            print('ℹ️ 没有需要更新的记录，所有枚举值已经是正确的格式')
        else:
            print(f'🎯 总共更新了 {total_updated} 条记录')
        
        # 提交事务
        conn.commit()
        print('✅ 事务提交成功')
        
        # 验证更新结果
        print('\n📊 验证更新结果...')
        cursor.execute('SELECT id, name, status FROM workflows ORDER BY created_at DESC')
        rows = cursor.fetchall()
        print(f'📋 更新后的数据：')
        for row in rows:
            print(f'  - {row[0]}: {row[1]} -> {row[2]}')
        
        print('\n🎉 工作流状态枚举值修复完成！')
        return True
        
    except Exception as e:
        print(f'❌ 修复失败：{e}')
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = fix_database_enum()
    sys.exit(0 if success else 1)
