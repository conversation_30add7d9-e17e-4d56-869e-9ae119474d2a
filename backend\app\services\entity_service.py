"""
实体建模服务
处理实体定义、字段、关系和数据记录的业务逻辑
"""
import time
import json
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from app.core.database import get_db_session, db_manager
from app.models.entity import (
    EntityDBModel, EntityFieldDBModel, EntityRelationshipDBModel, EntityRecordDBModel
)
from app.schemas.entity import (
    EntityCreateRequest, EntityUpdateRequest, Entity, EntityListItem,
    EntityField, EntityRelationship, EntityRecord, PaginationInfo,
    EntityStatistics, FieldType, RelationshipType, EntityStatus
)


class EntityService:
    """实体管理服务类"""
    
    def __init__(self):
        pass
    
    def _generate_id(self, prefix: str) -> str:
        """生成唯一ID"""
        import random
        timestamp = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)
        return f"{prefix}_{timestamp}_{random_suffix}"
    
    def _convert_db_entity_to_schema(self, db_entity: EntityDBModel, include_fields: bool = True, 
                                   include_relationships: bool = False, include_statistics: bool = False) -> Entity:
        """将数据库实体转换为Schema模型"""
        # 基础字段
        entity_data = {
            "id": db_entity.id,
            "name": db_entity.name,
            "displayName": db_entity.display_name,
            "description": db_entity.description,
            "icon": db_entity.icon,
            "status": EntityStatus(db_entity.status),
            "field_count": db_entity.field_count,
            "record_count": db_entity.record_count,
            "created_at": db_entity.created_at.isoformat() if db_entity.created_at else "",
            "updated_at": db_entity.updated_at.isoformat() if db_entity.updated_at else ""
        }
        
        # 包含字段信息
        if include_fields and db_entity.fields:
            entity_data["fields"] = [
                EntityField(
                    id=field.id,
                    name=field.name,
                    displayName=field.display_name,
                    type=FieldType(field.type),
                    required=field.required,
                    unique=field.unique_field,
                    defaultValue=field.default_value,
                    validation=field.validation,
                    options=field.options,
                    sort_order=field.sort_order
                ) for field in sorted(db_entity.fields, key=lambda x: x.sort_order)
            ]
        
        # 包含关系信息
        if include_relationships and db_entity.source_relationships:
            entity_data["relationships"] = [
                EntityRelationship(
                    id=rel.id,
                    name=rel.name,
                    sourceEntity=db_entity.name,
                    targetEntity=rel.target_entity.name if rel.target_entity else rel.target_entity_id,
                    type=RelationshipType(rel.type),
                    foreignKey=rel.foreign_key,
                    constraints=rel.constraints,
                    created_at=rel.created_at.isoformat() if rel.created_at else "",
                    updated_at=rel.updated_at.isoformat() if rel.updated_at else ""
                ) for rel in db_entity.source_relationships
            ]
        
        # 包含统计信息
        if include_statistics:
            entity_data["statistics"] = EntityStatistics(
                record_count=db_entity.record_count or 0,
                field_count=db_entity.field_count or 0,
                relationship_count=len(db_entity.source_relationships) if db_entity.source_relationships else 0,
                last_updated=db_entity.updated_at.isoformat() if db_entity.updated_at else ""
            )
        
        return Entity(**entity_data)
    
    def create_entity(self, request: EntityCreateRequest, scenario_id: Optional[str] = None) -> Dict[str, Any]:
        """创建实体"""
        try:
            with get_db_session() as db:
                # 检查实体名称是否重复
                existing_entity = db.query(EntityDBModel).filter(
                    and_(
                        EntityDBModel.name == request.name,
                        EntityDBModel.scenario_id == scenario_id
                    )
                ).first()
                
                if existing_entity:
                    return {
                        "success": False,
                        "error": "duplicate_entity_name",
                        "details": "实体名称已存在"
                    }
                
                # 创建实体
                entity_id = self._generate_id("entity")
                db_entity = EntityDBModel(
                    id=entity_id,
                    scenario_id=scenario_id,
                    name=request.name,
                    display_name=request.displayName,
                    description=request.description,
                    icon=request.icon,
                    status="active",
                    field_count=len(request.fields),
                    record_count=0
                )
                
                db.add(db_entity)
                db.flush()  # 获取实体ID
                
                # 创建字段
                for i, field_request in enumerate(request.fields):
                    field_id = self._generate_id("field")
                    db_field = EntityFieldDBModel(
                        id=field_id,
                        entity_id=entity_id,
                        name=field_request.name,
                        display_name=field_request.displayName,
                        type=field_request.type.value,
                        required=field_request.required,
                        unique_field=field_request.unique,
                        default_value=field_request.defaultValue,
                        validation=field_request.validation.model_dump() if field_request.validation else None,
                        options=field_request.options,
                        sort_order=i
                    )
                    db.add(db_field)
                
                db.commit()
                
                # 重新查询以获取完整信息
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                entity = self._convert_db_entity_to_schema(db_entity, include_fields=True)
                
                return {
                    "success": True,
                    "data": {"entity": entity}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"创建实体失败: {str(e)}"
            }
    
    def get_entities_list(self, scenario_id: Optional[str] = None, include_fields: bool = True, 
                         status: Optional[str] = None) -> Dict[str, Any]:
        """获取实体列表"""
        try:
            with get_db_session() as db:
                query = db.query(EntityDBModel)
                
                # 筛选条件
                if scenario_id:
                    query = query.filter(EntityDBModel.scenario_id == scenario_id)
                if status:
                    query = query.filter(EntityDBModel.status == status)
                
                db_entities = query.order_by(EntityDBModel.created_at.desc()).all()
                
                entities = []
                for db_entity in db_entities:
                    entity_item = EntityListItem(
                        id=db_entity.id,
                        name=db_entity.name,
                        displayName=db_entity.display_name,
                        description=db_entity.description,
                        icon=db_entity.icon,
                        status=EntityStatus(db_entity.status),
                        field_count=db_entity.field_count or 0,
                        record_count=db_entity.record_count or 0,
                        created_at=db_entity.created_at.isoformat() if db_entity.created_at else "",
                        updated_at=db_entity.updated_at.isoformat() if db_entity.updated_at else ""
                    )
                    
                    # 包含字段信息
                    if include_fields and db_entity.fields:
                        entity_item.fields = [
                            EntityField(
                                id=field.id,
                                name=field.name,
                                displayName=field.display_name,
                                type=FieldType(field.type),
                                required=field.required,
                                unique=field.unique_field,
                                defaultValue=field.default_value,
                                validation=field.validation,
                                options=field.options,
                                sort_order=field.sort_order
                            ) for field in sorted(db_entity.fields, key=lambda x: x.sort_order)
                        ]
                    
                    entities.append(entity_item)
                
                return {
                    "success": True,
                    "data": {
                        "entities": entities,
                        "total": len(entities)
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询实体列表失败: {str(e)}"
            }
    
    def get_entity_by_id(self, entity_id: str) -> Dict[str, Any]:
        """根据ID获取实体详情"""
        try:
            with get_db_session() as db:
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                
                if not db_entity:
                    return {
                        "success": False,
                        "error": "entity_not_found",
                        "details": "实体不存在"
                    }
                
                entity = self._convert_db_entity_to_schema(
                    db_entity, 
                    include_fields=True, 
                    include_relationships=True, 
                    include_statistics=True
                )
                
                return {
                    "success": True,
                    "data": {"entity": entity}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询实体详情失败: {str(e)}"
            }
    
    def update_entity(self, entity_id: str, request: EntityUpdateRequest) -> Dict[str, Any]:
        """更新实体"""
        try:
            with get_db_session() as db:
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                
                if not db_entity:
                    return {
                        "success": False,
                        "error": "entity_not_found",
                        "details": "实体不存在"
                    }
                
                # 更新基础信息
                if request.displayName is not None:
                    db_entity.display_name = request.displayName
                if request.description is not None:
                    db_entity.description = request.description
                if request.icon is not None:
                    db_entity.icon = request.icon
                
                # 更新字段（如果提供）
                if request.fields is not None:
                    # 删除现有字段
                    db.query(EntityFieldDBModel).filter(EntityFieldDBModel.entity_id == entity_id).delete()
                    
                    # 创建新字段
                    for i, field_request in enumerate(request.fields):
                        field_id = self._generate_id("field")
                        db_field = EntityFieldDBModel(
                            id=field_id,
                            entity_id=entity_id,
                            name=field_request.name,
                            display_name=field_request.displayName,
                            type=field_request.type.value,
                            required=field_request.required,
                            unique_field=field_request.unique,
                            default_value=field_request.defaultValue,
                            validation=field_request.validation.model_dump() if field_request.validation else None,
                            options=field_request.options,
                            sort_order=i
                        )
                        db.add(db_field)
                    
                    db_entity.field_count = len(request.fields)
                
                db.commit()
                
                # 重新查询以获取完整信息
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                entity = self._convert_db_entity_to_schema(db_entity, include_fields=True)
                
                return {
                    "success": True,
                    "data": {"entity": entity}
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新实体失败: {str(e)}"
            }
    
    def delete_entity(self, entity_id: str, force: bool = False, backup: bool = True) -> Dict[str, Any]:
        """删除实体"""
        try:
            with get_db_session() as db:
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                
                if not db_entity:
                    return {
                        "success": False,
                        "error": "entity_not_found",
                        "details": "实体不存在"
                    }
                
                # 统计影响的记录和关系
                affected_records = db.query(EntityRecordDBModel).filter(EntityRecordDBModel.entity_id == entity_id).count()
                affected_relationships = db.query(EntityRelationshipDBModel).filter(
                    or_(
                        EntityRelationshipDBModel.source_entity_id == entity_id,
                        EntityRelationshipDBModel.target_entity_id == entity_id
                    )
                ).count()
                
                # 备份数据（如果需要）
                backup_file = None
                if backup:
                    backup_data = {
                        "entity": {
                            "id": db_entity.id,
                            "name": db_entity.name,
                            "display_name": db_entity.display_name,
                            "description": db_entity.description,
                            "icon": db_entity.icon,
                            "status": db_entity.status
                        },
                        "fields": [
                            {
                                "name": field.name,
                                "display_name": field.display_name,
                                "type": field.type,
                                "required": field.required,
                                "unique_field": field.unique_field,
                                "default_value": field.default_value,
                                "validation": field.validation,
                                "options": field.options
                            } for field in db_entity.fields
                        ],
                        "records": [
                            record.data for record in db_entity.records
                        ]
                    }
                    backup_file = f"/backups/entity_{db_entity.name}_{int(time.time())}.json"
                    # 这里应该实际保存备份文件，暂时只是生成文件名
                
                # 删除实体（级联删除字段、关系和记录）
                db.delete(db_entity)
                db.commit()
                
                return {
                    "success": True,
                    "data": {
                        "entity_id": entity_id,
                        "name": db_entity.name,
                        "deleted_at": time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                        "backup_file": backup_file,
                        "affected_records": affected_records,
                        "affected_relationships": affected_relationships
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除实体失败: {str(e)}"
            }


    def get_entity_records(self, entity_id: str, page: int = 1, limit: int = 20,
                          filters: Optional[Dict[str, Any]] = None,
                          sort_by: Optional[str] = None, sort_order: str = "desc") -> Dict[str, Any]:
        """获取实体数据记录列表"""
        try:
            with get_db_session() as db:
                # 检查实体是否存在
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                if not db_entity:
                    return {
                        "success": False,
                        "error": "entity_not_found",
                        "details": "实体不存在"
                    }

                # 构建查询
                query = db.query(EntityRecordDBModel).filter(EntityRecordDBModel.entity_id == entity_id)

                # 应用筛选条件（简化版本，实际应该根据字段类型进行复杂筛选）
                if filters:
                    for field_name, value in filters.items():
                        if value is not None:
                            # 这里简化处理，实际应该根据字段类型进行不同的筛选
                            query = query.filter(EntityRecordDBModel.data[field_name].astext == str(value))

                # 排序
                if sort_by:
                    if sort_order.lower() == "asc":
                        query = query.order_by(asc(EntityRecordDBModel.data[sort_by].astext))
                    else:
                        query = query.order_by(desc(EntityRecordDBModel.data[sort_by].astext))
                else:
                    query = query.order_by(desc(EntityRecordDBModel.created_at))

                # 分页
                total = query.count()
                offset = (page - 1) * limit
                db_records = query.offset(offset).limit(limit).all()

                # 转换为响应格式
                records = []
                for db_record in db_records:
                    record_data = db_record.data.copy()
                    record_data["id"] = db_record.id
                    record_data["created_at"] = db_record.created_at.isoformat() if db_record.created_at else ""
                    record_data["updated_at"] = db_record.updated_at.isoformat() if db_record.updated_at else ""
                    records.append(record_data)

                # 分页信息
                pages = (total + limit - 1) // limit
                pagination = PaginationInfo(
                    page=page,
                    limit=limit,
                    total=total,
                    pages=pages,
                    has_next=page < pages,
                    has_prev=page > 1
                )

                return {
                    "success": True,
                    "data": {
                        "records": records,
                        "pagination": pagination
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询实体记录失败: {str(e)}"
            }

    def create_entity_record(self, entity_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建实体数据记录"""
        try:
            with get_db_session() as db:
                # 检查实体是否存在
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                if not db_entity:
                    return {
                        "success": False,
                        "error": "entity_not_found",
                        "details": "实体不存在"
                    }

                # 验证数据（简化版本）
                validation_result = self._validate_record_data(db_entity, data)
                if not validation_result["valid"]:
                    return {
                        "success": False,
                        "error": "validation_failed",
                        "details": validation_result["errors"]
                    }

                # 创建记录
                record_id = self._generate_id("record")
                db_record = EntityRecordDBModel(
                    id=record_id,
                    entity_id=entity_id,
                    data=data
                )

                db.add(db_record)

                # 更新实体记录数量
                db_entity.record_count = (db_entity.record_count or 0) + 1

                db.commit()

                # 构建响应数据
                record_data = data.copy()
                record_data["id"] = record_id
                record_data["created_at"] = db_record.created_at.isoformat() if db_record.created_at else ""
                record_data["updated_at"] = db_record.updated_at.isoformat() if db_record.updated_at else ""

                return {
                    "success": True,
                    "data": {"record": record_data}
                }

        except Exception as e:
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"创建记录失败: {str(e)}"
            }

    def update_entity_record(self, entity_id: str, record_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新实体数据记录"""
        try:
            with get_db_session() as db:
                # 检查记录是否存在
                db_record = db.query(EntityRecordDBModel).filter(
                    and_(
                        EntityRecordDBModel.id == record_id,
                        EntityRecordDBModel.entity_id == entity_id
                    )
                ).first()

                if not db_record:
                    return {
                        "success": False,
                        "error": "record_not_found",
                        "details": "记录不存在"
                    }

                # 获取实体信息用于验证
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()

                # 验证数据（简化版本）
                validation_result = self._validate_record_data(db_entity, data)
                if not validation_result["valid"]:
                    return {
                        "success": False,
                        "error": "validation_failed",
                        "details": validation_result["errors"]
                    }

                # 更新记录
                db_record.data = data
                db.commit()

                # 构建响应数据
                record_data = data.copy()
                record_data["id"] = record_id
                record_data["created_at"] = db_record.created_at.isoformat() if db_record.created_at else ""
                record_data["updated_at"] = db_record.updated_at.isoformat() if db_record.updated_at else ""

                return {
                    "success": True,
                    "data": {"record": record_data}
                }

        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新记录失败: {str(e)}"
            }

    def delete_entity_record(self, entity_id: str, record_id: str) -> Dict[str, Any]:
        """删除实体数据记录"""
        try:
            with get_db_session() as db:
                # 检查记录是否存在
                db_record = db.query(EntityRecordDBModel).filter(
                    and_(
                        EntityRecordDBModel.id == record_id,
                        EntityRecordDBModel.entity_id == entity_id
                    )
                ).first()

                if not db_record:
                    return {
                        "success": False,
                        "error": "record_not_found",
                        "details": "记录不存在"
                    }

                # 删除记录
                db.delete(db_record)

                # 更新实体记录数量
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                if db_entity:
                    db_entity.record_count = max(0, (db_entity.record_count or 1) - 1)

                db.commit()

                return {
                    "success": True,
                    "data": {
                        "record_id": record_id,
                        "deleted_at": time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除记录失败: {str(e)}"
            }

    def _validate_record_data(self, db_entity: EntityDBModel, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证记录数据（简化版本）"""
        errors = []

        # 检查必填字段
        for field in db_entity.fields:
            if field.required and (field.name not in data or data[field.name] is None):
                errors.append(f"字段 '{field.display_name}' 是必填的")

        # 检查唯一字段（简化版本，实际应该查询数据库）
        for field in db_entity.fields:
            if field.unique_field and field.name in data:
                # 这里应该查询数据库检查唯一性，暂时跳过
                pass

        return {
            "valid": len(errors) == 0,
            "errors": errors
        }

    def get_entity_relationships(self, entity_id: str) -> Dict[str, Any]:
        """获取实体关系列表"""
        try:
            with get_db_session() as db:
                # 检查实体是否存在
                db_entity = db.query(EntityDBModel).filter(EntityDBModel.id == entity_id).first()
                if not db_entity:
                    return {
                        "success": False,
                        "error": "entity_not_found",
                        "details": "实体不存在"
                    }

                # 获取作为源实体的关系
                source_relationships = db.query(EntityRelationshipDBModel).filter(
                    EntityRelationshipDBModel.source_entity_id == entity_id
                ).all()

                # 获取作为目标实体的关系
                target_relationships = db.query(EntityRelationshipDBModel).filter(
                    EntityRelationshipDBModel.target_entity_id == entity_id
                ).all()

                relationships = []

                # 处理源关系
                for rel in source_relationships:
                    target_entity = db.query(EntityDBModel).filter(EntityDBModel.id == rel.target_entity_id).first()
                    relationships.append(EntityRelationship(
                        id=rel.id,
                        name=rel.name,
                        sourceEntity=db_entity.name,
                        targetEntity=target_entity.name if target_entity else rel.target_entity_id,
                        type=RelationshipType(rel.type),
                        foreignKey=rel.foreign_key,
                        constraints=rel.constraints,
                        created_at=rel.created_at.isoformat() if rel.created_at else "",
                        updated_at=rel.updated_at.isoformat() if rel.updated_at else ""
                    ))

                # 处理目标关系（反向关系）
                for rel in target_relationships:
                    source_entity = db.query(EntityDBModel).filter(EntityDBModel.id == rel.source_entity_id).first()
                    relationships.append(EntityRelationship(
                        id=rel.id,
                        name=f"{rel.name} (反向)",
                        sourceEntity=source_entity.name if source_entity else rel.source_entity_id,
                        targetEntity=db_entity.name,
                        type=RelationshipType(rel.type),
                        foreignKey=rel.foreign_key,
                        constraints=rel.constraints,
                        created_at=rel.created_at.isoformat() if rel.created_at else "",
                        updated_at=rel.updated_at.isoformat() if rel.updated_at else ""
                    ))

                return {
                    "success": True,
                    "data": {
                        "relationships": relationships,
                        "total": len(relationships)
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"查询实体关系失败: {str(e)}"
            }

    def create_entity_relationship(self, source_entity_id: str, request) -> Dict[str, Any]:
        """创建实体关系"""
        try:
            with get_db_session() as db:
                # 检查源实体是否存在
                source_entity = db.query(EntityDBModel).filter(EntityDBModel.id == source_entity_id).first()
                if not source_entity:
                    return {
                        "success": False,
                        "error": "source_entity_not_found",
                        "details": "源实体不存在"
                    }

                # 检查目标实体是否存在
                target_entity = db.query(EntityDBModel).filter(EntityDBModel.name == request.targetEntity).first()
                if not target_entity:
                    return {
                        "success": False,
                        "error": "target_entity_not_found",
                        "details": "目标实体不存在"
                    }

                # 检查关系是否已存在
                existing_rel = db.query(EntityRelationshipDBModel).filter(
                    and_(
                        EntityRelationshipDBModel.source_entity_id == source_entity_id,
                        EntityRelationshipDBModel.target_entity_id == target_entity.id,
                        EntityRelationshipDBModel.name == request.name
                    )
                ).first()

                if existing_rel:
                    return {
                        "success": False,
                        "error": "relationship_exists",
                        "details": "关系已存在"
                    }

                # 创建关系
                relationship_id = self._generate_id("rel")
                db_relationship = EntityRelationshipDBModel(
                    id=relationship_id,
                    name=request.name,
                    source_entity_id=source_entity_id,
                    target_entity_id=target_entity.id,
                    type=request.type.value,
                    foreign_key=request.foreignKey,
                    constraints=request.constraints.model_dump() if request.constraints else None
                )

                db.add(db_relationship)
                db.commit()

                # 构建响应
                relationship = EntityRelationship(
                    id=relationship_id,
                    name=request.name,
                    sourceEntity=source_entity.name,
                    targetEntity=target_entity.name,
                    type=request.type,
                    foreignKey=request.foreignKey,
                    constraints=request.constraints,
                    created_at=db_relationship.created_at.isoformat() if db_relationship.created_at else "",
                    updated_at=db_relationship.updated_at.isoformat() if db_relationship.updated_at else ""
                )

                return {
                    "success": True,
                    "data": {"relationship": relationship}
                }

        except Exception as e:
            return {
                "success": False,
                "error": "creation_failed",
                "details": f"创建关系失败: {str(e)}"
            }

    def delete_entity_relationship(self, entity_id: str, relationship_id: str) -> Dict[str, Any]:
        """删除实体关系"""
        try:
            with get_db_session() as db:
                # 检查关系是否存在
                db_relationship = db.query(EntityRelationshipDBModel).filter(
                    and_(
                        EntityRelationshipDBModel.id == relationship_id,
                        or_(
                            EntityRelationshipDBModel.source_entity_id == entity_id,
                            EntityRelationshipDBModel.target_entity_id == entity_id
                        )
                    )
                ).first()

                if not db_relationship:
                    return {
                        "success": False,
                        "error": "relationship_not_found",
                        "details": "关系不存在"
                    }

                # 删除关系
                relationship_name = db_relationship.name
                db.delete(db_relationship)
                db.commit()

                return {
                    "success": True,
                    "data": {
                        "relationship_id": relationship_id,
                        "name": relationship_name,
                        "deleted_at": time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除关系失败: {str(e)}"
            }

    def update_entity_relationship_by_id(self, relationship_id: str, request) -> Dict[str, Any]:
        """根据关系ID更新实体关系"""
        try:
            with get_db_session() as db:
                # 检查关系是否存在
                db_relationship = db.query(EntityRelationshipDBModel).filter(
                    EntityRelationshipDBModel.id == relationship_id
                ).first()

                if not db_relationship:
                    return {
                        "success": False,
                        "error": "relationship_not_found",
                        "details": "关系不存在"
                    }

                # 检查目标实体是否存在
                target_entity = db.query(EntityDBModel).filter(EntityDBModel.name == request.targetEntity).first()
                if not target_entity:
                    return {
                        "success": False,
                        "error": "target_entity_not_found",
                        "details": "目标实体不存在"
                    }

                # 更新关系
                db_relationship.name = request.name
                db_relationship.target_entity_id = target_entity.id
                db_relationship.type = request.type.value
                db_relationship.foreign_key = request.foreignKey
                db_relationship.constraints = request.constraints.model_dump() if request.constraints else None

                db.commit()

                # 构建响应
                source_entity = db.query(EntityDBModel).filter(EntityDBModel.id == db_relationship.source_entity_id).first()
                relationship = EntityRelationship(
                    id=relationship_id,
                    name=request.name,
                    sourceEntity=source_entity.name if source_entity else db_relationship.source_entity_id,
                    targetEntity=target_entity.name,
                    type=request.type,
                    foreignKey=request.foreignKey,
                    constraints=request.constraints,
                    created_at=db_relationship.created_at.isoformat() if db_relationship.created_at else "",
                    updated_at=db_relationship.updated_at.isoformat() if db_relationship.updated_at else ""
                )

                return {
                    "success": True,
                    "data": {"relationship": relationship}
                }

        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新关系失败: {str(e)}"
            }

    def delete_entity_relationship_by_id(self, relationship_id: str) -> Dict[str, Any]:
        """根据关系ID删除实体关系"""
        try:
            with get_db_session() as db:
                # 检查关系是否存在
                db_relationship = db.query(EntityRelationshipDBModel).filter(
                    EntityRelationshipDBModel.id == relationship_id
                ).first()

                if not db_relationship:
                    return {
                        "success": False,
                        "error": "relationship_not_found",
                        "details": "关系不存在"
                    }

                # 删除关系
                relationship_name = db_relationship.name
                db.delete(db_relationship)
                db.commit()

                return {
                    "success": True,
                    "data": {
                        "relationship_id": relationship_id,
                        "name": relationship_name,
                        "deleted_at": time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "deletion_failed",
                "details": f"删除关系失败: {str(e)}"
            }


# 全局实体服务实例
entity_service = EntityService()
