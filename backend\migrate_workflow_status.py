#!/usr/bin/env python3
"""
数据库迁移脚本：更新工作流状态枚举值
将小写的枚举值更新为大写
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.core.config import settings

def migrate_workflow_status():
    """迁移工作流状态枚举值"""
    print("🔧 开始迁移工作流状态枚举值...")
    
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 更新现有的工作流状态值
                status_mapping = {
                    'draft': 'DRAFT',
                    'active': 'ACTIVE', 
                    'inactive': 'INACTIVE',
                    'archived': 'ARCHIVED'
                }
                
                for old_status, new_status in status_mapping.items():
                    result = conn.execute(
                        text("UPDATE workflows SET status = :new_status WHERE status = :old_status"),
                        {"old_status": old_status, "new_status": new_status}
                    )
                    if result.rowcount > 0:
                        print(f"✅ 更新了 {result.rowcount} 条记录：{old_status} -> {new_status}")
                
                # 提交事务
                trans.commit()
                print("🎉 工作流状态枚举值迁移完成！")
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"❌ 迁移失败，已回滚：{e}")
                raise
                
    except Exception as e:
        print(f"❌ 数据库连接失败：{e}")
        raise

if __name__ == "__main__":
    migrate_workflow_status()
