/**
 * 基于Butterfly-DAG的工作流可视化编辑画布
 * 支持拖拽创建节点、连接节点、编辑节点属性
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { message, Modal, Drawer } from 'antd';
import { Canvas } from 'butterfly-dag';
import type { WorkflowNode, WorkflowDefinition, ButterflyData } from '../../../types/developer/workflowTypes';
import { workflowAPI } from '../../../services/developer/workflowAPI';
import { apiClient } from '../../../services/common/apiClient';
import { useDeveloperAuth } from '../../../hooks/developer/useDeveloperAuth';
import NodeEditor from './NodeEditor';
import NodePalette from './NodePalette';
import WorkflowButterflyNode from './WorkflowButterflyNode';
import WorkflowButterflyEdge from './WorkflowButterflyEdge';
import './WorkflowCanvas.css';
import 'butterfly-dag/dist/index.css';

// Butterfly相关类型 (临时定义，等安装完成后使用真实的)
interface ButterflyCanvas {
  draw: (data: any) => void;
  addNode: (nodeData: any) => void;
  addEdge: (edgeData: any) => void;
  removeNode: (nodeId: string) => void;
  removeEdge: (edgeId: string) => void;
  getDataMap: () => any;
  on: (event: string, callback: Function) => void;
  off: (event: string, callback: Function) => void;
  redraw: () => void;
  destroy: () => void;
  clear?: () => void;  // 清除画布内容
}

interface WorkflowCanvasProps {
  workflowId?: string;
  workflow?: WorkflowDefinition;
  readonly?: boolean;
  onSave?: (workflow: WorkflowDefinition) => void;
  onNodeSelect?: (node: WorkflowNode | null) => void;
}

const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  workflowId,
  workflow,
  readonly = false,
  onSave,
  onNodeSelect,
}) => {
  const { authState } = useDeveloperAuth();
  const canvasRef = useRef<HTMLDivElement>(null);
  const butterflyRef = useRef<ButterflyCanvas | null>(null);
  const lastWorkflowIdRef = useRef<string | undefined>(undefined); // 追踪当前加载的工作流ID
  const [workflowData, setWorkflowData] = useState<WorkflowDefinition | null>(workflow || null);
  const [nodes, setNodes] = useState<WorkflowNode[]>(workflow?.nodes || []);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showNodeEditor, setShowNodeEditor] = useState(false);
  const [loading, setLoading] = useState(false);
  const [canvasInitialized, setCanvasInitialized] = useState(false);
  const [editorVisible, setEditorVisible] = useState(false);

  // 获取节点端点配置
  const getNodeEndpoints = useCallback((nodeType: string) => {
    // 根据节点类型返回不同的端点配置
    switch (nodeType) {
      case 'start':
        // 开始节点：只有右侧输出
        return [{ id: 'right', orientation: [1, 0], pos: [0, 0.5] }];

      case 'end':
        // 结束节点：只有左侧输入
        return [{ id: 'left', orientation: [-1, 0], pos: [0, 0.5] }];

      case 'condition':
        // 条件节点：左侧输入，右侧"是"输出，底部"否"输出
        return [
          { id: 'left', orientation: [-1, 0], pos: [0, 0.5] },      // 输入
          { id: 'right', orientation: [1, 0], pos: [0, 0.5] },     // "是"输出
          { id: 'bottom', orientation: [0, 1], pos: [0.5, 0] }     // "否"输出
        ];

      default:
        // 其他节点：标准的四个方向端点
        return [
          { id: 'left', orientation: [-1, 0], pos: [0, 0.5] },
          { id: 'right', orientation: [1, 0], pos: [0, 0.5] },
          { id: 'top', orientation: [0, -1], pos: [0.5, 0] },
          { id: 'bottom', orientation: [0, 1], pos: [0.5, 0] }
        ];
    }
  }, []);

  // 从后端加载工作流数据
  const loadWorkflowData = useCallback(async () => {
    if (!workflowId) return;

    setLoading(true);
    try {
      // 检查是否有有效的认证token
      const apiToken = apiClient.getToken();
      const hasValidToken = (authState.token && !authState.token.includes('fake-token')) || 
                           (apiToken && !apiToken.includes('fake-token'));
      const isAuthenticated = authState.isAuthenticated || hasValidToken; // 只要有有效token就认为已认证
      
      console.log('loadWorkflowData - authState:', authState);
      console.log('loadWorkflowData - isAuthenticated:', isAuthenticated);
      console.log('loadWorkflowData - token exists:', !!authState.token);
      console.log('loadWorkflowData - apiClient has token:', !!apiToken);
      console.log('loadWorkflowData - has valid token:', hasValidToken);
      
      if (isAuthenticated && hasValidToken) {
        console.log('loadWorkflowDetail - apiClient has token:', !!apiClient.getToken());
        console.log('loadWorkflowDetail - 调用API获取工作流详情:', workflowId);
        const response = await workflowAPI.getWorkflowDetail(workflowId);
        if (response.code === 200 && response.data) {
          const workflowDetail = response.data.workflow;
          setWorkflowData(workflowDetail);
          setNodes(workflowDetail.nodes || []);
          console.log('✅ 工作流数据加载成功:', workflowDetail);
        } else {
          message.error(response.message || '加载工作流数据失败');
        }
      } else {
        // 未认证时不调用API，使用传入的数据或示例数据
        console.log('未认证，跳过API调用');
      }
    } catch (error) {
      console.error('Failed to load workflow data:', error);
      message.error('加载工作流数据失败');
    } finally {
      setLoading(false);
    }
  }, [workflowId, authState.isAuthenticated, authState.token]);

  // 将工作流节点转换为Butterfly数据格式
  const convertWorkflowToButterflyData = useCallback((workflowNodes: WorkflowNode[]) => {
    console.log('🔍 开始转换工作流数据到Butterfly格式，节点数:', workflowNodes.length);
    console.log('🔍 原始节点数据:', workflowNodes.map(n => ({
      id: n.id,
      name: n.name,
      parent_nodes: n.parent_nodes,
      child_nodes: n.child_nodes
    })));

    const butterflyNodes = workflowNodes.map(node => ({
      id: node.id,
      label: node.name,
      left: Math.round(node.position?.x || 100),
      top: Math.round(node.position?.y || 100),
      Class: WorkflowButterflyNode,
      nodeType: node.type,
      endpoints: getNodeEndpoints(node.type),
      options: {
        nodeType: node.type,
        label: node.name,
        name: node.name
      }
    }));

    // 根据节点的parent_nodes和child_nodes关系生成连线
    const butterflyEdges: any[] = [];
    let edgeIndex = 0;

    workflowNodes.forEach(node => {
      console.log(`🔍 处理节点 ${node.id} 的连线，child_nodes:`, node.child_nodes);
      if (node.child_nodes && node.child_nodes.length > 0) {
        node.child_nodes.forEach((childRelation, index) => {
          const targetNode = workflowNodes.find(n => n.id === childRelation.node_id);
          console.log(`🔍 查找目标节点 ${childRelation.node_id}:`, targetNode ? '找到' : '未找到');
          if (targetNode) {
            // 根据节点类型确定连接端点
            let sourceEndpoint = 'right';
            let targetEndpoint = 'left';

            // 条件节点的特殊处理
            if (node.type === 'condition') {
              if (index === 0) {
                sourceEndpoint = 'right'; // "是"分支
              } else {
                sourceEndpoint = 'bottom'; // "否"分支
              }
            }

            const edge = {
              id: `edge-${edgeIndex++}`,
              source: sourceEndpoint,
              target: targetEndpoint,
              sourceNode: node.id,
              targetNode: childRelation.node_id,
              type: 'endpoint',
              arrow: true,
              Class: WorkflowButterflyEdge,
              label: childRelation.condition?.description || ''
            };
            
            console.log(`🔍 创建连线:`, edge);
            butterflyEdges.push(edge);
          }
        });
      }
    });

    console.log('🔍 生成的连线数组:', butterflyEdges);

    return {
      nodes: butterflyNodes,
      edges: butterflyEdges
    };
  }, [getNodeEndpoints]);

  // 创建示例数据（当没有workflowId时使用）
  const createSampleData = useCallback(() => {
    return {
      nodes: [
        {
          id: 'start-1',
          label: '开始',
          left: 100,
          top: 150,
          Class: WorkflowButterflyNode,
          nodeType: 'start',
          endpoints: getNodeEndpoints('start'),
          options: { nodeType: 'start', label: '开始', name: '开始' }
        },
        {
          id: 'condition-1',
          label: '条件判断',
          left: 300,
          top: 150,
          Class: WorkflowButterflyNode,
          nodeType: 'condition',
          endpoints: getNodeEndpoints('condition'),
          options: { nodeType: 'condition', label: '条件判断', name: '条件判断' }
        },
        {
          id: 'api-success',
          label: 'API调用',
          left: 500,
          top: 100,
          Class: WorkflowButterflyNode,
          nodeType: 'api_call',
          endpoints: getNodeEndpoints('api_call'),
          options: { nodeType: 'api_call', label: 'API调用', name: 'API调用' }
        },
        {
          id: 'notification-fail',
          label: '失败通知',
          left: 500,
          top: 250,
          Class: WorkflowButterflyNode,
          nodeType: 'notification',
          endpoints: getNodeEndpoints('notification'),
          options: { nodeType: 'notification', label: '失败通知', name: '失败通知' }
        },
        {
          id: 'end-1',
          label: '结束',
          left: 700,
          top: 150,
          Class: WorkflowButterflyNode,
          nodeType: 'end',
          endpoints: getNodeEndpoints('end'),
          options: { nodeType: 'end', label: '结束', name: '结束' }
        }
      ],
      edges: [
        {
          id: 'edge-1',
          source: 'right',
          target: 'left',
          sourceNode: 'start-1',
          targetNode: 'condition-1',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowButterflyEdge
        },
        {
          id: 'edge-2',
          source: 'right',
          target: 'left',
          sourceNode: 'condition-1',
          targetNode: 'api-success',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowButterflyEdge,
          label: '满足条件'
        },
        {
          id: 'edge-3',
          source: 'bottom',
          target: 'left',
          sourceNode: 'condition-1',
          targetNode: 'notification-fail',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowButterflyEdge,
          label: '不满足条件'
        },
        {
          id: 'edge-4',
          source: 'right',
          target: 'left',
          sourceNode: 'api-success',
          targetNode: 'end-1',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowButterflyEdge
        },
        {
          id: 'edge-5',
          source: 'right',
          target: 'left',
          sourceNode: 'notification-fail',
          targetNode: 'end-1',
          type: 'endpoint',
          arrow: true,
          Class: WorkflowButterflyEdge
        }
      ]
    };
  }, [getNodeEndpoints]);

  // 初始化Butterfly画布
  const initCanvas = useCallback(() => {
    if (!canvasRef.current) {
      console.warn('⚠️ Canvas ref不存在，延迟初始化');
      return;
    }

    // 如果画布已经初始化且正常工作，跳过重新初始化
    if (butterflyRef.current && canvasInitialized) {
      console.log('🎨 画布已初始化且正常工作，跳过重新初始化');
      return;
    }

    console.log('🎨 开始初始化Butterfly画布...');

    try {
      // 清理现有的画布
      if (butterflyRef.current) {
        console.log('🧹 清理现有画布');
        try {
          if (typeof butterflyRef.current.destroy === 'function') {
            butterflyRef.current.destroy();
          }
        } catch (e) {
          console.warn('清理画布时出错:', e);
        }
        butterflyRef.current = null;
      }

      // 确保容器可见
      const container = canvasRef.current;
      container.innerHTML = ''; // 清空容器内容
      container.style.minHeight = '500px';
      container.style.backgroundColor = '#ffffff';

      // 直接使用已导入的Canvas
      const canvas = new Canvas({
        root: container,
        disLinkable: readonly, // 只读模式下禁止连线
        linkable: !readonly,   // 只读模式下禁止连线
        draggable: !readonly,  // 只读模式下禁止拖拽
        zoomable: true,        // 允许缩放
        moveable: true,        // 允许平移
        theme: {
          edge: {
            shapeType: 'AdvancedBezier',
            arrow: true,
            arrowPosition: 0.5,
          },
          endpoint: {
            position: ['Top', 'Right', 'Bottom', 'Left'],
          },
        },
      });

      butterflyRef.current = canvas as any;
      console.log('✅ Butterfly画布创建成功:', canvas);

      // 绑定事件
      if (!readonly) {
        canvas.on('events', (data: any) => {
          handleCanvasEvent(data);
        });
      }

      // 标记画布已初始化
      setCanvasInitialized(true);
      console.log('✅ 画布初始化完成');

      // 延迟加载数据，确保画布完全准备好
      setTimeout(() => {
        // 加载数据 - 优先使用传入的workflow数据
        if (workflowData && workflowData.nodes && workflowData.nodes.length > 0) {
          // 如果有传入的workflow数据，优先使用传入的数据
          console.log('🎨 初始化：使用传入的workflow数据:', workflowData.name);
          const butterflyData = convertWorkflowToButterflyData(workflowData.nodes);
          canvas.draw(butterflyData);
          lastWorkflowIdRef.current = workflowData.id;
        } else if (workflowId && workflowId !== 'sample') {
          // 如果没有传入数据但有真实的workflowId，从后端加载数据
          console.log('🎨 初始化：从后端加载workflow数据:', workflowId);
          loadWorkflowData();
        } else {
          // 否则使用示例数据
          console.log('🎨 初始化：使用示例数据');
          const sampleData = createSampleData();
          canvas.draw(sampleData);
          lastWorkflowIdRef.current = undefined;
        }
      }, 100);

    } catch (error) {
      console.error('❌ 画布初始化失败:', error);
      message.error('画布初始化失败，可能需要安装butterfly-dag依赖');
      setCanvasInitialized(false);
    }
  }, [readonly, createSampleData, workflowId, loadWorkflowData, workflowData, convertWorkflowToButterflyData, canvasInitialized]);

  // 处理画布事件
  const handleCanvasEvent = (data: any) => {
    console.log('🎯 Canvas事件触发:', data.type, data);
    switch (data.type) {
      case 'node:click':
        console.log('🎯 节点点击事件:', data.node);
        handleNodeClick(data.node);
        break;
      case 'edge:click':
        console.log('🎯 边点击事件:', data.edge);
        handleEdgeClick(data.edge);
        break;
      case 'canvas:click':
        console.log('🎯 画布点击事件');
        setSelectedNode(null);
        onNodeSelect?.(null);
        break;
      case 'node:delete':
        console.log('🎯 节点删除事件:', data.node);
        handleNodeDelete(data.node);
        break;
      case 'edge:connect':
      case 'link:connect':
      case 'connection:create':
        console.log('🎯 边连接事件:', data.type, data);
        if (data.edge) {
          handleEdgeConnect(data.edge);
        } else if (data.link) {
          handleEdgeConnect(data.link);
        } else if (data.links && data.links.length > 0) {
          data.links.forEach((link: any) => handleEdgeConnect(link));
        }
        break;
      case 'edge:disconnect':
      case 'link:disconnect':
      case 'connection:delete':
        console.log('🎯 边断开事件:', data.type, data);
        if (data.edge) {
          handleEdgeDisconnect(data.edge);
        } else if (data.link) {
          handleEdgeDisconnect(data.link);
        }
        break;
      default:
        console.log('🎯 未处理的事件类型:', data.type, data);
        break;
    }
  };

  // 处理节点点击
  const handleNodeClick = (butterflyNode: any) => {
    const workflowNode = nodes.find(n => n.id === butterflyNode.id);
    if (workflowNode) {
      setSelectedNode(workflowNode);
      onNodeSelect?.(workflowNode);
    }
  };

  // 处理边点击
  const handleEdgeClick = (edge: any) => {
    // 可以在这里处理边的点击事件，比如编辑连接条件
    console.log('Edge clicked:', edge);
  };

  // 处理节点删除（本地操作，不调用API）
  const handleNodeDelete = async (butterflyNode: any) => {
    // 本地删除节点，不调用API
    const updatedNodes = nodes.filter(n => n.id !== butterflyNode.id);
    setNodes(updatedNodes);

    if (selectedNode?.id === butterflyNode.id) {
      setSelectedNode(null);
      onNodeSelect?.(null);
    }

    message.success('节点已删除，请保存工作流以提交更改');
  };

  // 处理边连接
  const handleEdgeConnect = async (edge: any) => {
    try {
      // 更新父子节点关系
      const sourceNode = nodes.find(n => n.id === edge.sourceNode);
      const targetNode = nodes.find(n => n.id === edge.targetNode);
      
      if (sourceNode && targetNode) {
        // 避免重复连接
        const existingConnection = sourceNode.child_nodes.find(c => c.node_id === targetNode.id);
        if (existingConnection) {
          console.log('⚠️ 节点已经连接，跳过重复连接');
          return;
        }

        // 更新源节点的子节点列表
        const updatedSourceNode = {
          ...sourceNode,
          child_nodes: [
            ...sourceNode.child_nodes,
            {
              node_id: targetNode.id,
              condition: { 
                type: 'always' as const, 
                description: edge.label || '总是执行'
              }
            }
          ]
        };

        // 更新目标节点的父节点列表
        const updatedTargetNode = {
          ...targetNode,
          parent_nodes: [...targetNode.parent_nodes, sourceNode.id]
        };

        // 更新本地状态但不触发重绘
        const updatedNodes = nodes.map(n => {
          if (n.id === sourceNode.id) return updatedSourceNode;
          if (n.id === targetNode.id) return updatedTargetNode;
          return n;
        });
        
        setNodes(updatedNodes);

        // 更新workflowData但不触发重绘
        if (workflowData) {
          const updatedWorkflowData = {
            ...workflowData,
            nodes: updatedNodes
          };
          setWorkflowData(prevData => ({ ...updatedWorkflowData }));
        }

        // 在Butterfly画布中实际创建边
        if (butterflyRef.current) {
          try {
            const newEdge = {
              id: `edge-${sourceNode.id}-${targetNode.id}-${Date.now()}`,
              source: sourceNode.id,  // 使用source而不是sourceNode
              target: targetNode.id,  // 使用target而不是targetNode
              sourceEndpoint: 'right',
              targetEndpoint: 'left',
              label: edge.label || '连接'
            };
            
            console.log('🔗 在画布中创建边:', newEdge);
            butterflyRef.current.addEdge(newEdge);
            console.log('✅ 画布边创建成功');
          } catch (canvasError) {
            console.warn('⚠️ 画布边创建失败，但节点关系已更新:', canvasError);
          }
        }

        console.log('✅ 节点连接成功:', `${sourceNode.id} -> ${targetNode.id}`);
        message.success('节点连接成功，请保存工作流以提交更改');
      }
    } catch (error) {
      console.error('❌ 节点连接失败:', error);
      message.error('节点连接失败');
    }
  };

  // 处理边断开
  const handleEdgeDisconnect = async (edge: any) => {
    try {
      const sourceNode = nodes.find(n => n.id === edge.sourceNode);
      const targetNode = nodes.find(n => n.id === edge.targetNode);
      
      if (sourceNode && targetNode) {
        // 更新源节点的子节点列表
        const updatedSourceNode = {
          ...sourceNode,
          child_nodes: sourceNode.child_nodes.filter(c => c.node_id !== targetNode.id)
        };

        // 更新目标节点的父节点列表
        const updatedTargetNode = {
          ...targetNode,
          parent_nodes: targetNode.parent_nodes.filter(p => p !== sourceNode.id)
        };

        // 更新本地状态但不触发重绘
        const updatedNodes = nodes.map(n => {
          if (n.id === sourceNode.id) return updatedSourceNode;
          if (n.id === targetNode.id) return updatedTargetNode;
          return n;
        });
        
        setNodes(updatedNodes);

        // 更新workflowData但不触发重绘
        if (workflowData) {
          const updatedWorkflowData = {
            ...workflowData,
            nodes: updatedNodes
          };
          setWorkflowData(prevData => ({ ...updatedWorkflowData }));
        }

        console.log('✅ 节点连接已断开:', `${sourceNode.id} -X- ${targetNode.id}`);
        message.success('节点连接已断开，请保存工作流以提交更改');
      }
    } catch (error) {
      console.error('❌ 断开连接失败:', error);
      message.error('断开连接失败');
    }
  };

  // 获取节点标签
  const getNodeLabel = useCallback((nodeType: string) => {
    const labels = {
      start: '开始',
      api_call: 'API调用',
      user_input: '用户输入',
      condition: '条件判断',
      notification: '通知',
      end: '结束'
    };
    return labels[nodeType as keyof typeof labels] || nodeType;
  }, []);

  // 添加节点到画布
  const addNodeToCanvas = useCallback(async (nodeType: string, position?: { x: number; y: number }) => {
    if (!butterflyRef.current || !canvasInitialized) {
      console.warn('⚠️ 画布未初始化，无法添加节点');
      return;
    }

    const nodeId = `${nodeType}-${Date.now()}`;
    // 计算更好的位置：避免重叠，根据现有节点数量调整位置
    const baseX = 100;
    const baseY = 100;
    const spacing = 200;
    const nodesInRow = 4;
    const nodeIndex = nodes.length;
    const row = Math.floor(nodeIndex / nodesInRow);
    const col = nodeIndex % nodesInRow;
    
    // 优先使用传入的位置，如果没有则使用计算的位置，确保坐标是整数
    const pos = position ? {
      x: Math.round(position.x),
      y: Math.round(position.y)
    } : { 
      x: baseX + col * spacing, 
      y: baseY + row * spacing 
    };
    
    console.log(`🎯 添加节点 ${nodeType}，位置计算:`, {
      nodeIndex,
      row,
      col,
      calculatedPos: { x: baseX + col * spacing, y: baseY + row * spacing },
      finalPos: pos,
      passedPosition: position
    });

    // 创建完整的WorkflowNode对象
    const workflowNode: WorkflowNode = {
      id: nodeId,
      name: getNodeLabel(nodeType),
      description: `${getNodeLabel(nodeType)}节点`,
      type: nodeType as any,
      position: pos,
      parent_nodes: [],
      child_nodes: [],
      config: {},
      outputs: [],
      ai_instructions: {
        purpose: `执行${getNodeLabel(nodeType)}操作`,
        action: `处理${getNodeLabel(nodeType)}逻辑`,
        user_message: '',
        success_handling: '',
        error_handling: ''
      }
    };

    // 创建Butterfly节点对象
    const butterflyNode = {
      id: nodeId,
      label: getNodeLabel(nodeType),
      left: pos.x,
      top: pos.y,
      Class: WorkflowButterflyNode,
      nodeType: nodeType,
      endpoints: getNodeEndpoints(nodeType),
      options: {
        nodeType: nodeType,
        label: getNodeLabel(nodeType),
        name: getNodeLabel(nodeType),
        nodeData: workflowNode  // 添加完整的节点数据
      }
    };

    try {
      // 先更新本地状态
      const updatedNodes = [...nodes, workflowNode];
      setNodes(updatedNodes);

      // 更新workflowData
      if (workflowData) {
        const updatedWorkflowData = {
          ...workflowData,
          nodes: updatedNodes
        };
        setWorkflowData(updatedWorkflowData);
      }

      // 添加到Butterfly画布（不重绘整个画布）
      butterflyRef.current.addNode(butterflyNode);
      console.log('✅ 添加节点成功:', butterflyNode);
      message.success(`节点已添加到画布，请保存工作流以提交更改`);
    } catch (error) {
      console.error('❌ 添加节点失败:', error);
      message.error('添加节点失败');
    }
  }, [nodes, workflowData, getNodeLabel, getNodeEndpoints, canvasInitialized]);

  // 删除节点（本地操作，不调用API）
  const removeNodeFromCanvas = useCallback(async (nodeId: string) => {
    if (!butterflyRef.current || !canvasInitialized) {
      console.warn('⚠️ 画布未初始化，无法删除节点');
      return;
    }

    try {
      // 先从Butterfly画布删除
      butterflyRef.current.removeNode(nodeId);
      
      // 再更新本地状态
      const updatedNodes = nodes.filter(n => n.id !== nodeId);
      setNodes(updatedNodes);

      // 更新workflowData
      if (workflowData) {
        const updatedWorkflowData = {
          ...workflowData,
          nodes: updatedNodes
        };
        setWorkflowData(updatedWorkflowData);
      }

      // 如果删除的是当前选中的节点，清空选择
      if (selectedNode?.id === nodeId) {
        setSelectedNode(null);
        onNodeSelect?.(null);
      }

      console.log('✅ 删除节点成功:', nodeId);
      message.success('节点已删除，请保存工作流以提交更改');
    } catch (error) {
      console.error('❌ 删除节点失败:', error);
      message.error('删除节点失败');
    }
  }, [nodes, workflowData, selectedNode, onNodeSelect, canvasInitialized]);

  // 删除连线
  const removeEdgeFromCanvas = useCallback((edgeId: string) => {
    if (!butterflyRef.current || !canvasInitialized) {
      console.warn('⚠️ 画布未初始化，无法删除连线');
      return;
    }

    try {
      butterflyRef.current.removeEdge(edgeId);
      console.log('✅ 删除连线成功:', edgeId);
      message.success('连线已删除');
    } catch (error) {
      console.error('❌ 删除连线失败:', error);
      message.error('删除连线失败');
    }
  }, [canvasInitialized]);

  // 保存工作流
  const saveWorkflow = async () => {
    if (!onSave || !workflowData) return;

    try {
      // 获取当前画布数据
      const currentData = butterflyRef.current?.getDataMap();
      console.log('💾 获取到的画布数据:', currentData);
      console.log('💾 画布节点数:', currentData?.nodes?.length || 0);
      console.log('💾 画布边数:', currentData?.edges?.length || 0);
      if (currentData?.edges?.length > 0) {
        console.log('💾 边数据详情:', currentData.edges);
      }

      // 将画布数据转换为WorkflowNode格式，保留节点位置
      const updatedNodes: WorkflowNode[] = [];

      if (currentData && currentData.nodes) {
        currentData.nodes.forEach((butterflyNode: any) => {
          const existingNode = nodes.find(n => n.id === butterflyNode.id);
          
          const workflowNode: WorkflowNode = {
            id: butterflyNode.id,
            name: existingNode?.name || butterflyNode.options?.label || butterflyNode.id,
            type: existingNode?.type || butterflyNode.options?.nodeType || 'api_call',
            description: existingNode?.description || '',
            config: existingNode?.config || {},
            ai_instructions: existingNode?.ai_instructions || {
              purpose: '',
              action: '',
              user_message: '',
              success_handling: '',
              error_handling: ''
            },
            // 保存实际的画布位置，确保坐标是整数
            position: {
              x: Math.round(butterflyNode.left || 0),
              y: Math.round(butterflyNode.top || 0)
            },
            parent_nodes: [],
            child_nodes: [],
            outputs: existingNode?.outputs || []
          };
          updatedNodes.push(workflowNode);
        });

        // 处理节点关系（parent_nodes 和 child_nodes）
        if (currentData.edges) {
          currentData.edges.forEach((edge: any) => {
            const sourceNode = updatedNodes.find(n => n.id === edge.sourceNode);
            const targetNode = updatedNodes.find(n => n.id === edge.targetNode);

            if (sourceNode && targetNode) {
              // 添加到源节点的子节点列表
              if (!sourceNode.child_nodes.find(c => c.node_id === targetNode.id)) {
                sourceNode.child_nodes.push({
                  node_id: targetNode.id,
                  condition: {
                    type: 'always',
                    expression: undefined,
                    description: edge.label || '默认转换条件'
                  }
                });
              }

              // 添加到目标节点的父节点列表
              if (!targetNode.parent_nodes.includes(sourceNode.id)) {
                targetNode.parent_nodes.push(sourceNode.id);
              }
            }
          });
        }
      } else {
        // 如果没有画布数据，使用本地节点数据
        updatedNodes.push(...nodes);
      }

      // 创建更新后的工作流定义，确保符合API文档要求
      const updatedWorkflow: WorkflowDefinition = {
        id: workflowData.id,
        name: workflowData.name || '未命名工作流',
        description: workflowData.description || '',
        business_scenario: workflowData.business_scenario || 'general',
        user_intents: workflowData.user_intents || [],
        trigger_keywords: workflowData.trigger_keywords || [],
        inputs: workflowData.inputs || [],
        nodes: updatedNodes,
        metadata: {
          tags: workflowData.metadata?.tags || [],
          complexity: workflowData.metadata?.complexity || 'medium',
          created_by: workflowData.metadata?.created_by,
          created_at: workflowData.metadata?.created_at,
          updated_at: new Date().toISOString()
        }
      };

      console.log('💾 保存工作流，节点数:', updatedNodes.length);
      console.log('💾 节点位置信息:', updatedNodes.map(n => ({ id: n.id, position: n.position })));

      // 调用保存回调
      onSave(updatedWorkflow);

    } catch (error) {
      console.error('保存工作流失败:', error);
      message.error('保存工作流失败');
    }
  };

  // 添加新节点（本地操作，不调用API）
  const addNode = async (nodeType: string, position: { x: number; y: number }) => {
    if (!workflowId) return;

    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      name: `新${getNodeTypeName(nodeType)}`,
      description: `描述新的${getNodeTypeName(nodeType)}`,
      type: nodeType as any,
      position,
      parent_nodes: [],
      child_nodes: [],
      config: {},
      outputs: [],
      ai_instructions: {
        purpose: '',
        action: '',
      },
    };

    // 本地添加节点，不调用API
    const updatedNodes = [...nodes, newNode];
    setNodes(updatedNodes);

    // 更新画布（使用现有的addNodeToCanvas逻辑）
    addNodeToCanvas(nodeType, position);

    message.success('节点已添加到画布，请保存工作流以提交更改');
  };

  // 获取节点类型名称
  const getNodeTypeName = (nodeType: string) => {
    const nameMap: Record<string, string> = {
      start: '开始节点',
      api_call: 'API调用',
      user_input: '用户输入',
      condition: '条件判断',
      notification: '通知节点',
      end: '结束节点',
    };
    return nameMap[nodeType] || '节点';
  };

  // 保存节点编辑（本地操作，不调用API，不触发重绘）
  const handleSaveNode = async (updatedNode: WorkflowNode) => {
    // 只更新本地状态，不调用API，不触发重绘
    const updatedNodes = nodes.map(n => n.id === updatedNode.id ? updatedNode : n);
    setNodes(updatedNodes);

    // 更新workflowData但不触发重绘
    if (workflowData) {
      const updatedWorkflowData = {
        ...workflowData,
        nodes: updatedNodes
      };
      setWorkflowData(prevData => ({ ...updatedWorkflowData }));
    }

    setEditorVisible(false);
    message.success('节点已更新，请保存工作流以提交更改');
  };

  // 处理拖拽放置
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const nodeType = e.dataTransfer.getData('application/workflow-node');
    if (nodeType && canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const position = {
        x: Math.round(e.clientX - rect.left),
        y: Math.round(e.clientY - rect.top)
      };
      addNodeToCanvas(nodeType, position);
    }
  }, [addNodeToCanvas]);

  // 处理节点调色板的节点添加
  const handleNodeDrop = useCallback((nodeType: string, position: { x: number; y: number }) => {
    addNodeToCanvas(nodeType, position);
  }, [addNodeToCanvas]);

  // 监听节点删除事件
  useEffect(() => {
    const handleNodeDeleteEvent = (e: any) => {
      const { nodeId } = e.detail;
      removeNodeFromCanvas(nodeId);
    };

    document.addEventListener('node-delete', handleNodeDeleteEvent);

    return () => {
      document.removeEventListener('node-delete', handleNodeDeleteEvent);
    };
  }, [removeNodeFromCanvas]);

  // 监听节点编辑事件
  useEffect(() => {
    const handleNodeEditEvent = (e: any) => {
      const { nodeId, nodeType } = e.detail;
      // 创建一个模拟的节点对象来打开编辑器
      const mockNode: WorkflowNode = {
        id: nodeId,
        name: getNodeLabel(nodeType),
        type: nodeType as any,
        description: '',
        config: {},
        ai_instructions: {
          purpose: '',
          action: '',
          user_message: '',
          success_handling: '',
          error_handling: ''
        },
        position: { x: 0, y: 0 },
        parent_nodes: [],
        child_nodes: [],
        outputs: []
      };

      setSelectedNode(mockNode);
      setEditorVisible(true);
    };

    document.addEventListener('node-edit', handleNodeEditEvent);

    return () => {
      document.removeEventListener('node-edit', handleNodeEditEvent);
    };
  }, [getNodeLabel]);

  // 监听连线删除事件
  useEffect(() => {
    const handleEdgeDeleteEvent = (e: any) => {
      const { edgeId } = e.detail;
      removeEdgeFromCanvas(edgeId);
    };

    document.addEventListener('edge-delete', handleEdgeDeleteEvent);

    return () => {
      document.removeEventListener('edge-delete', handleEdgeDeleteEvent);
    };
  }, [removeEdgeFromCanvas]);

  // 初始化画布
  useEffect(() => {
    initCanvas();

    // 清理函数
    return () => {
      if (butterflyRef.current) {
        // Butterfly可能没有destroy方法，使用其他清理方式
        try {
          if (typeof butterflyRef.current.destroy === 'function') {
            butterflyRef.current.destroy();
          } else {
            // 清理事件监听器
            butterflyRef.current.off?.('node:click', () => {});
            butterflyRef.current.off?.('edge:click', () => {});
            butterflyRef.current.off?.('canvas:click', () => {});
          }
        } catch (error) {
          console.warn('Failed to destroy butterfly canvas:', error);
        }
      }
    };
  }, [initCanvas]);

  // 监听工作流数据变化，避免不必要的完整重绘
  useEffect(() => {
    if (!canvasInitialized || !butterflyRef.current) {
      console.log('🎨 画布未初始化，跳过绘制');
      return;
    }

    // 只在初始化时或工作流ID变化时进行完整重绘
    const isInitialLoad = workflowData && !butterflyRef.current.getDataMap()?.nodes?.length;
    const workflowIdChanged = workflowData?.id !== lastWorkflowIdRef.current;
    
    if (isInitialLoad || workflowIdChanged) {
      console.log('🎨 初始加载或工作流切换，进行完整重绘:', workflowData?.name || 'local nodes');
      
      try {
        // 在切换工作流时，先清空画布
        if (workflowIdChanged && lastWorkflowIdRef.current && butterflyRef.current) {
          console.log('🧹 工作流切换：清空当前画布');
          try {
            // 获取所有节点和边并清除
            const dataMap = butterflyRef.current.getDataMap();
            if (dataMap && dataMap.nodes) {
              dataMap.nodes.forEach((node: any) => {
                if (butterflyRef.current) {
                  butterflyRef.current.removeNode(node.id);
                }
              });
            }
            if (dataMap && dataMap.edges) {
              dataMap.edges.forEach((edge: any) => {
                if (butterflyRef.current) {
                  butterflyRef.current.removeEdge(edge.id);
                }
              });
            }
          } catch (e) {
            console.warn('清空画布时出错，使用draw方法重绘:', e);
          }
        }
        
        if (workflowData && workflowData.nodes && workflowData.nodes.length > 0) {
          console.log('🎨 绘制工作流节点:', workflowData.nodes.length, '个节点');
          const butterflyData = convertWorkflowToButterflyData(workflowData.nodes);
          butterflyRef.current.draw(butterflyData);
          lastWorkflowIdRef.current = workflowData.id; // 记录当前工作流ID
        } else if (nodes.length > 0 && !workflowData) {
          console.log('🎨 绘制本地节点:', nodes.length, '个节点');
          const butterflyData = convertWorkflowToButterflyData(nodes);
          butterflyRef.current.draw(butterflyData);
          lastWorkflowIdRef.current = undefined;
        }
      } catch (error) {
        console.error('🎨 绘制节点失败:', error);
      }
    } else {
      console.log('🎨 跳过重绘，避免破坏现有连线和位置');
    }
  }, [workflowData?.id, canvasInitialized]); // 移除nodes.length依赖，避免每次节点变化都重绘

  // 监听外部传入的workflow prop变化 - 只在真正变化时更新
  useEffect(() => {
    if (workflow && workflow !== workflowData) {
      console.log('🔄 外部workflow变化，更新状态:', workflow.name, '节点数:', workflow.nodes?.length || 0);
      setWorkflowData(workflow);
      setNodes(workflow.nodes || []);
    } else if (!workflow && workflowData) {
      // 如果外部传入空workflow，清空所有状态
      console.log('🔄 外部workflow为空，清空所有状态');
      setWorkflowData(null);
      setNodes([]);
    }
  }, [workflow?.id, workflow?.nodes?.length]); // 只依赖关键标识符，避免无限循环

  return (
    <div className="workflow-canvas-container">
      {/* 节点调色板 */}
      {!readonly && (
        <NodePalette
          onNodeDrop={handleNodeDrop}
          loading={loading}
        />
      )}

      {/* 工具栏 */}
      {!readonly && onSave && (
        <div style={{ marginBottom: '16px', textAlign: 'right' }}>
          <button
            onClick={() => {
              // 手动创建测试连线
              console.log('🧪 手动创建测试连线');
              const testNodes = nodes.slice(); // 复制节点数组
              if (testNodes.length >= 2) {
                // 从第一个节点连接到第二个节点
                const sourceNode = testNodes[0];
                const targetNode = testNodes[1];
                const testEdge = {
                  sourceNode: sourceNode.id,
                  targetNode: targetNode.id,
                  label: '手动测试连线'
                };
                console.log('🧪 创建手动测试连线:', testEdge);
                handleEdgeConnect(testEdge);
              }
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#52c41a',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '8px'
            }}
          >
            🧪 测试连线
          </button>
          <button
            onClick={saveWorkflow}
            style={{
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            💾 保存工作流
          </button>
        </div>
      )}

      {/* 画布区域 */}
      <div
        ref={canvasRef}
        className={`workflow-canvas ${readonly ? 'readonly' : ''}`}
        style={{ 
          width: '100%', 
          height: '500px', 
          border: '1px solid #d9d9d9', 
          borderRadius: '8px',
          backgroundColor: '#ffffff',
          position: 'relative',
          overflow: 'visible'
        }}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {/* 调试信息 */}
        <div style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 1000
        }}>
          画布状态: {canvasInitialized ? '已初始化' : '未初始化'} | 节点数: {nodes.length}
        </div>
      </div>

      {/* 节点编辑器 */}
      <Drawer
        title="编辑节点"
        placement="right"
        width={500}
        open={editorVisible}
        onClose={() => setEditorVisible(false)}
        destroyOnClose
      >
        {selectedNode && (
          <NodeEditor
            node={selectedNode}
            onSave={handleSaveNode}
            onCancel={() => setEditorVisible(false)}
          />
        )}
      </Drawer>

      {/* 画布加载状态 */}
      {!canvasInitialized && (
        <div className="canvas-loading">
          <div style={{ textAlign: 'center', padding: '60px 20px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔧</div>
            <h3>正在初始化画布...</h3>
            <p>如果长时间未响应，请确保已安装butterfly-dag依赖包</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkflowCanvas; 